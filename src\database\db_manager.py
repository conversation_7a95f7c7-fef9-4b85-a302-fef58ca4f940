#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Manager for Cyber Shield Pro
Handles all database operations including user management, logs, threats, and settings
"""

import sqlite3
import os
import json
import hashlib
import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """Database manager for Cyber Shield Pro"""
    
    def __init__(self, db_path: str = "database/cyber_shield.db"):
        """Initialize database manager"""
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        
        # Ensure database directory exists
        Path(os.path.dirname(db_path)).mkdir(parents=True, exist_ok=True)
    
    def connect(self) -> bool:
        """Connect to the database"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # Enable column access by name
            self.cursor = self.connection.cursor()
            return True
        except Exception as e:
            print(f"Database connection error: {e}")
            return False
    
    def close(self):
        """Close database connection"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
    
    def initialize_database(self) -> bool:
        """Initialize database with all required tables"""
        if not self.connect():
            return False
        
        try:
            # Create users table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    salt TEXT NOT NULL,
                    is_admin BOOLEAN DEFAULT FALSE,
                    is_active BOOLEAN DEFAULT TRUE,
                    two_factor_enabled BOOLEAN DEFAULT FALSE,
                    two_factor_secret TEXT,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Create sessions table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token TEXT UNIQUE NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create threats table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS threats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    threat_type TEXT NOT NULL,
                    threat_name TEXT NOT NULL,
                    file_path TEXT,
                    file_hash TEXT,
                    severity TEXT DEFAULT 'medium',
                    status TEXT DEFAULT 'detected',
                    action_taken TEXT,
                    detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP,
                    quarantine_path TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create scans table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS scans (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    scan_type TEXT NOT NULL,
                    scan_path TEXT,
                    files_scanned INTEGER DEFAULT 0,
                    threats_found INTEGER DEFAULT 0,
                    scan_duration INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'running',
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create logs table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    log_level TEXT NOT NULL,
                    category TEXT NOT NULL,
                    message TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create network_connections table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS network_connections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    local_ip TEXT,
                    remote_ip TEXT,
                    remote_port INTEGER,
                    protocol TEXT,
                    process_name TEXT,
                    process_id INTEGER,
                    connection_state TEXT,
                    country TEXT,
                    city TEXT,
                    is_suspicious BOOLEAN DEFAULT FALSE,
                    blocked BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create settings table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT NOT NULL,
                    setting_type TEXT DEFAULT 'string',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, setting_key)
                )
            ''')
            
            # Create reports table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    report_type TEXT NOT NULL,
                    report_title TEXT NOT NULL,
                    report_data TEXT NOT NULL,
                    file_path TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Create notifications table
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    notification_type TEXT NOT NULL,
                    title TEXT NOT NULL,
                    message TEXT NOT NULL,
                    is_read BOOLEAN DEFAULT FALSE,
                    priority TEXT DEFAULT 'normal',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Commit changes
            self.connection.commit()
            
            # Create default admin user if not exists
            self._create_default_admin()
            
            return True
            
        except Exception as e:
            print(f"Database initialization error: {e}")
            self.connection.rollback()
            return False
    
    def _create_default_admin(self):
        """Create default admin user"""
        try:
            # Check if admin user exists
            self.cursor.execute("SELECT id FROM users WHERE username = 'admin'")
            if self.cursor.fetchone():
                return
            
            # Create admin user
            salt = os.urandom(32).hex()
            password_hash = hashlib.pbkdf2_hmac('sha256', 'admin123'.encode(), salt.encode(), 100000).hex()
            
            self.cursor.execute('''
                INSERT INTO users (username, email, password_hash, salt, is_admin, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ('admin', '<EMAIL>', password_hash, salt, True, True))
            
            self.connection.commit()
            
        except Exception as e:
            print(f"Error creating default admin: {e}")
    
    def execute_query(self, query: str, params: tuple = ()) -> Optional[List[sqlite3.Row]]:
        """Execute a SELECT query and return results"""
        try:
            if not self.connection:
                self.connect()
            
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
            
        except Exception as e:
            print(f"Query execution error: {e}")
            return None
    
    def execute_update(self, query: str, params: tuple = ()) -> bool:
        """Execute an INSERT, UPDATE, or DELETE query"""
        try:
            if not self.connection:
                self.connect()
            
            self.cursor.execute(query, params)
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"Update execution error: {e}")
            self.connection.rollback()
            return False
    
    def get_last_insert_id(self) -> int:
        """Get the ID of the last inserted row"""
        return self.cursor.lastrowid if self.cursor else 0
