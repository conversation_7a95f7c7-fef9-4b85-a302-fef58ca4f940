#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Theme for Cyber Shield Pro
Defines colors, styles, and visual effects for the cyber-themed interface
"""

import tkinter as tk
from tkinter import ttk
import customtkinter as ctk

class CyberTheme:
    """Cyber theme configuration and styling"""
    
    def __init__(self):
        """Initialize cyber theme"""
        # Color palette
        self.colors = {
            # Primary colors
            'bg_primary': '#0a0a0a',      # Deep black
            'bg_secondary': '#1a1a1a',    # Dark gray
            'bg_tertiary': '#2a2a2a',     # Medium gray
            
            # Accent colors
            'accent_cyan': '#00ffff',     # Bright cyan
            'accent_green': '#00ff00',    # Bright green
            'accent_blue': '#0080ff',     # Electric blue
            'accent_purple': '#8000ff',   # Electric purple
            'accent_red': '#ff0040',      # Neon red
            'accent_orange': '#ff8000',   # Neon orange
            
            # Text colors
            'text_primary': '#ffffff',    # White
            'text_secondary': '#cccccc',  # Light gray
            'text_accent': '#00ffff',     # Cyan
            'text_success': '#00ff00',    # Green
            'text_warning': '#ff8000',    # Orange
            'text_error': '#ff0040',      # Red
            
            # UI elements
            'border_normal': '#333333',   # Dark border
            'border_active': '#00ffff',   # Cyan border
            'border_hover': '#0080ff',    # Blue border
            
            # Button colors
            'btn_primary': '#00ffff',     # Cyan button
            'btn_secondary': '#333333',   # Gray button
            'btn_success': '#00ff00',     # Green button
            'btn_warning': '#ff8000',     # Orange button
            'btn_danger': '#ff0040',      # Red button
            
            # Status colors
            'status_online': '#00ff00',   # Green
            'status_offline': '#666666',  # Gray
            'status_warning': '#ff8000',  # Orange
            'status_error': '#ff0040',    # Red
            
            # Matrix effect colors
            'matrix_green': '#00ff41',    # Matrix green
            'matrix_dark': '#003300',     # Dark matrix
        }
        
        # Fonts
        self.fonts = {
            'title': ('Consolas', 24, 'bold'),
            'heading': ('Consolas', 18, 'bold'),
            'subheading': ('Consolas', 14, 'bold'),
            'body': ('Consolas', 12, 'normal'),
            'small': ('Consolas', 10, 'normal'),
            'code': ('Courier New', 11, 'normal'),
            'matrix': ('Courier New', 8, 'normal'),
        }
        
        # Sizes and spacing
        self.sizes = {
            'window_min_width': 1200,
            'window_min_height': 800,
            'sidebar_width': 250,
            'header_height': 80,
            'footer_height': 40,
            'padding_small': 5,
            'padding_medium': 10,
            'padding_large': 20,
            'border_width': 2,
            'corner_radius': 8,
        }
        
        # Animation settings
        self.animations = {
            'fade_duration': 300,
            'slide_duration': 250,
            'glow_duration': 1000,
            'matrix_speed': 50,
            'typing_speed': 30,
        }
    
    def configure_customtkinter(self):
        """Configure CustomTkinter with cyber theme"""
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Custom color theme
        ctk.ThemeManager.theme["CTk"]["fg_color"] = [self.colors['bg_primary'], self.colors['bg_primary']]
        ctk.ThemeManager.theme["CTkFrame"]["fg_color"] = [self.colors['bg_secondary'], self.colors['bg_secondary']]
        ctk.ThemeManager.theme["CTkButton"]["fg_color"] = [self.colors['accent_cyan'], self.colors['accent_cyan']]
        ctk.ThemeManager.theme["CTkButton"]["hover_color"] = [self.colors['accent_blue'], self.colors['accent_blue']]
        ctk.ThemeManager.theme["CTkButton"]["text_color"] = [self.colors['bg_primary'], self.colors['bg_primary']]
        ctk.ThemeManager.theme["CTkEntry"]["fg_color"] = [self.colors['bg_tertiary'], self.colors['bg_tertiary']]
        ctk.ThemeManager.theme["CTkEntry"]["border_color"] = [self.colors['border_normal'], self.colors['border_active']]
        ctk.ThemeManager.theme["CTkLabel"]["text_color"] = [self.colors['text_primary'], self.colors['text_primary']]
    
    def get_button_style(self, button_type: str = 'primary') -> dict:
        """Get button style configuration"""
        styles = {
            'primary': {
                'fg_color': self.colors['btn_primary'],
                'hover_color': self.colors['accent_blue'],
                'text_color': self.colors['bg_primary'],
                'font': self.fonts['body'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': 0,
            },
            'secondary': {
                'fg_color': self.colors['btn_secondary'],
                'hover_color': self.colors['bg_tertiary'],
                'text_color': self.colors['text_primary'],
                'font': self.fonts['body'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': self.sizes['border_width'],
                'border_color': self.colors['border_normal'],
            },
            'success': {
                'fg_color': self.colors['btn_success'],
                'hover_color': self.colors['accent_green'],
                'text_color': self.colors['bg_primary'],
                'font': self.fonts['body'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': 0,
            },
            'warning': {
                'fg_color': self.colors['btn_warning'],
                'hover_color': self.colors['accent_orange'],
                'text_color': self.colors['bg_primary'],
                'font': self.fonts['body'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': 0,
            },
            'danger': {
                'fg_color': self.colors['btn_danger'],
                'hover_color': self.colors['accent_red'],
                'text_color': self.colors['text_primary'],
                'font': self.fonts['body'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': 0,
            }
        }
        
        return styles.get(button_type, styles['primary'])
    
    def get_entry_style(self) -> dict:
        """Get entry field style configuration"""
        return {
            'fg_color': self.colors['bg_tertiary'],
            'border_color': self.colors['border_normal'],
            'text_color': self.colors['text_primary'],
            'placeholder_text_color': self.colors['text_secondary'],
            'font': self.fonts['body'],
            'corner_radius': self.sizes['corner_radius'],
            'border_width': self.sizes['border_width'],
        }
    
    def get_frame_style(self, frame_type: str = 'primary') -> dict:
        """Get frame style configuration"""
        styles = {
            'primary': {
                'fg_color': self.colors['bg_primary'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': 0,
            },
            'secondary': {
                'fg_color': self.colors['bg_secondary'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': self.sizes['border_width'],
                'border_color': self.colors['border_normal'],
            },
            'accent': {
                'fg_color': self.colors['bg_secondary'],
                'corner_radius': self.sizes['corner_radius'],
                'border_width': self.sizes['border_width'],
                'border_color': self.colors['accent_cyan'],
            }
        }
        
        return styles.get(frame_type, styles['primary'])
    
    def get_label_style(self, label_type: str = 'primary') -> dict:
        """Get label style configuration"""
        styles = {
            'primary': {
                'text_color': self.colors['text_primary'],
                'font': self.fonts['body'],
            },
            'secondary': {
                'text_color': self.colors['text_secondary'],
                'font': self.fonts['body'],
            },
            'accent': {
                'text_color': self.colors['text_accent'],
                'font': self.fonts['body'],
            },
            'title': {
                'text_color': self.colors['text_accent'],
                'font': self.fonts['title'],
            },
            'heading': {
                'text_color': self.colors['text_primary'],
                'font': self.fonts['heading'],
            },
            'success': {
                'text_color': self.colors['text_success'],
                'font': self.fonts['body'],
            },
            'warning': {
                'text_color': self.colors['text_warning'],
                'font': self.fonts['body'],
            },
            'error': {
                'text_color': self.colors['text_error'],
                'font': self.fonts['body'],
            }
        }
        
        return styles.get(label_type, styles['primary'])
    
    def create_glow_effect(self, widget, color: str = None):
        """Create glow effect for widgets"""
        if color is None:
            color = self.colors['accent_cyan']
        
        # This would be implemented with custom drawing or effects
        # For now, we'll use border highlighting
        try:
            widget.configure(border_color=color)
        except:
            pass
    
    def create_matrix_background(self, parent, width: int, height: int):
        """Create matrix-style background effect"""
        # This would create a canvas with falling matrix characters
        # Implementation would involve animation and random character generation
        canvas = tk.Canvas(
            parent,
            width=width,
            height=height,
            bg=self.colors['bg_primary'],
            highlightthickness=0
        )
        
        # Add matrix effect implementation here
        return canvas
    
    def get_status_color(self, status: str) -> str:
        """Get color for status indicators"""
        status_colors = {
            'online': self.colors['status_online'],
            'offline': self.colors['status_offline'],
            'protected': self.colors['status_online'],
            'at_risk': self.colors['status_error'],
            'warning': self.colors['status_warning'],
            'error': self.colors['status_error'],
            'scanning': self.colors['accent_blue'],
            'updating': self.colors['accent_purple'],
        }
        
        return status_colors.get(status.lower(), self.colors['text_secondary'])
