#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration Manager for Cyber Shield Pro
Handles application configuration and settings
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

class ConfigManager:
    """Configuration management"""
    
    def __init__(self, config_file: str = "config/app_config.json"):
        """Initialize configuration manager"""
        self.config_file = Path(config_file)
        self.config_data = {}
        self.default_config = self._get_default_config()
        
        # Ensure config directory exists
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "application": {
                "name": "Cyber Shield Pro",
                "version": "1.0.0",
                "language": "ar",
                "theme": "dark",
                "auto_start": False,
                "minimize_to_tray": True
            },
            "security": {
                "real_time_protection": True,
                "auto_scan": True,
                "scan_interval": 24
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "remember_position": True
            },
            "logging": {
                "log_level": "INFO",
                "log_to_file": True,
                "log_to_console": False
            }
        }
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
            else:
                # Create default config file
                self.config_data = self.default_config.copy()
                self.save_config()
            
            return True
            
        except Exception as e:
            print(f"Error loading config: {e}")
            self.config_data = self.default_config.copy()
            return False
    
    def save_config(self) -> bool:
        """Save configuration to file"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            return True
            
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """Set configuration value"""
        keys = key.split('.')
        config = self.config_data
        
        try:
            # Navigate to the parent of the target key
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # Set the value
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            print(f"Error setting config value: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get entire configuration section"""
        return self.config_data.get(section, {})
    
    def set_section(self, section: str, data: Dict[str, Any]) -> bool:
        """Set entire configuration section"""
        try:
            self.config_data[section] = data
            return True
        except Exception as e:
            print(f"Error setting config section: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """Reset configuration to defaults"""
        try:
            self.config_data = self.default_config.copy()
            return self.save_config()
        except Exception as e:
            print(f"Error resetting config: {e}")
            return False
    
    def backup_config(self, backup_file: Optional[str] = None) -> bool:
        """Create backup of current configuration"""
        try:
            if backup_file is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"config/backup_config_{timestamp}.json"
            
            backup_path = Path(backup_file)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=4, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"Error backing up config: {e}")
            return False
    
    def restore_config(self, backup_file: str) -> bool:
        """Restore configuration from backup"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                return False
            
            with open(backup_path, 'r', encoding='utf-8') as f:
                self.config_data = json.load(f)
            
            return self.save_config()
            
        except Exception as e:
            print(f"Error restoring config: {e}")
            return False
