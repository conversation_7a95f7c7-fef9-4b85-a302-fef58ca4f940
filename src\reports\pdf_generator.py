#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Generator for Cyber Shield Pro
Professional PDF report generation with charts and tables
"""

import os
from typing import Dict, List, Any, Optional
from datetime import datetime

class PDFGenerator:
    """Professional PDF Report Generator"""
    
    def __init__(self):
        """Initialize PDF generator"""
        # Company branding
        self.company_name = "Cyber Shield Pro"
        self.company_subtitle = "Advanced Cybersecurity Solutions"
        self.logo_path = "assets/logo.png"
    
    def generate_pdf_report(self, report_data: Dict[str, Any], output_path: str):
        """Generate PDF report from report data"""
        try:
            # For now, create a simple HTML-to-PDF conversion
            html_content = self._create_html_content(report_data)
            
            # Save as HTML first (can be converted to PDF later)
            html_path = output_path.replace('.pdf', '.html')
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            # Try to convert to PDF using weasyprint if available
            try:
                import weasyprint
                weasyprint.HTML(string=html_content).write_pdf(output_path)
                os.remove(html_path)  # Remove temporary HTML file
            except ImportError:
                # If weasyprint not available, keep HTML file
                print(f"PDF generation requires weasyprint. HTML report saved as: {html_path}")
                return html_path
            
            return output_path
            
        except Exception as e:
            raise Exception(f"Error generating PDF report: {e}")
    
    def _create_html_content(self, report_data: Dict[str, Any]) -> str:
        """Create HTML content for PDF conversion"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{report_data.get('title', 'Security Report')}</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 40px;
                    background-color: #f8f9fa;
                    color: #2c3e50;
                    line-height: 1.6;
                }}
                
                .header {{
                    text-align: center;
                    margin-bottom: 40px;
                    padding: 30px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 10px;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                
                .company-name {{
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                
                .company-subtitle {{
                    font-size: 14px;
                    opacity: 0.9;
                    margin-bottom: 20px;
                }}
                
                .report-title {{
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                
                .report-subtitle {{
                    font-size: 16px;
                    opacity: 0.9;
                }}
                
                .metadata {{
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 30px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                
                .metadata h3 {{
                    margin-top: 0;
                    color: #3498db;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 10px;
                }}
                
                .metadata-grid {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15px;
                    margin-top: 15px;
                }}
                
                .metadata-item {{
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    border-bottom: 1px solid #ecf0f1;
                }}
                
                .metadata-label {{
                    font-weight: bold;
                    color: #34495e;
                }}
                
                .section {{
                    background: white;
                    margin-bottom: 30px;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                
                .section-header {{
                    background: #3498db;
                    color: white;
                    padding: 15px 20px;
                    font-size: 18px;
                    font-weight: bold;
                }}
                
                .section-content {{
                    padding: 20px;
                }}
                
                .alert {{
                    padding: 15px;
                    margin: 15px 0;
                    border-radius: 5px;
                    border-left: 4px solid;
                }}
                
                .alert-critical {{
                    background-color: #ffebee;
                    border-left-color: #f44336;
                    color: #c62828;
                }}
                
                .alert-warning {{
                    background-color: #fff3e0;
                    border-left-color: #ff9800;
                    color: #ef6c00;
                }}
                
                .alert-info {{
                    background-color: #e3f2fd;
                    border-left-color: #2196f3;
                    color: #1565c0;
                }}
                
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 15px 0;
                    background: white;
                }}
                
                th, td {{
                    padding: 12px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                
                th {{
                    background-color: #f8f9fa;
                    font-weight: bold;
                    color: #2c3e50;
                }}
                
                tr:hover {{
                    background-color: #f5f5f5;
                }}
                
                .recommendations {{
                    background: #e8f5e8;
                    border: 1px solid #4caf50;
                    border-radius: 5px;
                    padding: 15px;
                    margin: 15px 0;
                }}
                
                .recommendations h4 {{
                    color: #2e7d32;
                    margin-top: 0;
                }}
                
                .recommendations ul {{
                    margin: 10px 0;
                    padding-left: 20px;
                }}
                
                .recommendations li {{
                    margin: 5px 0;
                    color: #2e7d32;
                }}
                
                .chart-container {{
                    text-align: center;
                    margin: 20px 0;
                    padding: 15px;
                    background: #f8f9fa;
                    border-radius: 5px;
                }}
                
                .footer {{
                    margin-top: 50px;
                    padding: 30px;
                    background: #2c3e50;
                    color: white;
                    text-align: center;
                    border-radius: 10px;
                }}
                
                .page-break {{
                    page-break-before: always;
                }}
                
                @media print {{
                    body {{
                        background-color: white;
                    }}
                    
                    .section {{
                        box-shadow: none;
                        border: 1px solid #ddd;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <div class="company-name">{self.company_name}</div>
                <div class="company-subtitle">{self.company_subtitle}</div>
                <div class="report-title">{report_data.get('title', 'Security Report')}</div>
                <div class="report-subtitle">{report_data.get('subtitle', '')}</div>
                <div style="margin-top: 15px; font-size: 14px;">
                    Generated: {report_data.get('generated_at', datetime.now()).strftime('%Y-%m-%d %H:%M:%S')}
                </div>
            </div>
        """
        
        # Add metadata section
        if 'metadata' in report_data:
            html += self._create_metadata_section(report_data['metadata'])
        
        # Add report sections
        for section in report_data.get('sections', []):
            html += self._create_section_html(section)
        
        # Add footer
        html += f"""
            <div class="footer">
                <h3>Report Summary</h3>
                <p>This report was automatically generated by {self.company_name} security monitoring system.</p>
                <p>For questions or concerns about this report, please contact your system administrator.</p>
                <p>© 2025 {self.company_name}. All rights reserved.</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _create_metadata_section(self, metadata: Dict[str, Any]) -> str:
        """Create metadata section HTML"""
        html = """
        <div class="metadata">
            <h3>📊 Report Information</h3>
            <div class="metadata-grid">
        """
        
        for key, value in metadata.items():
            if key not in ['date_range']:  # Skip complex objects
                formatted_key = key.replace('_', ' ').title()
                formatted_value = str(value)
                html += f"""
                <div class="metadata-item">
                    <span class="metadata-label">{formatted_key}:</span>
                    <span>{formatted_value}</span>
                </div>
                """
        
        html += """
            </div>
        </div>
        """
        
        return html
    
    def _create_section_html(self, section) -> str:
        """Create section HTML"""
        html = f"""
        <div class="section">
            <div class="section-header">
                {getattr(section, 'title', 'Section')}
            </div>
            <div class="section-content">
        """
        
        # Add content
        if hasattr(section, 'content') and section.content:
            # Determine alert class based on severity
            alert_class = ""
            if hasattr(section, 'severity'):
                if section.severity == 'critical':
                    alert_class = "alert alert-critical"
                elif section.severity == 'warning':
                    alert_class = "alert alert-warning"
                elif section.severity in ['info', 'error']:
                    alert_class = "alert alert-info"
            
            if alert_class:
                html += f'<div class="{alert_class}">{section.content}</div>'
            else:
                html += f'<div>{section.content}</div>'
        
        # Add charts
        if hasattr(section, 'charts') and section.charts:
            for chart in section.charts:
                html += f"""
                <div class="chart-container">
                    <h4>{chart.get('title', 'Chart')}</h4>
                    <p><em>Chart visualization would appear here in full PDF version</em></p>
                </div>
                """
        
        # Add tables
        if hasattr(section, 'tables') and section.tables:
            for table in section.tables:
                html += self._create_table_html(table)
        
        # Add recommendations
        if hasattr(section, 'recommendations') and section.recommendations:
            html += """
            <div class="recommendations">
                <h4>💡 Recommendations</h4>
                <ul>
            """
            for rec in section.recommendations:
                html += f"<li>{rec}</li>"
            html += """
                </ul>
            </div>
            """
        
        html += """
            </div>
        </div>
        """
        
        return html
    
    def _create_table_html(self, table_data: Dict[str, Any]) -> str:
        """Create table HTML"""
        html = "<table>"
        
        # Add headers
        if 'headers' in table_data:
            html += "<thead><tr>"
            for header in table_data['headers']:
                html += f"<th>{header}</th>"
            html += "</tr></thead>"
        
        # Add rows
        if 'rows' in table_data:
            html += "<tbody>"
            for row in table_data['rows']:
                html += "<tr>"
                for cell in row:
                    html += f"<td>{cell}</td>"
                html += "</tr>"
            html += "</tbody>"
        
        html += "</table>"
        
        return html
