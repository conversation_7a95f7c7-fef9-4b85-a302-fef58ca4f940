#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Runner for Cyber Shield Pro Advanced
Runs the system with basic functionality
"""

import sys
import os
import asyncio
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class SimpleCyberShield:
    """Simplified Cyber Shield Pro"""
    
    def __init__(self):
        """Initialize simple cyber shield"""
        self.name = "Cyber Shield Pro Advanced"
        self.version = "1.0.0"
        self.is_running = False
        self.start_time = datetime.now()
        
        # Components
        self.logger = None
        self.config = None
        self.database = None
        self.threat_manager = None
        self.notification_manager = None
        self.dashboard = None
        
        # Statistics
        self.stats = {
            'threats_detected': 0,
            'files_scanned': 0,
            'connections_monitored': 0,
            'alerts_sent': 0
        }
    
    async def initialize(self):
        """Initialize the system"""
        try:
            print(f"🛡️  Starting {self.name} v{self.version}")
            print("=" * 50)
            
            # Create directories
            self._create_directories()
            
            # Initialize core components
            await self._init_core_components()
            
            # Initialize security components
            await self._init_security_components()
            
            # Start dashboard
            await self._start_dashboard()
            
            print("✅ System initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def _create_directories(self):
        """Create necessary directories"""
        directories = ['logs', 'data', 'temp', 'reports', 'quarantine']
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        print("📁 Directories created")
    
    async def _init_core_components(self):
        """Initialize core components"""
        from src.utils.logger import Logger
        from src.utils.config_manager import ConfigManager
        from src.database.database_manager import DatabaseManager
        from src.database.threat_manager import ThreatManager
        from src.notifications.notification_manager import NotificationManager
        
        # Logger
        self.logger = Logger()
        self.logger.info(f"Starting {self.name} v{self.version}")
        print("📝 Logger initialized")
        
        # Configuration
        self.config = ConfigManager()
        print("⚙️  Configuration manager initialized")
        
        # Database
        self.database = DatabaseManager('data/cybershield.db', self.logger)
        await self.database.initialize()
        print("🗄️  Database initialized")
        
        # Threat manager
        self.threat_manager = ThreatManager(self.database, self.logger)
        print("🔒 Threat manager initialized")
        
        # Notification manager
        self.notification_manager = NotificationManager(self.logger)
        self.notification_manager.start_notifications()
        print("🔔 Notification manager initialized")
    
    async def _init_security_components(self):
        """Initialize security components"""
        from src.security.threat_detector import ThreatDetector
        from src.security.malware_scanner import MalwareScanner
        from src.security.firewall_manager import FirewallManager
        from src.database.log_manager import LogManager
        
        # Log manager
        log_manager = LogManager(self.database, self.logger)
        
        # Threat detector
        threat_detector = ThreatDetector(self.threat_manager, log_manager, self.logger)
        threat_detector.start_monitoring()
        print("🔍 Threat detector started")
        
        # Malware scanner
        malware_scanner = MalwareScanner(self.threat_manager, self.logger)
        malware_scanner.start_monitoring()
        print("🦠 Malware scanner started")
        
        # Firewall
        firewall = FirewallManager(self.logger)
        firewall.initialize_firewall()
        print("🔥 Firewall initialized")
    
    async def _start_dashboard(self):
        """Start web dashboard"""
        try:
            from src.dashboard.admin_dashboard import AdminDashboard
            
            self.dashboard = AdminDashboard(self.logger, port=8080)
            
            # Update initial data
            self.dashboard.update_data({
                'system_status': 'running',
                'threats_detected': 0,
                'active_connections': 0,
                'cpu_usage': 15.5,
                'memory_usage': 35.2,
                'start_time': self.start_time.timestamp() * 1000
            })
            
            # Try to start dashboard
            if self.dashboard.start_dashboard():
                print("🌐 Dashboard started at http://localhost:8080")
            else:
                print("⚠️  Dashboard could not be started (Flask may not be available)")
                
        except Exception as e:
            print(f"⚠️  Dashboard initialization failed: {e}")
    
    async def run(self):
        """Run the main application loop"""
        try:
            self.is_running = True
            print("\n🚀 Cyber Shield Pro Advanced is now running!")
            print("=" * 50)
            print("🔒 Real-time protection: ACTIVE")
            print("🔍 Threat monitoring: ACTIVE")
            print("🔥 Firewall: ACTIVE")
            print("🌐 Dashboard: http://localhost:8080")
            print("=" * 50)
            print("\nPress Ctrl+C to stop")
            
            # Main monitoring loop
            while self.is_running:
                await self._monitoring_cycle()
                await asyncio.sleep(30)  # Check every 30 seconds
                
        except KeyboardInterrupt:
            print("\n⚠️  Shutdown requested by user")
            await self.shutdown()
        except Exception as e:
            print(f"\n💥 Runtime error: {e}")
            await self.shutdown()
    
    async def _monitoring_cycle(self):
        """Perform one monitoring cycle"""
        try:
            current_time = datetime.now()
            
            # Simulate some activity
            self.stats['connections_monitored'] += 5
            
            # Occasionally simulate a threat
            if time.time() % 120 < 1:  # Every 2 minutes
                await self._simulate_threat_detection()
            
            # Update dashboard
            if self.dashboard:
                uptime_minutes = (current_time - self.start_time).total_seconds() / 60
                
                self.dashboard.update_data({
                    'threats_detected': self.stats['threats_detected'],
                    'active_connections': self.stats['connections_monitored'],
                    'files_scanned': self.stats['files_scanned'],
                    'processes_monitored': 25,
                    'cpu_usage': 15 + (time.time() % 10),  # Simulate varying CPU
                    'memory_usage': 35 + (time.time() % 15),  # Simulate varying memory
                    'uptime_minutes': int(uptime_minutes)
                })
            
            # Log activity
            if int(time.time()) % 300 == 0:  # Every 5 minutes
                self.logger.info(f"System status: {self.stats}")
                
        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
    
    async def _simulate_threat_detection(self):
        """Simulate threat detection"""
        try:
            threat_types = ['suspicious_file', 'network_anomaly', 'process_anomaly', 'malware_signature']
            severities = ['low', 'medium', 'high']
            
            import random
            threat_type = random.choice(threat_types)
            severity = random.choice(severities)
            
            # Log threat
            await self.threat_manager.log_threat(
                threat_type=threat_type,
                severity=severity,
                source='monitoring_system',
                description=f"Simulated {threat_type} detected",
                details=f"Automated detection at {datetime.now()}"
            )
            
            # Send notification
            await self.notification_manager.send_threat_alert(
                threat_type=threat_type,
                severity=severity,
                details=f"Threat detected by monitoring system"
            )
            
            self.stats['threats_detected'] += 1
            self.stats['alerts_sent'] += 1
            
            print(f"🚨 Threat detected: {threat_type} ({severity})")
            
        except Exception as e:
            self.logger.error(f"Error simulating threat: {e}")
    
    async def shutdown(self):
        """Shutdown the system"""
        try:
            print("\n🛑 Shutting down Cyber Shield Pro Advanced...")
            
            self.is_running = False
            
            # Stop notifications
            if self.notification_manager:
                self.notification_manager.stop_notifications()
            
            # Close database
            if self.database:
                await self.database.close()
            
            # Log shutdown
            if self.logger:
                self.logger.info("Cyber Shield Pro Advanced shutdown complete")
            
            print("✅ Shutdown complete")
            
        except Exception as e:
            print(f"❌ Error during shutdown: {e}")

async def main():
    """Main entry point"""
    try:
        # Create and initialize system
        cyber_shield = SimpleCyberShield()
        
        if await cyber_shield.initialize():
            await cyber_shield.run()
        else:
            print("❌ Failed to initialize system")
            return False
        
        return True
        
    except Exception as e:
        print(f"💥 Fatal error: {e}")
        return False

if __name__ == "__main__":
    try:
        print("🛡️  Cyber Shield Pro Advanced")
        print("   Advanced Cybersecurity Protection Suite")
        print("   © 2025 - All rights reserved")
        print()
        
        result = asyncio.run(main())
        
        if result:
            print("\n👋 Thank you for using Cyber Shield Pro Advanced!")
        else:
            print("\n❌ System failed to start properly")
            
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Critical error: {e}")
        sys.exit(1)
