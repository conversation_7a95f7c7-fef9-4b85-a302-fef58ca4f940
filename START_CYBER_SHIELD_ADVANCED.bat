@echo off
REM Cyber Shield Pro Advanced - Ultimate Quick Start
REM This script does everything needed to get you running

title Cyber Shield Pro Advanced - Ultimate Launcher

echo.
echo ================================================================
echo                🛡️  Cyber Shield Pro Advanced 🛡️
echo                    Ultimate Quick Start Launcher
echo ================================================================
echo.

REM Check if Python is installed
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed
    echo.
    echo 📥 Opening Python download page...
    start https://www.python.org/downloads/
    echo.
    echo Please install Python 3.8 or higher and run this script again
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

REM Check if we're running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  Not running as Administrator
    echo For best performance and full functionality, please:
    echo 1. Right-click this file
    echo 2. Select "Run as administrator"
    echo.
    echo Do you want to continue anyway? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" (
        echo Exiting...
        pause
        exit /b 1
    )
) else (
    echo ✅ Running with Administrator privileges
)

echo.
echo 🔧 Setting up project structure...

REM Run project setup
python setup_project.py
if errorlevel 1 (
    echo ❌ Project setup failed
    pause
    exit /b 1
)

echo.
echo 📦 Installing/updating dependencies...

REM Upgrade pip first
python -m pip install --upgrade pip

REM Install dependencies
pip install -r requirements_advanced.txt
if errorlevel 1 (
    echo ⚠️  Some dependencies failed to install
    echo Trying to install essential dependencies only...
    pip install aiohttp aiosqlite psutil flask fastapi uvicorn
    if errorlevel 1 (
        echo ❌ Failed to install essential dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo ✅ Dependencies installed successfully

echo.
echo 🧪 Running system tests...

REM Run tests
python test_cyber_shield_advanced.py
if errorlevel 1 (
    echo ⚠️  Some tests failed, but continuing anyway...
    echo The application might work with limited functionality
    timeout /t 3 >nul
)

echo.
echo 🚀 Starting Cyber Shield Pro Advanced...
echo.
echo ================================================================
echo                        IMPORTANT NOTES
echo ================================================================
echo.
echo 📊 Dashboard will be available at: http://localhost:8080
echo 👤 Default login credentials:
echo    Username: admin
echo    Password: CyberShield2025!
echo.
echo 🔒 SECURITY REMINDER: Change the default password immediately!
echo.
echo 🛑 To stop the application, press Ctrl+C in the application window
echo.
echo ⚠️  First run may take longer as the system initializes
echo ================================================================
echo.

REM Start the application
python cyber_shield_advanced.py

echo.
echo 🛑 Cyber Shield Pro Advanced has stopped
echo.
echo Thank you for using Cyber Shield Pro Advanced!
echo For support, check the documentation or visit our GitHub page.
echo.
pause
