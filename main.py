#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Shield Pro - Advanced Cybersecurity Protection Suite
Developed in Palestine 🇵🇸
Copyright © 2025 - All rights reserved.

Main application entry point
"""

import sys
import os
import threading
import time
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import application modules
from src.ui.main_window import CyberShieldApp
from src.utils.logger import Logger
from src.utils.config_manager import ConfigManager
from src.database.db_manager import DatabaseManager

class CyberShieldPro:
    """Main application class for Cyber Shield Pro"""
    
    def __init__(self):
        """Initialize the application"""
        self.app_name = "Cyber Shield Pro"
        self.version = "1.0.0"
        self.copyright = "© 2025 - Developed in Palestine 🇵🇸"
        
        # Initialize components
        self.logger = Logger()
        self.config_manager = ConfigManager()
        self.db_manager = DatabaseManager()
        self.main_app = None
        
        # Setup application
        self._setup_application()
    
    def _setup_application(self):
        """Setup application environment"""
        try:
            # Create necessary directories
            self._create_directories()
            
            # Initialize database
            self.db_manager.initialize_database()
            
            # Load configuration
            self.config_manager.load_config()
            
            # Setup logging
            self.logger.info(f"Starting {self.app_name} v{self.version}")
            self.logger.info(f"Copyright {self.copyright}")
            
        except Exception as e:
            self.logger.error(f"Failed to setup application: {str(e)}")
            sys.exit(1)
    
    def _create_directories(self):
        """Create necessary application directories"""
        directories = [
            'logs',
            'temp',
            'database',
            'config',
            'assets/images',
            'assets/icons',
            'assets/sounds'
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def run(self):
        """Run the main application"""
        try:
            # Initialize main application window
            self.main_app = CyberShieldApp(
                app_name=self.app_name,
                version=self.version,
                copyright=self.copyright,
                logger=self.logger,
                config_manager=self.config_manager,
                db_manager=self.db_manager
            )
            
            # Start the application
            self.main_app.run()
            
        except Exception as e:
            self.logger.error(f"Application error: {str(e)}")
            sys.exit(1)
    
    def shutdown(self):
        """Graceful application shutdown"""
        try:
            self.logger.info("Shutting down Cyber Shield Pro...")
            
            # Close database connections
            if self.db_manager:
                self.db_manager.close()
            
            # Save configuration
            if self.config_manager:
                self.config_manager.save_config()
            
            self.logger.info("Application shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {str(e)}")

def main():
    """Main entry point"""
    try:
        # Create and run application
        app = CyberShieldPro()
        app.run()
        
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
