#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Geo Locator for Cyber Shield Pro
Geographic location services for IP addresses
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional, Tuple
import folium
from folium import plugins
import os
from ..utils.logger import Logger

class GeoLocator:
    """Geographic location services"""
    
    def __init__(self, logger: Logger = None):
        """Initialize geo locator"""
        self.logger = logger
        
        # Cache for location data
        self.location_cache = {}
        self.cache_duration = 3600  # 1 hour
        
        # API endpoints
        self.apis = [
            'http://ip-api.com/json/',
            'https://ipapi.co/',
            'https://freegeoip.app/json/'
        ]
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1  # 1 second between requests
    
    def get_location(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Get geographic location for IP address"""
        try:
            # Check cache first
            cached_location = self._get_cached_location(ip_address)
            if cached_location:
                return cached_location
            
            # Rate limiting
            self._enforce_rate_limit()
            
            # Try different APIs
            for api_url in self.apis:
                try:
                    location_data = self._query_api(api_url, ip_address)
                    if location_data:
                        # Cache the result
                        self._cache_location(ip_address, location_data)
                        return location_data
                except Exception as e:
                    if self.logger:
                        self.logger.warning(f"API {api_url} failed: {e}")
                    continue
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting location for {ip_address}: {e}")
            return None
    
    def _query_api(self, api_url: str, ip_address: str) -> Optional[Dict[str, Any]]:
        """Query specific API for location data"""
        try:
            if 'ip-api.com' in api_url:
                return self._query_ip_api(ip_address)
            elif 'ipapi.co' in api_url:
                return self._query_ipapi_co(ip_address)
            elif 'freegeoip.app' in api_url:
                return self._query_freegeoip(ip_address)
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error querying API {api_url}: {e}")
            return None
    
    def _query_ip_api(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Query ip-api.com"""
        try:
            url = f"http://ip-api.com/json/{ip_address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status') == 'success':
                    return {
                        'ip': ip_address,
                        'country': data.get('country'),
                        'country_code': data.get('countryCode'),
                        'region': data.get('regionName'),
                        'city': data.get('city'),
                        'latitude': data.get('lat'),
                        'longitude': data.get('lon'),
                        'timezone': data.get('timezone'),
                        'isp': data.get('isp'),
                        'organization': data.get('org'),
                        'as_number': data.get('as'),
                        'zip_code': data.get('zip'),
                        'source': 'ip-api.com'
                    }
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error with ip-api.com: {e}")
            return None
    
    def _query_ipapi_co(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Query ipapi.co"""
        try:
            url = f"https://ipapi.co/{ip_address}/json/"
            headers = {'User-Agent': 'CyberShieldPro/1.0'}
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'error' not in data:
                    return {
                        'ip': ip_address,
                        'country': data.get('country_name'),
                        'country_code': data.get('country_code'),
                        'region': data.get('region'),
                        'city': data.get('city'),
                        'latitude': data.get('latitude'),
                        'longitude': data.get('longitude'),
                        'timezone': data.get('timezone'),
                        'isp': data.get('org'),
                        'organization': data.get('org'),
                        'as_number': data.get('asn'),
                        'zip_code': data.get('postal'),
                        'source': 'ipapi.co'
                    }
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error with ipapi.co: {e}")
            return None
    
    def _query_freegeoip(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Query freegeoip.app"""
        try:
            url = f"https://freegeoip.app/json/{ip_address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                return {
                    'ip': ip_address,
                    'country': data.get('country_name'),
                    'country_code': data.get('country_code'),
                    'region': data.get('region_name'),
                    'city': data.get('city'),
                    'latitude': data.get('latitude'),
                    'longitude': data.get('longitude'),
                    'timezone': data.get('time_zone'),
                    'zip_code': data.get('zip_code'),
                    'source': 'freegeoip.app'
                }
            
            return None
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error with freegeoip.app: {e}")
            return None
    
    def _get_cached_location(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Get cached location data"""
        if ip_address in self.location_cache:
            cache_entry = self.location_cache[ip_address]
            if time.time() - cache_entry['timestamp'] < self.cache_duration:
                return cache_entry['data']
            else:
                del self.location_cache[ip_address]
        return None
    
    def _cache_location(self, ip_address: str, location_data: Dict[str, Any]):
        """Cache location data"""
        self.location_cache[ip_address] = {
            'timestamp': time.time(),
            'data': location_data
        }
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting between API calls"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            time.sleep(self.min_request_interval - time_since_last)
        
        self.last_request_time = time.time()
    
    def create_map(self, ip_locations: List[Dict[str, Any]], output_file: str = "network_map.html") -> str:
        """Create interactive map with IP locations"""
        try:
            # Create base map
            center_lat = 20.0
            center_lon = 0.0
            
            # Calculate center if we have locations
            valid_locations = [loc for loc in ip_locations 
                             if loc.get('latitude') and loc.get('longitude')]
            
            if valid_locations:
                center_lat = sum(loc['latitude'] for loc in valid_locations) / len(valid_locations)
                center_lon = sum(loc['longitude'] for loc in valid_locations) / len(valid_locations)
            
            # Create map
            m = folium.Map(
                location=[center_lat, center_lon],
                zoom_start=2,
                tiles='OpenStreetMap'
            )
            
            # Add markers for each IP location
            for location in valid_locations:
                lat = location['latitude']
                lon = location['longitude']
                ip = location['ip']
                country = location.get('country', 'Unknown')
                city = location.get('city', 'Unknown')
                isp = location.get('isp', 'Unknown')
                
                # Determine marker color based on threat level
                threat_level = location.get('threat_level', 'low')
                if threat_level == 'high' or location.get('is_malicious'):
                    color = 'red'
                    icon = 'exclamation-sign'
                elif threat_level == 'medium':
                    color = 'orange'
                    icon = 'warning-sign'
                else:
                    color = 'green'
                    icon = 'info-sign'
                
                # Create popup content
                popup_content = f"""
                <div style="width: 200px;">
                    <h4>{ip}</h4>
                    <p><strong>Location:</strong> {city}, {country}</p>
                    <p><strong>ISP:</strong> {isp}</p>
                    <p><strong>Coordinates:</strong> {lat:.4f}, {lon:.4f}</p>
                    <p><strong>Threat Level:</strong> {threat_level}</p>
                </div>
                """
                
                # Add marker
                folium.Marker(
                    location=[lat, lon],
                    popup=folium.Popup(popup_content, max_width=250),
                    tooltip=f"{ip} ({country})",
                    icon=folium.Icon(color=color, icon=icon)
                ).add_to(m)
            
            # Add heat map layer for connection density
            if len(valid_locations) > 1:
                heat_data = [[loc['latitude'], loc['longitude']] for loc in valid_locations]
                plugins.HeatMap(heat_data, radius=15, blur=10, gradient={
                    0.2: 'blue',
                    0.4: 'lime',
                    0.6: 'orange',
                    1.0: 'red'
                }).add_to(m)
            
            # Add layer control
            folium.LayerControl().add_to(m)
            
            # Save map
            output_path = os.path.join("temp", output_file)
            os.makedirs("temp", exist_ok=True)
            m.save(output_path)
            
            return output_path
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error creating map: {e}")
            return None
    
    def get_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates in kilometers"""
        try:
            from math import radians, sin, cos, sqrt, atan2
            
            # Convert to radians
            lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
            
            # Haversine formula
            dlat = lat2 - lat1
            dlon = lon2 - lon1
            a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
            c = 2 * atan2(sqrt(a), sqrt(1-a))
            
            # Earth's radius in kilometers
            R = 6371
            
            return R * c
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error calculating distance: {e}")
            return 0.0
    
    def get_country_info(self, country_code: str) -> Optional[Dict[str, Any]]:
        """Get additional country information"""
        try:
            # This would query a country information API
            # For now, return basic info
            country_info = {
                'US': {'name': 'United States', 'continent': 'North America'},
                'CN': {'name': 'China', 'continent': 'Asia'},
                'RU': {'name': 'Russia', 'continent': 'Europe/Asia'},
                'DE': {'name': 'Germany', 'continent': 'Europe'},
                'GB': {'name': 'United Kingdom', 'continent': 'Europe'},
                'FR': {'name': 'France', 'continent': 'Europe'},
                'JP': {'name': 'Japan', 'continent': 'Asia'},
                'BR': {'name': 'Brazil', 'continent': 'South America'},
                'IN': {'name': 'India', 'continent': 'Asia'},
                'CA': {'name': 'Canada', 'continent': 'North America'},
            }
            
            return country_info.get(country_code.upper())
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting country info: {e}")
            return None
    
    def clear_cache(self):
        """Clear location cache"""
        self.location_cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cached_locations': len(self.location_cache),
            'cache_duration': self.cache_duration,
            'oldest_entry': min([entry['timestamp'] for entry in self.location_cache.values()]) if self.location_cache else None,
            'newest_entry': max([entry['timestamp'] for entry in self.location_cache.values()]) if self.location_cache else None
        }
