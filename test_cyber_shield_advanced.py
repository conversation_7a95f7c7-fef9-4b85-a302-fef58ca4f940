#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Shield Pro Advanced - Test Script
Test all components to ensure they work correctly
"""

import sys
import os
import asyncio
import traceback
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def print_header():
    """Print test header"""
    print("=" * 60)
    print("🛡️  Cyber Shield Pro Advanced - Component Test")
    print("=" * 60)
    print()

def print_test_result(test_name, success, error=None):
    """Print test result"""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{test_name:40} : {status}")
    if error:
        print(f"    Error: {error}")

async def test_core_imports():
    """Test core module imports"""
    print("🔍 Testing Core Module Imports...")
    tests = []
    
    # Test logger
    try:
        from src.utils.logger import Logger
        logger = Logger()
        tests.append(("Logger", True, None))
    except Exception as e:
        tests.append(("Logger", False, str(e)))
    
    # Test config manager
    try:
        from src.utils.config_manager import ConfigManager
        config = ConfigManager()
        tests.append(("Config Manager", True, None))
    except Exception as e:
        tests.append(("Config Manager", False, str(e)))
    
    # Test database manager
    try:
        from src.database.database_manager import DatabaseManager
        tests.append(("Database Manager", True, None))
    except Exception as e:
        tests.append(("Database Manager", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_security_modules():
    """Test security module imports"""
    print("\n🔒 Testing Security Module Imports...")
    tests = []
    
    # Test threat detector
    try:
        from src.security.threat_detector import ThreatDetector
        tests.append(("Threat Detector", True, None))
    except Exception as e:
        tests.append(("Threat Detector", False, str(e)))
    
    # Test malware scanner
    try:
        from src.security.malware_scanner import MalwareScanner
        tests.append(("Malware Scanner", True, None))
    except Exception as e:
        tests.append(("Malware Scanner", False, str(e)))
    
    # Test firewall manager
    try:
        from src.security.firewall_manager import FirewallManager
        tests.append(("Firewall Manager", True, None))
    except Exception as e:
        tests.append(("Firewall Manager", False, str(e)))
    
    # Test intrusion detector
    try:
        from src.security.intrusion_detector import IntrusionDetector
        tests.append(("Intrusion Detector", True, None))
    except Exception as e:
        tests.append(("Intrusion Detector", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_network_modules():
    """Test network module imports"""
    print("\n🌐 Testing Network Module Imports...")
    tests = []
    
    # Test network monitor
    try:
        from src.network.network_monitor import NetworkMonitor
        tests.append(("Network Monitor", True, None))
    except Exception as e:
        tests.append(("Network Monitor", False, str(e)))
    
    # Test packet analyzer
    try:
        from src.network.packet_analyzer import PacketAnalyzer
        tests.append(("Packet Analyzer", True, None))
    except Exception as e:
        tests.append(("Packet Analyzer", False, str(e)))
    
    # Test connection tracker
    try:
        from src.network.connection_tracker import ConnectionTracker
        tests.append(("Connection Tracker", True, None))
    except Exception as e:
        tests.append(("Connection Tracker", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_system_modules():
    """Test system module imports"""
    print("\n🖥️  Testing System Module Imports...")
    tests = []
    
    # Test process monitor
    try:
        from src.system.process_monitor import ProcessMonitor
        tests.append(("Process Monitor", True, None))
    except Exception as e:
        tests.append(("Process Monitor", False, str(e)))
    
    # Test performance monitor
    try:
        from src.system.performance_monitor import PerformanceMonitor
        tests.append(("Performance Monitor", True, None))
    except Exception as e:
        tests.append(("Performance Monitor", False, str(e)))
    
    # Test service manager
    try:
        from src.system.service_manager import ServiceManager
        tests.append(("Service Manager", True, None))
    except Exception as e:
        tests.append(("Service Manager", False, str(e)))
    
    # Test registry manager
    try:
        from src.system.registry_manager import RegistryManager
        tests.append(("Registry Manager", True, None))
    except Exception as e:
        tests.append(("Registry Manager", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_dashboard_modules():
    """Test dashboard module imports"""
    print("\n📊 Testing Dashboard Module Imports...")
    tests = []
    
    # Test admin dashboard
    try:
        from src.dashboard.admin_dashboard import AdminDashboard
        tests.append(("Admin Dashboard", True, None))
    except Exception as e:
        tests.append(("Admin Dashboard", False, str(e)))
    
    # Test notification manager
    try:
        from src.notifications.notification_manager import NotificationManager
        tests.append(("Notification Manager", True, None))
    except Exception as e:
        tests.append(("Notification Manager", False, str(e)))
    
    # Test report generator
    try:
        from src.reports.report_generator import ReportGenerator
        tests.append(("Report Generator", True, None))
    except Exception as e:
        tests.append(("Report Generator", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_dependencies():
    """Test external dependencies"""
    print("\n📦 Testing External Dependencies...")
    tests = []
    
    # Test asyncio
    try:
        import asyncio
        tests.append(("asyncio", True, None))
    except Exception as e:
        tests.append(("asyncio", False, str(e)))
    
    # Test aiohttp
    try:
        import aiohttp
        tests.append(("aiohttp", True, None))
    except Exception as e:
        tests.append(("aiohttp", False, str(e)))
    
    # Test aiosqlite
    try:
        import aiosqlite
        tests.append(("aiosqlite", True, None))
    except Exception as e:
        tests.append(("aiosqlite", False, str(e)))
    
    # Test psutil
    try:
        import psutil
        tests.append(("psutil", True, None))
    except Exception as e:
        tests.append(("psutil", False, str(e)))
    
    # Test flask
    try:
        import flask
        tests.append(("flask", True, None))
    except Exception as e:
        tests.append(("flask", False, str(e)))
    
    # Test fastapi
    try:
        import fastapi
        tests.append(("fastapi", True, None))
    except Exception as e:
        tests.append(("fastapi", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_basic_functionality():
    """Test basic functionality"""
    print("\n⚙️  Testing Basic Functionality...")
    tests = []
    
    # Test logger functionality
    try:
        from src.utils.logger import Logger
        logger = Logger()
        logger.info("Test log message")
        tests.append(("Logger Functionality", True, None))
    except Exception as e:
        tests.append(("Logger Functionality", False, str(e)))
    
    # Test config manager functionality
    try:
        from src.utils.config_manager import ConfigManager
        config = ConfigManager()
        config.set('test.key', 'test_value')
        value = config.get('test.key')
        assert value == 'test_value'
        tests.append(("Config Manager Functionality", True, None))
    except Exception as e:
        tests.append(("Config Manager Functionality", False, str(e)))
    
    # Test database creation
    try:
        from src.database.database_manager import DatabaseManager
        from src.utils.logger import Logger
        logger = Logger()
        db = DatabaseManager('test_db.sqlite', logger)
        await db.initialize()
        await db.close()
        if os.path.exists('test_db.sqlite'):
            os.remove('test_db.sqlite')
        tests.append(("Database Creation", True, None))
    except Exception as e:
        tests.append(("Database Creation", False, str(e)))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_directory_structure():
    """Test directory structure"""
    print("\n📁 Testing Directory Structure...")
    tests = []
    
    required_dirs = [
        'src',
        'src/utils',
        'src/database',
        'src/security',
        'src/network',
        'src/system',
        'src/dashboard',
        'src/notifications',
        'src/reports'
    ]
    
    for directory in required_dirs:
        exists = os.path.exists(directory)
        tests.append((f"Directory: {directory}", exists, None if exists else "Directory not found"))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def test_configuration_files():
    """Test configuration files"""
    print("\n⚙️  Testing Configuration Files...")
    tests = []
    
    # Test main config file
    config_exists = os.path.exists('config/cybershield_advanced.yaml')
    tests.append(("Main Config File", config_exists, None if config_exists else "Config file not found"))
    
    # Test requirements file
    req_exists = os.path.exists('requirements_advanced.txt')
    tests.append(("Requirements File", req_exists, None if req_exists else "Requirements file not found"))
    
    # Test README file
    readme_exists = os.path.exists('README_ADVANCED.md')
    tests.append(("README File", readme_exists, None if readme_exists else "README file not found"))
    
    for test_name, success, error in tests:
        print_test_result(test_name, success, error)
    
    return all(test[1] for test in tests)

async def main():
    """Main test function"""
    print_header()
    
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    os.makedirs('config', exist_ok=True)
    
    # Run all tests
    test_results = []
    
    try:
        test_results.append(await test_directory_structure())
        test_results.append(await test_configuration_files())
        test_results.append(await test_dependencies())
        test_results.append(await test_core_imports())
        test_results.append(await test_security_modules())
        test_results.append(await test_network_modules())
        test_results.append(await test_system_modules())
        test_results.append(await test_dashboard_modules())
        test_results.append(await test_basic_functionality())
        
    except Exception as e:
        print(f"\n❌ Critical error during testing: {e}")
        traceback.print_exc()
        return False
    
    # Print summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(test_results)
    failed_tests = total_tests - passed_tests
    
    print(f"Total Test Suites: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {failed_tests}")
    
    if all(test_results):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Cyber Shield Pro Advanced is ready to run!")
        print("\n🚀 To start the application:")
        print("   Windows: run_cyber_shield_advanced.bat")
        print("   Linux/macOS: ./run_cyber_shield_advanced.sh")
        print("\n📊 Dashboard will be available at: http://localhost:8080")
        return True
    else:
        print("\n⚠️  SOME TESTS FAILED!")
        print("❌ Please check the errors above and fix them before running the application")
        print("\n🔧 Common solutions:")
        print("   1. Install missing dependencies: pip install -r requirements_advanced.txt")
        print("   2. Check Python version (3.8+ required)")
        print("   3. Ensure all source files are present")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        traceback.print_exc()
        sys.exit(1)
