#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process Monitor for Cyber Shield Pro Advanced
Monitor system processes and detect suspicious activity
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

class ProcessMonitor:
    """System process monitor"""
    
    def __init__(self, logger):
        """Initialize process monitor"""
        self.logger = logger
        self.is_monitoring = False
        self.processes = {}
        self.process_count = 0
        self.suspicious_processes = []
        self.last_activity = None
        
        # Suspicious process patterns
        self.suspicious_patterns = [
            'cmd.exe',
            'powershell.exe',
            'nc.exe',
            'ncat.exe',
            'telnet.exe',
            'ftp.exe'
        ]
    
    def start_monitoring(self):
        """Start process monitoring"""
        self.is_monitoring = True
        self.logger.info("Process monitor started")
        
        # Start background monitoring
        asyncio.create_task(self._monitor_processes())
    
    def stop_monitoring(self):
        """Stop process monitoring"""
        self.is_monitoring = False
        self.logger.info("Process monitor stopped")
    
    async def _monitor_processes(self):
        """Monitor system processes"""
        while self.is_monitoring:
            try:
                # Try to use psutil if available
                try:
                    import psutil
                    await self._monitor_with_psutil()
                except ImportError:
                    # Fallback to simulation
                    await self._simulate_process_monitoring()
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring processes: {e}")
                await asyncio.sleep(30)
    
    async def _monitor_with_psutil(self):
        """Monitor processes using psutil"""
        import psutil
        
        current_processes = {}
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                proc_info = proc.info
                current_processes[proc_info['pid']] = {
                    'pid': proc_info['pid'],
                    'name': proc_info['name'],
                    'cpu_percent': proc_info['cpu_percent'],
                    'memory_percent': proc_info['memory_percent'],
                    'timestamp': datetime.now()
                }
                
                # Check for suspicious processes
                if any(pattern in proc_info['name'].lower() for pattern in self.suspicious_patterns):
                    if proc_info['pid'] not in [p['pid'] for p in self.suspicious_processes]:
                        self.suspicious_processes.append(proc_info)
                        self.logger.warning(f"Suspicious process detected: {proc_info['name']} (PID: {proc_info['pid']})")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        self.processes = current_processes
        self.process_count = len(current_processes)
        self.last_activity = datetime.now()
    
    async def _simulate_process_monitoring(self):
        """Simulate process monitoring for demo"""
        # Simulate some common processes
        simulated_processes = {
            1234: {'pid': 1234, 'name': 'explorer.exe', 'cpu_percent': 2.1, 'memory_percent': 5.3},
            5678: {'pid': 5678, 'name': 'chrome.exe', 'cpu_percent': 15.2, 'memory_percent': 12.8},
            9012: {'pid': 9012, 'name': 'python.exe', 'cpu_percent': 8.5, 'memory_percent': 6.2},
            3456: {'pid': 3456, 'name': 'notepad.exe', 'cpu_percent': 0.1, 'memory_percent': 1.2}
        }
        
        # Occasionally add a suspicious process
        if time.time() % 120 < 1:  # Every 2 minutes
            suspicious_proc = {
                'pid': 7890,
                'name': 'cmd.exe',
                'cpu_percent': 25.0,
                'memory_percent': 3.5,
                'timestamp': datetime.now()
            }
            simulated_processes[7890] = suspicious_proc
            
            if 7890 not in [p['pid'] for p in self.suspicious_processes]:
                self.suspicious_processes.append(suspicious_proc)
                self.logger.warning(f"Suspicious process detected: cmd.exe (PID: 7890)")
        
        for proc in simulated_processes.values():
            proc['timestamp'] = datetime.now()
        
        self.processes = simulated_processes
        self.process_count = len(simulated_processes)
        self.last_activity = datetime.now()
    
    def get_running_processes(self) -> List[Dict[str, Any]]:
        """Get list of running processes"""
        return list(self.processes.values())
    
    def get_suspicious_processes(self) -> List[Dict[str, Any]]:
        """Get list of suspicious processes"""
        return self.suspicious_processes.copy()
    
    def get_process_by_pid(self, pid: int) -> Optional[Dict[str, Any]]:
        """Get process information by PID"""
        return self.processes.get(pid)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get monitor statistics"""
        total_cpu = sum(proc.get('cpu_percent', 0) for proc in self.processes.values())
        total_memory = sum(proc.get('memory_percent', 0) for proc in self.processes.values())
        
        return {
            'total_processes': self.process_count,
            'suspicious_processes': len(self.suspicious_processes),
            'total_cpu_usage': round(total_cpu, 2),
            'total_memory_usage': round(total_memory, 2),
            'is_monitoring': self.is_monitoring,
            'last_activity': self.last_activity
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get monitor health status"""
        return {
            'status': 'running' if self.is_monitoring else 'stopped',
            'last_activity': self.last_activity,
            'processes_monitored': self.process_count,
            'suspicious_processes': len(self.suspicious_processes)
        }
