#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Shield Pro Advanced - Project Setup Script
Automatically sets up the project structure and creates missing files
"""

import os
import sys
import shutil
from pathlib import Path

def print_header():
    """Print setup header"""
    print("=" * 60)
    print("🛡️  Cyber Shield Pro Advanced - Project Setup")
    print("=" * 60)
    print()

def create_directory_structure():
    """Create the complete directory structure"""
    print("📁 Creating directory structure...")
    
    directories = [
        # Core directories
        'src',
        'src/utils',
        'src/database',
        'src/security',
        'src/network',
        'src/system',
        'src/dashboard',
        'src/notifications',
        'src/reports',
        
        # Data directories
        'logs',
        'data',
        'temp',
        'reports',
        'quarantine',
        'config',
        'certs',
        
        # Asset directories
        'assets',
        'assets/icons',
        'assets/images',
        'assets/sounds',
        
        # Documentation
        'docs',
        'docs/api',
        'docs/user',
        
        # Tests
        'tests',
        'tests/unit',
        'tests/integration',
        
        # Scripts
        'scripts',
        'scripts/install',
        'scripts/maintenance'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ Created: {directory}")
    
    print("✅ Directory structure created successfully")

def create_init_files():
    """Create __init__.py files for Python packages"""
    print("\n📦 Creating Python package files...")
    
    init_files = [
        'src/__init__.py',
        'src/utils/__init__.py',
        'src/database/__init__.py',
        'src/security/__init__.py',
        'src/network/__init__.py',
        'src/system/__init__.py',
        'src/dashboard/__init__.py',
        'src/notifications/__init__.py',
        'src/reports/__init__.py',
        'tests/__init__.py',
        'tests/unit/__init__.py',
        'tests/integration/__init__.py'
    ]
    
    for init_file in init_files:
        if not os.path.exists(init_file):
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('# -*- coding: utf-8 -*-\n')
                f.write(f'"""\n{os.path.dirname(init_file)} package\n"""\n')
            print(f"  ✅ Created: {init_file}")
    
    print("✅ Python package files created successfully")

def create_placeholder_files():
    """Create placeholder files for missing modules"""
    print("\n📄 Creating placeholder files...")
    
    # Create placeholder for missing modules that might be imported
    placeholder_files = {
        'src/utils/crypto_utils.py': '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cryptographic utilities for Cyber Shield Pro Advanced
"""

import hashlib
import secrets
from cryptography.fernet import Fernet

class CryptoUtils:
    """Cryptographic utility functions"""
    
    @staticmethod
    def generate_key():
        """Generate a new encryption key"""
        return Fernet.generate_key()
    
    @staticmethod
    def encrypt_data(data: bytes, key: bytes) -> bytes:
        """Encrypt data with given key"""
        f = Fernet(key)
        return f.encrypt(data)
    
    @staticmethod
    def decrypt_data(encrypted_data: bytes, key: bytes) -> bytes:
        """Decrypt data with given key"""
        f = Fernet(key)
        return f.decrypt(encrypted_data)
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using SHA-256"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
        return f"{salt}:{password_hash}"
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """Verify a password against its hash"""
        try:
            salt, password_hash = hashed.split(':')
            return hashlib.sha256((password + salt).encode()).hexdigest() == password_hash
        except ValueError:
            return False
''',
        
        'src/utils/file_utils.py': '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File utilities for Cyber Shield Pro Advanced
"""

import os
import shutil
import hashlib
from pathlib import Path
from typing import List, Optional

class FileUtils:
    """File utility functions"""
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'sha256') -> Optional[str]:
        """Calculate hash of a file"""
        try:
            hash_func = getattr(hashlib, algorithm)()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_func.update(chunk)
            return hash_func.hexdigest()
        except Exception:
            return None
    
    @staticmethod
    def safe_delete(file_path: str) -> bool:
        """Safely delete a file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
    
    @staticmethod
    def quarantine_file(file_path: str, quarantine_dir: str = 'quarantine') -> bool:
        """Move file to quarantine directory"""
        try:
            os.makedirs(quarantine_dir, exist_ok=True)
            filename = os.path.basename(file_path)
            quarantine_path = os.path.join(quarantine_dir, filename)
            shutil.move(file_path, quarantine_path)
            return True
        except Exception:
            return False
    
    @staticmethod
    def get_file_info(file_path: str) -> dict:
        """Get detailed file information"""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'accessed': stat.st_atime,
                'permissions': oct(stat.st_mode)[-3:],
                'hash': FileUtils.calculate_file_hash(file_path)
            }
        except Exception:
            return {}
''',
        
        'src/utils/network_utils.py': '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Network utilities for Cyber Shield Pro Advanced
"""

import socket
import requests
from typing import Optional, Dict, Any

class NetworkUtils:
    """Network utility functions"""
    
    @staticmethod
    def is_port_open(host: str, port: int, timeout: int = 3) -> bool:
        """Check if a port is open on a host"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(timeout)
                result = sock.connect_ex((host, port))
                return result == 0
        except Exception:
            return False
    
    @staticmethod
    def get_public_ip() -> Optional[str]:
        """Get public IP address"""
        try:
            response = requests.get('https://api.ipify.org', timeout=5)
            return response.text.strip()
        except Exception:
            return None
    
    @staticmethod
    def get_local_ip() -> Optional[str]:
        """Get local IP address"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.connect(("*******", 80))
                return sock.getsockname()[0]
        except Exception:
            return None
    
    @staticmethod
    def resolve_hostname(hostname: str) -> Optional[str]:
        """Resolve hostname to IP address"""
        try:
            return socket.gethostbyname(hostname)
        except Exception:
            return None
    
    @staticmethod
    def get_ip_info(ip_address: str) -> Dict[str, Any]:
        """Get information about an IP address"""
        try:
            response = requests.get(f'http://ip-api.com/json/{ip_address}', timeout=5)
            return response.json()
        except Exception:
            return {}
'''
    }
    
    for file_path, content in placeholder_files.items():
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ Created: {file_path}")
    
    print("✅ Placeholder files created successfully")

def create_gitignore():
    """Create .gitignore file"""
    print("\n📝 Creating .gitignore file...")
    
    gitignore_content = '''# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Cyber Shield Pro Advanced specific
logs/
data/
temp/
quarantine/
reports/
*.db
*.sqlite
*.sqlite3
config/local_config.yaml
certs/*.key
certs/*.crt
.vscode/
.idea/
*.bak
*.tmp
test_*.db
'''
    
    if not os.path.exists('.gitignore'):
        with open('.gitignore', 'w', encoding='utf-8') as f:
            f.write(gitignore_content)
        print("  ✅ Created: .gitignore")
    
    print("✅ .gitignore file created successfully")

def create_license():
    """Create LICENSE file"""
    print("\n📜 Creating LICENSE file...")
    
    license_content = '''MIT License

Copyright (c) 2025 Cyber Shield Pro Advanced

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
'''
    
    if not os.path.exists('LICENSE'):
        with open('LICENSE', 'w', encoding='utf-8') as f:
            f.write(license_content)
        print("  ✅ Created: LICENSE")
    
    print("✅ LICENSE file created successfully")

def create_contributing_guide():
    """Create CONTRIBUTING.md file"""
    print("\n🤝 Creating CONTRIBUTING.md file...")
    
    contributing_content = '''# Contributing to Cyber Shield Pro Advanced

Thank you for your interest in contributing to Cyber Shield Pro Advanced! This document provides guidelines for contributing to the project.

## Getting Started

1. Fork the repository
2. Clone your fork locally
3. Create a new branch for your feature or bug fix
4. Make your changes
5. Test your changes thoroughly
6. Submit a pull request

## Development Setup

1. Install Python 3.8 or higher
2. Install dependencies: `pip install -r requirements_advanced.txt`
3. Run tests: `python test_cyber_shield_advanced.py`
4. Start the application: `python cyber_shield_advanced.py`

## Code Style

- Follow PEP 8 guidelines
- Use meaningful variable and function names
- Add docstrings to all functions and classes
- Include type hints where appropriate
- Write comprehensive tests for new features

## Reporting Issues

When reporting issues, please include:
- Operating system and version
- Python version
- Steps to reproduce the issue
- Expected behavior
- Actual behavior
- Any error messages or logs

## Feature Requests

We welcome feature requests! Please provide:
- Clear description of the feature
- Use case and benefits
- Possible implementation approach
- Any relevant examples or references

## Security Issues

For security-related issues, please email us directly instead of creating a public issue.

## License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.
'''
    
    if not os.path.exists('CONTRIBUTING.md'):
        with open('CONTRIBUTING.md', 'w', encoding='utf-8') as f:
            f.write(contributing_content)
        print("  ✅ Created: CONTRIBUTING.md")
    
    print("✅ CONTRIBUTING.md file created successfully")

def main():
    """Main setup function"""
    print_header()
    
    try:
        create_directory_structure()
        create_init_files()
        create_placeholder_files()
        create_gitignore()
        create_license()
        create_contributing_guide()
        
        print("\n" + "=" * 60)
        print("🎉 PROJECT SETUP COMPLETE!")
        print("=" * 60)
        print()
        print("✅ All directories and files have been created successfully!")
        print()
        print("🚀 Next steps:")
        print("   1. Install dependencies: pip install -r requirements_advanced.txt")
        print("   2. Test the setup: python test_cyber_shield_advanced.py")
        print("   3. Start the application: python cyber_shield_advanced.py")
        print()
        print("📚 Documentation:")
        print("   • README_ADVANCED.md - Complete user guide")
        print("   • QUICK_START_ADVANCED.md - Quick start guide")
        print("   • CONTRIBUTING.md - Contribution guidelines")
        print()
        print("🛡️  Cyber Shield Pro Advanced is ready for development!")
        
    except Exception as e:
        print(f"\n❌ Error during setup: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
