#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Desktop Notifier for Cyber Shield Pro
Native desktop notifications for real-time alerts
"""

import platform
import subprocess
from typing import Dict, List, Any
from datetime import datetime
from ..utils.logger import Logger

class DesktopNotifier:
    """Desktop Notification System"""
    
    def __init__(self, logger: Logger):
        """Initialize desktop notifier"""
        self.logger = logger
        
        # Detect operating system
        self.os_type = platform.system().lower()
        
        # Notification statistics
        self.stats = {
            'sent': 0,
            'failed': 0,
            'last_sent': None,
            'last_error': None
        }
        
        # Notification icons based on priority
        self.icons = {
            'low': 'info',
            'medium': 'warning',
            'high': 'error',
            'critical': 'error',
            'emergency': 'error'
        }
        
        # Check if notifications are supported
        self.supported = self._check_notification_support()
    
    async def send_notification(self, notification) -> bool:
        """Send desktop notification"""
        try:
            if not self.supported:
                self.logger.warning("Desktop notifications not supported on this system")
                return False
            
            # Format notification
            title = f"Cyber Shield Pro - {notification.title}"
            message = notification.message
            icon = self.icons.get(notification.priority.value, 'info')
            
            # Send notification based on OS
            success = False
            if self.os_type == 'windows':
                success = await self._send_windows_notification(title, message, icon)
            elif self.os_type == 'darwin':  # macOS
                success = await self._send_macos_notification(title, message, icon)
            elif self.os_type == 'linux':
                success = await self._send_linux_notification(title, message, icon)
            else:
                self.logger.warning(f"Desktop notifications not implemented for {self.os_type}")
                return False
            
            if success:
                self.stats['sent'] += 1
                self.stats['last_sent'] = datetime.now()
                self.logger.info(f"Desktop notification sent: {notification.title}")
            else:
                self.stats['failed'] += 1
                self.logger.error(f"Failed to send desktop notification: {notification.title}")
            
            return success
            
        except Exception as e:
            self.stats['failed'] += 1
            self.stats['last_error'] = str(e)
            self.logger.error(f"Error sending desktop notification: {e}")
            return False
    
    async def _send_windows_notification(self, title: str, message: str, icon: str) -> bool:
        """Send Windows toast notification"""
        try:
            # Try using win10toast if available
            try:
                from win10toast import ToastNotifier
                
                toaster = ToastNotifier()
                
                # Determine icon path
                icon_path = None
                if icon == 'error':
                    icon_path = "assets/icons/error.ico"
                elif icon == 'warning':
                    icon_path = "assets/icons/warning.ico"
                else:
                    icon_path = "assets/icons/info.ico"
                
                # Show notification
                toaster.show_toast(
                    title=title,
                    msg=message,
                    icon_path=icon_path if icon_path and os.path.exists(icon_path) else None,
                    duration=10,
                    threaded=True
                )
                
                return True
                
            except ImportError:
                # Fallback to PowerShell
                return await self._send_windows_powershell_notification(title, message)
                
        except Exception as e:
            self.logger.error(f"Error sending Windows notification: {e}")
            return False
    
    async def _send_windows_powershell_notification(self, title: str, message: str) -> bool:
        """Send Windows notification via PowerShell"""
        try:
            # PowerShell script for toast notification
            ps_script = f'''
            [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType = WindowsRuntime] | Out-Null
            [Windows.UI.Notifications.ToastNotification, Windows.UI.Notifications, ContentType = WindowsRuntime] | Out-Null
            [Windows.Data.Xml.Dom.XmlDocument, Windows.Data.Xml.Dom.XmlDocument, ContentType = WindowsRuntime] | Out-Null
            
            $template = @"
            <toast>
                <visual>
                    <binding template="ToastGeneric">
                        <text>{title}</text>
                        <text>{message}</text>
                    </binding>
                </visual>
            </toast>
            "@
            
            $xml = New-Object Windows.Data.Xml.Dom.XmlDocument
            $xml.LoadXml($template)
            $toast = New-Object Windows.UI.Notifications.ToastNotification $xml
            [Windows.UI.Notifications.ToastNotificationManager]::CreateToastNotifier("Cyber Shield Pro").Show($toast)
            '''
            
            # Execute PowerShell script
            result = subprocess.run(
                ['powershell', '-Command', ps_script],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Error sending PowerShell notification: {e}")
            return False
    
    async def _send_macos_notification(self, title: str, message: str, icon: str) -> bool:
        """Send macOS notification"""
        try:
            # Use osascript to display notification
            script = f'''
            display notification "{message}" with title "{title}" subtitle "Security Alert"
            '''
            
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Error sending macOS notification: {e}")
            return False
    
    async def _send_linux_notification(self, title: str, message: str, icon: str) -> bool:
        """Send Linux notification"""
        try:
            # Try notify-send first
            if self._command_exists('notify-send'):
                # Determine urgency level
                urgency = 'normal'
                if icon == 'error':
                    urgency = 'critical'
                elif icon == 'warning':
                    urgency = 'normal'
                
                result = subprocess.run([
                    'notify-send',
                    '--urgency', urgency,
                    '--icon', icon,
                    '--app-name', 'Cyber Shield Pro',
                    title,
                    message
                ], capture_output=True, timeout=10)
                
                return result.returncode == 0
            
            # Try zenity as fallback
            elif self._command_exists('zenity'):
                result = subprocess.run([
                    'zenity',
                    '--notification',
                    '--text', f"{title}\n{message}"
                ], capture_output=True, timeout=10)
                
                return result.returncode == 0
            
            else:
                self.logger.warning("No notification system found on Linux")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending Linux notification: {e}")
            return False
    
    def _check_notification_support(self) -> bool:
        """Check if desktop notifications are supported"""
        try:
            if self.os_type == 'windows':
                # Windows 10+ supports toast notifications
                return True
            elif self.os_type == 'darwin':
                # macOS supports notifications via osascript
                return self._command_exists('osascript')
            elif self.os_type == 'linux':
                # Linux supports notifications via notify-send or zenity
                return self._command_exists('notify-send') or self._command_exists('zenity')
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking notification support: {e}")
            return False
    
    def _command_exists(self, command: str) -> bool:
        """Check if a command exists in the system"""
        try:
            result = subprocess.run(
                ['which', command] if self.os_type != 'windows' else ['where', command],
                capture_output=True,
                timeout=5
            )
            return result.returncode == 0
        except Exception:
            return False
    
    def show_persistent_alert(self, title: str, message: str, alert_type: str = 'warning') -> bool:
        """Show persistent alert dialog"""
        try:
            if self.os_type == 'windows':
                return self._show_windows_dialog(title, message, alert_type)
            elif self.os_type == 'darwin':
                return self._show_macos_dialog(title, message, alert_type)
            elif self.os_type == 'linux':
                return self._show_linux_dialog(title, message, alert_type)
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"Error showing persistent alert: {e}")
            return False
    
    def _show_windows_dialog(self, title: str, message: str, alert_type: str) -> bool:
        """Show Windows message box"""
        try:
            import ctypes
            
            # Message box types
            MB_OK = 0x0
            MB_ICONWARNING = 0x30
            MB_ICONERROR = 0x10
            MB_ICONINFORMATION = 0x40
            
            icon_type = MB_ICONINFORMATION
            if alert_type == 'error':
                icon_type = MB_ICONERROR
            elif alert_type == 'warning':
                icon_type = MB_ICONWARNING
            
            ctypes.windll.user32.MessageBoxW(0, message, title, MB_OK | icon_type)
            return True
            
        except Exception as e:
            self.logger.error(f"Error showing Windows dialog: {e}")
            return False
    
    def _show_macos_dialog(self, title: str, message: str, alert_type: str) -> bool:
        """Show macOS dialog"""
        try:
            script = f'''
            display dialog "{message}" with title "{title}" buttons {{"OK"}} default button "OK"
            '''
            
            result = subprocess.run(
                ['osascript', '-e', script],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Error showing macOS dialog: {e}")
            return False
    
    def _show_linux_dialog(self, title: str, message: str, alert_type: str) -> bool:
        """Show Linux dialog"""
        try:
            if self._command_exists('zenity'):
                dialog_type = '--info'
                if alert_type == 'error':
                    dialog_type = '--error'
                elif alert_type == 'warning':
                    dialog_type = '--warning'
                
                result = subprocess.run([
                    'zenity',
                    dialog_type,
                    '--title', title,
                    '--text', message
                ], capture_output=True, timeout=30)
                
                return result.returncode == 0
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error showing Linux dialog: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get desktop notification statistics"""
        return {
            **self.stats,
            'supported': self.supported,
            'os_type': self.os_type
        }
    
    def test_notification(self) -> bool:
        """Test desktop notification system"""
        try:
            if not self.supported:
                return False
            
            # Create test notification
            class TestNotification:
                def __init__(self):
                    self.title = "Test Notification"
                    self.message = "This is a test notification from Cyber Shield Pro"
                    self.priority = type('Priority', (), {'value': 'medium'})()
            
            test_notification = TestNotification()
            
            # Send test notification
            import asyncio
            return asyncio.run(self.send_notification(test_notification))
            
        except Exception as e:
            self.logger.error(f"Error testing desktop notification: {e}")
            return False
