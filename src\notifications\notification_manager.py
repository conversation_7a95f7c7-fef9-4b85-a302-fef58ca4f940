#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Notification Manager for Cyber Shield Pro
Multi-channel notification system with intelligent routing and escalation
"""

import asyncio
import threading
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
import json
from .email_notifier import EmailNotifier
from .sms_notifier import SMSNotifier
from .desktop_notifier import DesktopNotifier
from .webhook_notifier import WebhookNotifier
from ..utils.logger import Logger

class NotificationPriority(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class NotificationChannel(Enum):
    EMAIL = "email"
    SMS = "sms"
    DESKTOP = "desktop"
    WEBHOOK = "webhook"
    DASHBOARD = "dashboard"
    PUSH = "push"

class NotificationStatus(Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    ACKNOWLEDGED = "acknowledged"

@dataclass
class NotificationRule:
    """Notification routing rule"""
    id: str
    name: str
    conditions: Dict[str, Any]
    channels: List[NotificationChannel]
    recipients: List[str]
    priority: NotificationPriority
    enabled: bool = True
    escalation_delay: int = 300  # 5 minutes
    max_escalations: int = 3
    quiet_hours: Optional[Dict[str, str]] = None  # {"start": "22:00", "end": "06:00"}

@dataclass
class Notification:
    """Notification message"""
    id: str
    timestamp: datetime
    priority: NotificationPriority
    category: str
    title: str
    message: str
    details: Dict[str, Any]
    channels: List[NotificationChannel]
    recipients: List[str]
    status: NotificationStatus = NotificationStatus.PENDING
    attempts: int = 0
    max_attempts: int = 3
    escalation_level: int = 0
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    delivery_receipts: Dict[str, Dict[str, Any]] = None

class NotificationManager:
    """Advanced Multi-Channel Notification System"""
    
    def __init__(self, logger: Logger):
        """Initialize notification manager"""
        self.logger = logger
        
        # Notification channels
        self.email_notifier = EmailNotifier(logger)
        self.sms_notifier = SMSNotifier(logger)
        self.desktop_notifier = DesktopNotifier(logger)
        self.webhook_notifier = WebhookNotifier(logger)
        
        # Notification state
        self.notifications = {}
        self.notification_rules = {}
        self.notification_queue = asyncio.Queue()
        
        # Processing state
        self.is_running = False
        self.worker_tasks = []
        
        # Configuration
        self.config = {
            'max_queue_size': 1000,
            'worker_count': 3,
            'retry_delay': 60,  # seconds
            'escalation_enabled': True,
            'rate_limiting': {
                'email': {'max_per_hour': 100, 'max_per_day': 500},
                'sms': {'max_per_hour': 50, 'max_per_day': 200},
                'webhook': {'max_per_hour': 1000, 'max_per_day': 10000}
            }
        }
        
        # Rate limiting tracking
        self.rate_limits = {
            channel.value: {'hourly': [], 'daily': []}
            for channel in NotificationChannel
        }
        
        # Callbacks
        self.callbacks = {
            'on_notification_sent': [],
            'on_notification_failed': [],
            'on_escalation': []
        }
        
        # Default notification rules
        self._setup_default_rules()
    
    async def start(self):
        """Start notification processing"""
        try:
            if self.is_running:
                return
            
            self.is_running = True
            
            # Start worker tasks
            for i in range(self.config['worker_count']):
                task = asyncio.create_task(self._notification_worker(f"worker-{i}"))
                self.worker_tasks.append(task)
            
            # Start escalation monitor
            escalation_task = asyncio.create_task(self._escalation_monitor())
            self.worker_tasks.append(escalation_task)
            
            # Start rate limit cleanup
            cleanup_task = asyncio.create_task(self._rate_limit_cleanup())
            self.worker_tasks.append(cleanup_task)
            
            self.logger.info("Notification manager started")
            
        except Exception as e:
            self.logger.error(f"Error starting notification manager: {e}")
            raise
    
    async def stop(self):
        """Stop notification processing"""
        try:
            self.is_running = False
            
            # Cancel worker tasks
            for task in self.worker_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
            
            self.worker_tasks.clear()
            self.logger.info("Notification manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping notification manager: {e}")
    
    async def send_notification(self, priority: NotificationPriority, category: str,
                              title: str, message: str, details: Dict[str, Any] = None,
                              custom_recipients: List[str] = None,
                              custom_channels: List[NotificationChannel] = None) -> str:
        """Send a notification"""
        try:
            # Generate notification ID
            notification_id = f"notif_{int(time.time() * 1000)}"
            
            # Determine channels and recipients based on rules
            channels, recipients = self._determine_routing(
                priority, category, custom_channels, custom_recipients
            )
            
            if not channels or not recipients:
                self.logger.warning(f"No routing found for notification: {title}")
                return notification_id
            
            # Create notification
            notification = Notification(
                id=notification_id,
                timestamp=datetime.now(),
                priority=priority,
                category=category,
                title=title,
                message=message,
                details=details or {},
                channels=channels,
                recipients=recipients,
                delivery_receipts={}
            )
            
            # Store notification
            self.notifications[notification_id] = notification
            
            # Add to queue
            await self.notification_queue.put(notification)
            
            self.logger.info(f"Notification queued: {title} (ID: {notification_id})")
            
            return notification_id
            
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
            raise
    
    async def _notification_worker(self, worker_name: str):
        """Notification processing worker"""
        self.logger.info(f"Notification worker {worker_name} started")
        
        while self.is_running:
            try:
                # Get notification from queue
                notification = await asyncio.wait_for(
                    self.notification_queue.get(), timeout=1.0
                )
                
                # Process notification
                await self._process_notification(notification)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error in notification worker {worker_name}: {e}")
                await asyncio.sleep(1)
        
        self.logger.info(f"Notification worker {worker_name} stopped")
    
    async def _process_notification(self, notification: Notification):
        """Process a single notification"""
        try:
            # Check if in quiet hours
            if self._is_quiet_hours(notification):
                # Delay non-critical notifications
                if notification.priority not in [NotificationPriority.CRITICAL, NotificationPriority.EMERGENCY]:
                    await asyncio.sleep(60)  # Check again in 1 minute
                    await self.notification_queue.put(notification)
                    return
            
            # Check rate limits
            if not self._check_rate_limits(notification):
                self.logger.warning(f"Rate limit exceeded for notification {notification.id}")
                notification.status = NotificationStatus.FAILED
                return
            
            # Send through each channel
            success_count = 0
            for channel in notification.channels:
                try:
                    success = await self._send_through_channel(notification, channel)
                    if success:
                        success_count += 1
                        self._update_rate_limits(channel)
                    
                except Exception as e:
                    self.logger.error(f"Error sending through {channel.value}: {e}")
            
            # Update notification status
            if success_count > 0:
                notification.status = NotificationStatus.SENT
                notification.attempts += 1
                
                # Call success callbacks
                for callback in self.callbacks['on_notification_sent']:
                    try:
                        callback(notification)
                    except Exception as e:
                        self.logger.error(f"Error in notification callback: {e}")
                
            else:
                notification.status = NotificationStatus.FAILED
                notification.attempts += 1
                
                # Retry if under max attempts
                if notification.attempts < notification.max_attempts:
                    await asyncio.sleep(self.config['retry_delay'])
                    await self.notification_queue.put(notification)
                else:
                    # Call failure callbacks
                    for callback in self.callbacks['on_notification_failed']:
                        try:
                            callback(notification)
                        except Exception as e:
                            self.logger.error(f"Error in failure callback: {e}")
            
        except Exception as e:
            self.logger.error(f"Error processing notification {notification.id}: {e}")
    
    async def _send_through_channel(self, notification: Notification, 
                                  channel: NotificationChannel) -> bool:
        """Send notification through specific channel"""
        try:
            if channel == NotificationChannel.EMAIL:
                return await self.email_notifier.send_notification(notification)
            elif channel == NotificationChannel.SMS:
                return await self.sms_notifier.send_notification(notification)
            elif channel == NotificationChannel.DESKTOP:
                return await self.desktop_notifier.send_notification(notification)
            elif channel == NotificationChannel.WEBHOOK:
                return await self.webhook_notifier.send_notification(notification)
            elif channel == NotificationChannel.DASHBOARD:
                return await self._send_dashboard_notification(notification)
            else:
                self.logger.warning(f"Unsupported channel: {channel}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending through {channel.value}: {e}")
            return False
    
    async def _send_dashboard_notification(self, notification: Notification) -> bool:
        """Send notification to dashboard"""
        try:
            # This would integrate with the dashboard system
            # For now, just log
            self.logger.info(f"Dashboard notification: {notification.title}")
            return True
        except Exception as e:
            self.logger.error(f"Error sending dashboard notification: {e}")
            return False
    
    async def _escalation_monitor(self):
        """Monitor for notifications requiring escalation"""
        while self.is_running:
            try:
                if not self.config['escalation_enabled']:
                    await asyncio.sleep(60)
                    continue
                
                current_time = datetime.now()
                
                for notification in self.notifications.values():
                    if (notification.status == NotificationStatus.SENT and
                        not notification.acknowledged and
                        notification.escalation_level < 3):
                        
                        # Check if escalation time has passed
                        escalation_time = notification.timestamp + timedelta(
                            seconds=300 * (notification.escalation_level + 1)  # 5, 10, 15 minutes
                        )
                        
                        if current_time >= escalation_time:
                            await self._escalate_notification(notification)
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in escalation monitor: {e}")
                await asyncio.sleep(60)
    
    async def _escalate_notification(self, notification: Notification):
        """Escalate a notification"""
        try:
            notification.escalation_level += 1
            
            # Create escalated notification
            escalated_title = f"[ESCALATED] {notification.title}"
            escalated_message = f"ESCALATION LEVEL {notification.escalation_level}\n\n{notification.message}"
            
            # Send to higher priority channels/recipients
            escalation_channels = [NotificationChannel.EMAIL, NotificationChannel.SMS]
            escalation_recipients = self._get_escalation_recipients(notification.escalation_level)
            
            escalated_notification = Notification(
                id=f"{notification.id}_esc_{notification.escalation_level}",
                timestamp=datetime.now(),
                priority=NotificationPriority.CRITICAL,
                category=f"escalation_{notification.category}",
                title=escalated_title,
                message=escalated_message,
                details=notification.details,
                channels=escalation_channels,
                recipients=escalation_recipients
            )
            
            await self.notification_queue.put(escalated_notification)
            
            # Call escalation callbacks
            for callback in self.callbacks['on_escalation']:
                try:
                    callback(notification, escalated_notification)
                except Exception as e:
                    self.logger.error(f"Error in escalation callback: {e}")
            
            self.logger.warning(f"Notification escalated: {notification.title} (Level {notification.escalation_level})")
            
        except Exception as e:
            self.logger.error(f"Error escalating notification: {e}")
    
    def _determine_routing(self, priority: NotificationPriority, category: str,
                          custom_channels: List[NotificationChannel] = None,
                          custom_recipients: List[str] = None) -> tuple:
        """Determine notification routing based on rules"""
        try:
            if custom_channels and custom_recipients:
                return custom_channels, custom_recipients
            
            # Find matching rules
            matching_rules = []
            for rule in self.notification_rules.values():
                if not rule.enabled:
                    continue
                
                if self._rule_matches(rule, priority, category):
                    matching_rules.append(rule)
            
            if not matching_rules:
                # Use default routing
                return self._get_default_routing(priority)
            
            # Combine channels and recipients from all matching rules
            channels = set()
            recipients = set()
            
            for rule in matching_rules:
                channels.update(rule.channels)
                recipients.update(rule.recipients)
            
            return list(channels), list(recipients)
            
        except Exception as e:
            self.logger.error(f"Error determining routing: {e}")
            return [], []
    
    def _rule_matches(self, rule: NotificationRule, priority: NotificationPriority, category: str) -> bool:
        """Check if a rule matches the notification criteria"""
        try:
            conditions = rule.conditions
            
            # Check priority
            if 'priority' in conditions:
                required_priorities = conditions['priority']
                if isinstance(required_priorities, str):
                    required_priorities = [required_priorities]
                if priority.value not in required_priorities:
                    return False
            
            # Check category
            if 'category' in conditions:
                required_categories = conditions['category']
                if isinstance(required_categories, str):
                    required_categories = [required_categories]
                if category not in required_categories:
                    return False
            
            # Check time-based conditions
            if 'time_range' in conditions:
                current_time = datetime.now().time()
                time_range = conditions['time_range']
                start_time = datetime.strptime(time_range['start'], '%H:%M').time()
                end_time = datetime.strptime(time_range['end'], '%H:%M').time()
                
                if start_time <= end_time:
                    if not (start_time <= current_time <= end_time):
                        return False
                else:  # Overnight range
                    if not (current_time >= start_time or current_time <= end_time):
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking rule match: {e}")
            return False
    
    def _get_default_routing(self, priority: NotificationPriority) -> tuple:
        """Get default routing for priority level"""
        routing_map = {
            NotificationPriority.LOW: ([NotificationChannel.DASHBOARD], ['<EMAIL>']),
            NotificationPriority.MEDIUM: ([NotificationChannel.EMAIL, NotificationChannel.DASHBOARD], ['<EMAIL>']),
            NotificationPriority.HIGH: ([NotificationChannel.EMAIL, NotificationChannel.DESKTOP], ['<EMAIL>', '<EMAIL>']),
            NotificationPriority.CRITICAL: ([NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.DESKTOP], ['<EMAIL>', '<EMAIL>']),
            NotificationPriority.EMERGENCY: ([NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.WEBHOOK], ['<EMAIL>', '<EMAIL>', '<EMAIL>'])
        }
        
        return routing_map.get(priority, ([NotificationChannel.EMAIL], ['<EMAIL>']))
    
    def _get_escalation_recipients(self, escalation_level: int) -> List[str]:
        """Get recipients for escalation level"""
        escalation_map = {
            1: ['<EMAIL>', '<EMAIL>'],
            2: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
            3: ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
        
        return escalation_map.get(escalation_level, ['<EMAIL>'])
    
    def _is_quiet_hours(self, notification: Notification) -> bool:
        """Check if current time is in quiet hours"""
        try:
            # Find applicable rule with quiet hours
            for rule in self.notification_rules.values():
                if (rule.enabled and rule.quiet_hours and 
                    self._rule_matches(rule, notification.priority, notification.category)):
                    
                    current_time = datetime.now().time()
                    start_time = datetime.strptime(rule.quiet_hours['start'], '%H:%M').time()
                    end_time = datetime.strptime(rule.quiet_hours['end'], '%H:%M').time()
                    
                    if start_time <= end_time:
                        return start_time <= current_time <= end_time
                    else:  # Overnight quiet hours
                        return current_time >= start_time or current_time <= end_time
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking quiet hours: {e}")
            return False
    
    def _check_rate_limits(self, notification: Notification) -> bool:
        """Check if notification is within rate limits"""
        try:
            current_time = datetime.now()
            
            for channel in notification.channels:
                channel_limits = self.config['rate_limiting'].get(channel.value, {})
                channel_tracking = self.rate_limits[channel.value]
                
                # Check hourly limit
                if 'max_per_hour' in channel_limits:
                    hourly_count = len([
                        t for t in channel_tracking['hourly']
                        if current_time - t < timedelta(hours=1)
                    ])
                    
                    if hourly_count >= channel_limits['max_per_hour']:
                        return False
                
                # Check daily limit
                if 'max_per_day' in channel_limits:
                    daily_count = len([
                        t for t in channel_tracking['daily']
                        if current_time - t < timedelta(days=1)
                    ])
                    
                    if daily_count >= channel_limits['max_per_day']:
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking rate limits: {e}")
            return True
    
    def _update_rate_limits(self, channel: NotificationChannel):
        """Update rate limit tracking"""
        try:
            current_time = datetime.now()
            channel_tracking = self.rate_limits[channel.value]
            
            channel_tracking['hourly'].append(current_time)
            channel_tracking['daily'].append(current_time)
            
        except Exception as e:
            self.logger.error(f"Error updating rate limits: {e}")
    
    async def _rate_limit_cleanup(self):
        """Clean up old rate limit entries"""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                for channel_tracking in self.rate_limits.values():
                    # Clean hourly tracking
                    channel_tracking['hourly'] = [
                        t for t in channel_tracking['hourly']
                        if current_time - t < timedelta(hours=1)
                    ]
                    
                    # Clean daily tracking
                    channel_tracking['daily'] = [
                        t for t in channel_tracking['daily']
                        if current_time - t < timedelta(days=1)
                    ]
                
                await asyncio.sleep(300)  # Clean every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in rate limit cleanup: {e}")
                await asyncio.sleep(300)
    
    def _setup_default_rules(self):
        """Setup default notification rules"""
        try:
            # Critical security alerts
            self.notification_rules['critical_security'] = NotificationRule(
                id='critical_security',
                name='Critical Security Alerts',
                conditions={
                    'priority': ['critical', 'emergency'],
                    'category': ['security', 'threat', 'malware']
                },
                channels=[NotificationChannel.EMAIL, NotificationChannel.SMS, NotificationChannel.DESKTOP],
                recipients=['<EMAIL>', '<EMAIL>'],
                priority=NotificationPriority.CRITICAL,
                escalation_delay=300,
                max_escalations=3
            )
            
            # System performance alerts
            self.notification_rules['system_performance'] = NotificationRule(
                id='system_performance',
                name='System Performance Alerts',
                conditions={
                    'category': ['performance', 'system']
                },
                channels=[NotificationChannel.EMAIL, NotificationChannel.DASHBOARD],
                recipients=['<EMAIL>'],
                priority=NotificationPriority.MEDIUM,
                quiet_hours={'start': '22:00', 'end': '06:00'}
            )
            
            # Network security alerts
            self.notification_rules['network_security'] = NotificationRule(
                id='network_security',
                name='Network Security Alerts',
                conditions={
                    'category': ['network', 'firewall', 'intrusion']
                },
                channels=[NotificationChannel.EMAIL, NotificationChannel.WEBHOOK],
                recipients=['<EMAIL>', '<EMAIL>'],
                priority=NotificationPriority.HIGH
            )
            
        except Exception as e:
            self.logger.error(f"Error setting up default rules: {e}")
    
    def add_notification_rule(self, rule: NotificationRule):
        """Add a notification rule"""
        self.notification_rules[rule.id] = rule
        self.logger.info(f"Notification rule added: {rule.name}")
    
    def remove_notification_rule(self, rule_id: str):
        """Remove a notification rule"""
        if rule_id in self.notification_rules:
            del self.notification_rules[rule_id]
            self.logger.info(f"Notification rule removed: {rule_id}")
    
    def acknowledge_notification(self, notification_id: str, acknowledged_by: str):
        """Acknowledge a notification"""
        if notification_id in self.notifications:
            notification = self.notifications[notification_id]
            notification.acknowledged = True
            notification.acknowledged_by = acknowledged_by
            notification.acknowledged_at = datetime.now()
            notification.status = NotificationStatus.ACKNOWLEDGED
            
            self.logger.info(f"Notification acknowledged: {notification_id} by {acknowledged_by}")
    
    def get_notification_status(self, notification_id: str) -> Optional[Dict[str, Any]]:
        """Get notification status"""
        if notification_id in self.notifications:
            return asdict(self.notifications[notification_id])
        return None
    
    def get_notification_statistics(self) -> Dict[str, Any]:
        """Get notification statistics"""
        try:
            total_notifications = len(self.notifications)
            
            status_counts = {}
            priority_counts = {}
            channel_counts = {}
            
            for notification in self.notifications.values():
                # Count by status
                status = notification.status.value
                status_counts[status] = status_counts.get(status, 0) + 1
                
                # Count by priority
                priority = notification.priority.value
                priority_counts[priority] = priority_counts.get(priority, 0) + 1
                
                # Count by channels
                for channel in notification.channels:
                    channel_name = channel.value
                    channel_counts[channel_name] = channel_counts.get(channel_name, 0) + 1
            
            return {
                'total_notifications': total_notifications,
                'status_breakdown': status_counts,
                'priority_breakdown': priority_counts,
                'channel_breakdown': channel_counts,
                'active_rules': len([r for r in self.notification_rules.values() if r.enabled]),
                'queue_size': self.notification_queue.qsize(),
                'is_running': self.is_running
            }
            
        except Exception as e:
            self.logger.error(f"Error getting notification statistics: {e}")
            return {}
    
    def add_callback(self, event_type: str, callback: Callable):
        """Add event callback"""
        if event_type in self.callbacks:
            self.callbacks[event_type].append(callback)
    
    def remove_callback(self, event_type: str, callback: Callable):
        """Remove event callback"""
        if event_type in self.callbacks and callback in self.callbacks[event_type]:
            self.callbacks[event_type].remove(callback)
