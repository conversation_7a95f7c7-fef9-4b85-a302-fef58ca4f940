# دليل التثبيت والتشغيل - Cyber Shield Pro

## متطلبات النظام / System Requirements

- **نظام التشغيل**: Windows 10/11 (64-bit)
- **الذاكرة**: 4GB RAM (8GB مستحسن)
- **المساحة**: 2GB مساحة فارغة
- **الاتصال**: اتصال بالإنترنت للتحديثات

## خطوات التثبيت / Installation Steps

### الخطوة 1: تثبيت Python

1. **تحميل Python**:
   - اذهب إلى: https://www.python.org/downloads/
   - حمل Python 3.8 أو أحدث
   - اختر "Windows installer (64-bit)"

2. **تثبيت Python**:
   - شغل ملف التثبيت
   - ✅ **مهم**: تأكد من تفعيل "Add Python to PATH"
   - اختر "Install Now"
   - انتظر حتى اكتمال التثبيت

3. **التحقق من التثبيت**:
   - افتح Command Prompt
   - اكتب: `python --version`
   - يجب أن ترى رقم الإصدار

### الخطوة 2: تشغيل البرنامج

#### الطريقة الأولى (تلقائية):
1. انقر نقراً مزدوجاً على `install_and_run.bat`
2. سيقوم البرنامج بتثبيت المكتبات المطلوبة تلقائياً
3. سيبدأ تشغيل البرنامج

#### الطريقة الثانية (يدوية):
1. افتح Command Prompt في مجلد البرنامج
2. اكتب: `pip install -r requirements.txt`
3. اكتب: `python main.py`

#### الطريقة الثالثة (ملف التشغيل):
1. انقر نقراً مزدوجاً على `run.bat`

## استكشاف الأخطاء / Troubleshooting

### خطأ: "python is not recognized"
**الحل**:
1. تأكد من تثبيت Python بشكل صحيح
2. أعد تشغيل الكمبيوتر
3. أضف Python إلى PATH يدوياً:
   - ابحث عن "Environment Variables"
   - أضف مسار Python إلى PATH
   - مثال: `C:\Users\<USER>\AppData\Local\Programs\Python\Python39\`

### خطأ: "No module named 'customtkinter'"
**الحل**:
```cmd
pip install customtkinter
```

### خطأ: "Permission denied"
**الحل**:
1. شغل Command Prompt كمسؤول
2. أو استخدم: `pip install --user package_name`

### خطأ في قاعدة البيانات
**الحل**:
1. احذف مجلد `database`
2. أعد تشغيل البرنامج

## الاستخدام الأول / First Use

1. **إنشاء حساب**:
   - اختر "إنشاء حساب جديد"
   - أدخل اسم المستخدم والبريد الإلكتروني
   - اختر كلمة مرور قوية

2. **تسجيل الدخول**:
   - أدخل اسم المستخدم وكلمة المرور
   - اختر "تذكرني" إذا كنت تريد البقاء مسجلاً

3. **استكشاف البرنامج**:
   - ابدأ بفحص سريع
   - تصفح القائمة الجانبية
   - اطلع على حالة الحماية

## المساعدة والدعم / Help & Support

- **البريد الإلكتروني**: <EMAIL>
- **واتساب**: +970599123456
- **الموقع**: https://cybershield.ps

## معلومات إضافية / Additional Information

### حساب المسؤول الافتراضي:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- ⚠️ **مهم**: غير كلمة المرور فوراً بعد أول تسجيل دخول

### ملفات مهمة:
- `main.py` - الملف الرئيسي للبرنامج
- `config/` - ملفات التكوين
- `database/` - قاعدة البيانات
- `logs/` - ملفات السجلات

### النسخ الاحتياطي:
- يتم حفظ النسخ الاحتياطية في مجلد `backup/`
- يُنصح بعمل نسخة احتياطية دورية من مجلد `database/`

---

**Cyber Shield Pro © 2025**  
**تم التطوير في فلسطين 🇵🇸**  
**جميع الحقوق محفوظة**
