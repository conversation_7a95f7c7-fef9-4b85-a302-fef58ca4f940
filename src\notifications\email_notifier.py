#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Email Notifier for Cyber Shield Pro
Professional email notifications with HTML templates
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, List, Any
from datetime import datetime
from ..utils.logger import Logger

class EmailNotifier:
    """Professional Email Notification System"""
    
    def __init__(self, logger: Logger):
        """Initialize email notifier"""
        self.logger = logger
        
        # SMTP configuration
        self.smtp_config = {
            'server': 'smtp.gmail.com',
            'port': 587,
            'use_tls': True,
            'username': '<EMAIL>',
            'password': 'app_password',
            'from_name': 'Cyber Shield Pro',
            'from_email': '<EMAIL>'
        }
        
        # Email statistics
        self.stats = {
            'sent': 0,
            'failed': 0,
            'last_sent': None,
            'last_error': None
        }
    
    async def send_notification(self, notification) -> bool:
        """Send email notification"""
        try:
            # Create email message
            message = self._create_email_message(notification)
            
            # Send email
            success = await self._send_email(message, notification.recipients)
            
            if success:
                self.stats['sent'] += 1
                self.stats['last_sent'] = datetime.now()
                self.logger.info(f"Email notification sent: {notification.title}")
            else:
                self.stats['failed'] += 1
                self.logger.error(f"Failed to send email notification: {notification.title}")
            
            return success
            
        except Exception as e:
            self.stats['failed'] += 1
            self.stats['last_error'] = str(e)
            self.logger.error(f"Error sending email notification: {e}")
            return False
    
    def _create_email_message(self, notification) -> MIMEMultipart:
        """Create email message with HTML template"""
        try:
            # Create message
            message = MIMEMultipart('alternative')
            message['Subject'] = f"[Cyber Shield Pro] {notification.title}"
            message['From'] = f"{self.smtp_config['from_name']} <{self.smtp_config['from_email']}>"
            
            # Create HTML content
            html_content = self._get_html_template().format(
                title=notification.title,
                message=notification.message,
                priority=notification.priority.value.upper(),
                category=notification.category,
                timestamp=notification.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                details=self._format_details(notification.details),
                priority_color=self._get_priority_color(notification.priority)
            )
            
            # Create HTML part
            html_part = MIMEText(html_content, 'html')
            message.attach(html_part)
            
            # Create plain text version
            text_content = f"""
            Cyber Shield Pro Security Alert
            
            Title: {notification.title}
            Priority: {notification.priority.value.upper()}
            Category: {notification.category}
            Time: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            
            Message:
            {notification.message}
            
            Details:
            {self._format_details_text(notification.details)}
            """
            
            text_part = MIMEText(text_content, 'plain')
            message.attach(text_part)
            
            return message
            
        except Exception as e:
            self.logger.error(f"Error creating email message: {e}")
            raise
    
    async def _send_email(self, message: MIMEMultipart, recipients: List[str]) -> bool:
        """Send email via SMTP"""
        try:
            # Create SMTP connection
            if self.smtp_config['use_tls']:
                context = ssl.create_default_context()
                server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
                server.starttls(context=context)
            else:
                server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
            
            # Login
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            
            # Send to each recipient
            for recipient in recipients:
                message['To'] = recipient
                text = message.as_string()
                server.sendmail(self.smtp_config['from_email'], recipient, text)
                del message['To']  # Remove for next recipient
            
            # Close connection
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email via SMTP: {e}")
            return False
    
    def _get_priority_color(self, priority) -> str:
        """Get color for priority level"""
        color_map = {
            'low': '#28a745',
            'medium': '#ffc107',
            'high': '#fd7e14',
            'critical': '#dc3545',
            'emergency': '#6f42c1'
        }
        
        return color_map.get(priority.value, '#6c757d')
    
    def _format_details(self, details: Dict[str, Any]) -> str:
        """Format details dictionary as HTML"""
        if not details:
            return ""
        
        html = "<ul>"
        for key, value in details.items():
            formatted_key = key.replace('_', ' ').title()
            html += f"<li><strong>{formatted_key}:</strong> {value}</li>"
        html += "</ul>"
        
        return html
    
    def _format_details_text(self, details: Dict[str, Any]) -> str:
        """Format details dictionary as plain text"""
        if not details:
            return ""
        
        text = ""
        for key, value in details.items():
            formatted_key = key.replace('_', ' ').title()
            text += f"- {formatted_key}: {value}\n"
        
        return text
    
    def _get_html_template(self) -> str:
        """Get HTML email template"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Cyber Shield Pro Alert</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f8f9fa;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }}
                .logo {{
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }}
                .alert-badge {{
                    background-color: {priority_color};
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 14px;
                    font-weight: bold;
                    display: inline-block;
                    margin-top: 10px;
                }}
                .content {{
                    padding: 30px;
                }}
                .alert-title {{
                    font-size: 20px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                }}
                .alert-message {{
                    font-size: 16px;
                    line-height: 1.6;
                    color: #34495e;
                    margin-bottom: 20px;
                }}
                .details {{
                    background-color: #f8f9fa;
                    border-left: 4px solid {priority_color};
                    padding: 15px;
                    margin: 20px 0;
                }}
                .details h4 {{
                    margin-top: 0;
                    color: #2c3e50;
                }}
                .timestamp {{
                    color: #6c757d;
                    font-size: 14px;
                    margin-top: 20px;
                }}
                .footer {{
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    font-size: 14px;
                }}
                .action-required {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 5px;
                    padding: 15px;
                    margin: 20px 0;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🛡️ Cyber Shield Pro</div>
                    <div>Advanced Cybersecurity System</div>
                    <div class="alert-badge">{priority} PRIORITY</div>
                </div>
                
                <div class="content">
                    <div class="alert-title">{title}</div>
                    <div class="alert-message">{message}</div>
                    
                    <div class="action-required">
                        <strong>⚠️ Action Required:</strong> This alert requires attention from the security team.
                    </div>
                    
                    <div class="details">
                        <h4>📋 Alert Details</h4>
                        <p><strong>Category:</strong> {category}</p>
                        <p><strong>Priority:</strong> {priority}</p>
                        <p><strong>Timestamp:</strong> {timestamp}</p>
                        {details}
                    </div>
                    
                    <div class="timestamp">
                        Alert generated on {timestamp}
                    </div>
                </div>
                
                <div class="footer">
                    <p>This is an automated security alert from Cyber Shield Pro</p>
                    <p>Please do not reply to this email. Contact your system administrator for assistance.</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get email notification statistics"""
        return self.stats.copy()
    
    def test_connection(self) -> bool:
        """Test SMTP connection"""
        try:
            if self.smtp_config['use_tls']:
                context = ssl.create_default_context()
                server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
                server.starttls(context=context)
            else:
                server = smtplib.SMTP(self.smtp_config['server'], self.smtp_config['port'])
            
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            server.quit()
            
            self.logger.info("SMTP connection test successful")
            return True
            
        except Exception as e:
            self.logger.error(f"SMTP connection test failed: {e}")
            return False
