#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Webhook Notifier for Cyber Shield Pro
HTTP webhook notifications for integration with external systems
"""

import json
import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from ..utils.logger import Logger

class WebhookNotifier:
    """Webhook Notification System"""
    
    def __init__(self, logger: Logger):
        """Initialize webhook notifier"""
        self.logger = logger
        
        # Webhook configuration
        self.webhook_config = {
            'timeout': 30,
            'retry_attempts': 3,
            'retry_delay': 5,
            'verify_ssl': True,
            'user_agent': 'Cyber Shield Pro Webhook Notifier/1.0'
        }
        
        # Webhook endpoints
        self.webhooks = {
            'slack': {
                'url': 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK',
                'format': 'slack',
                'enabled': False
            },
            'discord': {
                'url': 'https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK',
                'format': 'discord',
                'enabled': False
            },
            'teams': {
                'url': 'https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK',
                'format': 'teams',
                'enabled': False
            },
            'custom': {
                'url': 'https://your-custom-endpoint.com/webhook',
                'format': 'json',
                'enabled': False,
                'headers': {
                    'Authorization': 'Bearer your-token',
                    'Content-Type': 'application/json'
                }
            }
        }
        
        # Notification statistics
        self.stats = {
            'sent': 0,
            'failed': 0,
            'last_sent': None,
            'last_error': None,
            'webhook_stats': {}
        }
        
        # Initialize webhook stats
        for webhook_name in self.webhooks:
            self.stats['webhook_stats'][webhook_name] = {
                'sent': 0,
                'failed': 0,
                'last_sent': None
            }
    
    async def send_notification(self, notification) -> bool:
        """Send webhook notification"""
        try:
            success_count = 0
            total_webhooks = 0
            
            # Send to all enabled webhooks
            for webhook_name, webhook_config in self.webhooks.items():
                if webhook_config.get('enabled', False):
                    total_webhooks += 1
                    
                    if await self._send_webhook(webhook_name, webhook_config, notification):
                        success_count += 1
                        self.stats['webhook_stats'][webhook_name]['sent'] += 1
                        self.stats['webhook_stats'][webhook_name]['last_sent'] = datetime.now()
                    else:
                        self.stats['webhook_stats'][webhook_name]['failed'] += 1
            
            success = success_count > 0
            
            if success:
                self.stats['sent'] += 1
                self.stats['last_sent'] = datetime.now()
                self.logger.info(f"Webhook notification sent to {success_count}/{total_webhooks} endpoints: {notification.title}")
            else:
                self.stats['failed'] += 1
                self.logger.error(f"Failed to send webhook notification: {notification.title}")
            
            return success
            
        except Exception as e:
            self.stats['failed'] += 1
            self.stats['last_error'] = str(e)
            self.logger.error(f"Error sending webhook notification: {e}")
            return False
    
    async def _send_webhook(self, webhook_name: str, webhook_config: Dict[str, Any], notification) -> bool:
        """Send notification to specific webhook"""
        try:
            # Format payload based on webhook type
            payload = self._format_payload(webhook_config['format'], notification)
            
            # Prepare headers
            headers = {
                'User-Agent': self.webhook_config['user_agent'],
                'Content-Type': 'application/json'
            }
            
            # Add custom headers if specified
            if 'headers' in webhook_config:
                headers.update(webhook_config['headers'])
            
            # Send webhook with retries
            for attempt in range(self.webhook_config['retry_attempts']):
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(
                            webhook_config['url'],
                            json=payload,
                            headers=headers,
                            timeout=aiohttp.ClientTimeout(total=self.webhook_config['timeout']),
                            ssl=self.webhook_config['verify_ssl']
                        ) as response:
                            
                            if response.status in [200, 201, 202, 204]:
                                self.logger.info(f"Webhook sent successfully to {webhook_name}")
                                return True
                            else:
                                self.logger.warning(f"Webhook {webhook_name} returned status {response.status}")
                                
                                # Don't retry for client errors (4xx)
                                if 400 <= response.status < 500:
                                    return False
                
                except asyncio.TimeoutError:
                    self.logger.warning(f"Webhook {webhook_name} timeout (attempt {attempt + 1})")
                except Exception as e:
                    self.logger.warning(f"Webhook {webhook_name} error (attempt {attempt + 1}): {e}")
                
                # Wait before retry
                if attempt < self.webhook_config['retry_attempts'] - 1:
                    await asyncio.sleep(self.webhook_config['retry_delay'])
            
            self.logger.error(f"Failed to send webhook to {webhook_name} after {self.webhook_config['retry_attempts']} attempts")
            return False
            
        except Exception as e:
            self.logger.error(f"Error sending webhook to {webhook_name}: {e}")
            return False
    
    def _format_payload(self, format_type: str, notification) -> Dict[str, Any]:
        """Format notification payload for specific webhook type"""
        try:
            if format_type == 'slack':
                return self._format_slack_payload(notification)
            elif format_type == 'discord':
                return self._format_discord_payload(notification)
            elif format_type == 'teams':
                return self._format_teams_payload(notification)
            else:
                return self._format_json_payload(notification)
                
        except Exception as e:
            self.logger.error(f"Error formatting payload for {format_type}: {e}")
            return self._format_json_payload(notification)
    
    def _format_slack_payload(self, notification) -> Dict[str, Any]:
        """Format payload for Slack webhook"""
        # Get color based on priority
        color_map = {
            'low': '#36a64f',      # Green
            'medium': '#ffcc00',   # Yellow
            'high': '#ff9500',     # Orange
            'critical': '#ff0000', # Red
            'emergency': '#800080' # Purple
        }
        
        color = color_map.get(notification.priority.value, '#36a64f')
        
        # Create Slack attachment
        attachment = {
            'color': color,
            'title': notification.title,
            'text': notification.message,
            'fields': [
                {
                    'title': 'Priority',
                    'value': notification.priority.value.upper(),
                    'short': True
                },
                {
                    'title': 'Category',
                    'value': notification.category,
                    'short': True
                },
                {
                    'title': 'Timestamp',
                    'value': notification.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    'short': True
                }
            ],
            'footer': 'Cyber Shield Pro',
            'ts': int(notification.timestamp.timestamp())
        }
        
        # Add details if available
        if notification.details:
            for key, value in notification.details.items():
                attachment['fields'].append({
                    'title': key.replace('_', ' ').title(),
                    'value': str(value),
                    'short': True
                })
        
        return {
            'text': f'🛡️ Cyber Shield Pro Alert',
            'attachments': [attachment]
        }
    
    def _format_discord_payload(self, notification) -> Dict[str, Any]:
        """Format payload for Discord webhook"""
        # Get color based on priority
        color_map = {
            'low': 0x36a64f,      # Green
            'medium': 0xffcc00,   # Yellow
            'high': 0xff9500,     # Orange
            'critical': 0xff0000, # Red
            'emergency': 0x800080 # Purple
        }
        
        color = color_map.get(notification.priority.value, 0x36a64f)
        
        # Create Discord embed
        embed = {
            'title': f'🛡️ {notification.title}',
            'description': notification.message,
            'color': color,
            'timestamp': notification.timestamp.isoformat(),
            'footer': {
                'text': 'Cyber Shield Pro'
            },
            'fields': [
                {
                    'name': 'Priority',
                    'value': notification.priority.value.upper(),
                    'inline': True
                },
                {
                    'name': 'Category',
                    'value': notification.category,
                    'inline': True
                }
            ]
        }
        
        # Add details if available
        if notification.details:
            for key, value in notification.details.items():
                embed['fields'].append({
                    'name': key.replace('_', ' ').title(),
                    'value': str(value),
                    'inline': True
                })
        
        return {
            'embeds': [embed]
        }
    
    def _format_teams_payload(self, notification) -> Dict[str, Any]:
        """Format payload for Microsoft Teams webhook"""
        # Get color based on priority
        color_map = {
            'low': '36a64f',      # Green
            'medium': 'ffcc00',   # Yellow
            'high': 'ff9500',     # Orange
            'critical': 'ff0000', # Red
            'emergency': '800080' # Purple
        }
        
        color = color_map.get(notification.priority.value, '36a64f')
        
        # Create Teams card
        card = {
            '@type': 'MessageCard',
            '@context': 'http://schema.org/extensions',
            'themeColor': color,
            'summary': notification.title,
            'sections': [
                {
                    'activityTitle': f'🛡️ Cyber Shield Pro Alert',
                    'activitySubtitle': notification.title,
                    'activityImage': 'https://example.com/cyber-shield-icon.png',
                    'facts': [
                        {
                            'name': 'Priority',
                            'value': notification.priority.value.upper()
                        },
                        {
                            'name': 'Category',
                            'value': notification.category
                        },
                        {
                            'name': 'Timestamp',
                            'value': notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                        }
                    ],
                    'markdown': True,
                    'text': notification.message
                }
            ]
        }
        
        # Add details if available
        if notification.details:
            for key, value in notification.details.items():
                card['sections'][0]['facts'].append({
                    'name': key.replace('_', ' ').title(),
                    'value': str(value)
                })
        
        return card
    
    def _format_json_payload(self, notification) -> Dict[str, Any]:
        """Format payload as generic JSON"""
        return {
            'source': 'Cyber Shield Pro',
            'timestamp': notification.timestamp.isoformat(),
            'notification': {
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'priority': notification.priority.value,
                'category': notification.category,
                'details': notification.details,
                'channels': [channel.value for channel in notification.channels],
                'recipients': notification.recipients
            }
        }
    
    def add_webhook(self, name: str, url: str, format_type: str = 'json', 
                   headers: Dict[str, str] = None, enabled: bool = True):
        """Add a new webhook endpoint"""
        self.webhooks[name] = {
            'url': url,
            'format': format_type,
            'enabled': enabled,
            'headers': headers or {}
        }
        
        # Initialize stats
        self.stats['webhook_stats'][name] = {
            'sent': 0,
            'failed': 0,
            'last_sent': None
        }
        
        self.logger.info(f"Webhook added: {name}")
    
    def remove_webhook(self, name: str):
        """Remove a webhook endpoint"""
        if name in self.webhooks:
            del self.webhooks[name]
            if name in self.stats['webhook_stats']:
                del self.stats['webhook_stats'][name]
            self.logger.info(f"Webhook removed: {name}")
    
    def enable_webhook(self, name: str):
        """Enable a webhook endpoint"""
        if name in self.webhooks:
            self.webhooks[name]['enabled'] = True
            self.logger.info(f"Webhook enabled: {name}")
    
    def disable_webhook(self, name: str):
        """Disable a webhook endpoint"""
        if name in self.webhooks:
            self.webhooks[name]['enabled'] = False
            self.logger.info(f"Webhook disabled: {name}")
    
    async def test_webhook(self, name: str) -> bool:
        """Test a specific webhook endpoint"""
        try:
            if name not in self.webhooks:
                self.logger.error(f"Webhook not found: {name}")
                return False
            
            # Create test notification
            class TestNotification:
                def __init__(self):
                    self.id = 'test-notification'
                    self.title = 'Test Notification'
                    self.message = 'This is a test notification from Cyber Shield Pro'
                    self.priority = type('Priority', (), {'value': 'medium'})()
                    self.category = 'test'
                    self.timestamp = datetime.now()
                    self.details = {'test': True}
                    self.channels = []
                    self.recipients = []
            
            test_notification = TestNotification()
            webhook_config = self.webhooks[name]
            
            # Send test webhook
            success = await self._send_webhook(name, webhook_config, test_notification)
            
            if success:
                self.logger.info(f"Webhook test successful: {name}")
            else:
                self.logger.error(f"Webhook test failed: {name}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error testing webhook {name}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get webhook notification statistics"""
        return self.stats.copy()
    
    def get_webhook_list(self) -> Dict[str, Dict[str, Any]]:
        """Get list of configured webhooks"""
        webhook_list = {}
        for name, config in self.webhooks.items():
            webhook_list[name] = {
                'url': config['url'],
                'format': config['format'],
                'enabled': config['enabled'],
                'stats': self.stats['webhook_stats'].get(name, {})
            }
        return webhook_list
