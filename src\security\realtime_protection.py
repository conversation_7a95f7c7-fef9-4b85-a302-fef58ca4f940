#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real-Time Protection System for Cyber Shield Pro
Advanced real-time monitoring and protection with admin privileges
"""

import os
import sys
import time
import threading
import ctypes
import winreg
import subprocess
import psutil
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Callable, Optional
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
from .threat_detector import ThreatDetector
from .process_monitor import ProcessMonitor
from ..database.threat_manager import ThreatManager
from ..database.log_manager import LogManager
from ..utils.logger import Logger

class RealTimeProtection:
    """Advanced Real-Time Protection System"""
    
    def __init__(self, threat_manager: Threat<PERSON>ana<PERSON>, log_manager: LogManager, logger: Logger):
        """Initialize real-time protection"""
        self.threat_manager = threat_manager
        self.log_manager = log_manager
        self.logger = logger
        
        # Protection components
        self.threat_detector = ThreatDetector()
        self.process_monitor = ProcessMonitor(self.threat_detector)
        self.file_monitor = FileSystemMonitor(self.threat_detector)
        self.registry_monitor = RegistryMonitor()
        self.network_monitor = NetworkMonitor()
        
        # Protection state
        self.is_active = False
        self.protection_level = "high"  # low, medium, high, maximum
        self.auto_quarantine = True
        self.auto_block = True
        
        # Monitoring threads
        self.monitor_threads = []
        self.callbacks = {}
        
        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()
        if not self.has_admin_rights:
            self.logger.warning("Running without admin privileges - some features may be limited")
        
        # Protected directories
        self.protected_dirs = [
            "C:\\Windows\\System32",
            "C:\\Windows\\SysWOW64",
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            os.path.expanduser("~/Documents"),
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Downloads"),
        ]
        
        # Exclusions
        self.exclusions = {
            'processes': ['System', 'csrss.exe', 'winlogon.exe', 'services.exe'],
            'files': ['.tmp', '.log', '.cache'],
            'directories': ['C:\\Windows\\Temp', 'C:\\Windows\\Logs']
        }
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def request_admin_privileges(self) -> bool:
        """Request administrator privileges"""
        try:
            if self.has_admin_rights:
                return True
            
            # Re-run as administrator
            ctypes.windll.shell32.ShellExecuteW(
                None, "runas", sys.executable, " ".join(sys.argv), None, 1
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to request admin privileges: {e}")
            return False
    
    def start_protection(self, user_id: int, callbacks: Dict[str, Callable] = None) -> bool:
        """Start real-time protection"""
        try:
            if self.is_active:
                return True
            
            self.callbacks = callbacks or {}
            
            # Start file system monitoring
            if not self.file_monitor.start_monitoring(self.protected_dirs, self._on_file_event):
                self.logger.error("Failed to start file system monitoring")
                return False
            
            # Start process monitoring
            if not self.process_monitor.start_monitoring(self._on_process_event):
                self.logger.error("Failed to start process monitoring")
                return False
            
            # Start registry monitoring
            if self.has_admin_rights:
                if not self.registry_monitor.start_monitoring(self._on_registry_event):
                    self.logger.warning("Failed to start registry monitoring")
            
            # Start network monitoring
            if not self.network_monitor.start_monitoring(self._on_network_event):
                self.logger.warning("Failed to start network monitoring")
            
            # Start system integrity monitoring
            self._start_system_monitoring()
            
            self.is_active = True
            self.log_manager.log_info(user_id, 'security', 'Real-time protection started')
            
            if self.callbacks.get('on_protection_started'):
                self.callbacks['on_protection_started']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start real-time protection: {e}")
            return False
    
    def stop_protection(self) -> bool:
        """Stop real-time protection"""
        try:
            if not self.is_active:
                return True
            
            # Stop all monitoring
            self.file_monitor.stop_monitoring()
            self.process_monitor.stop_monitoring()
            self.registry_monitor.stop_monitoring()
            self.network_monitor.stop_monitoring()
            
            # Stop monitoring threads
            for thread in self.monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=5)
            
            self.is_active = False
            self.logger.info("Real-time protection stopped")
            
            if self.callbacks.get('on_protection_stopped'):
                self.callbacks['on_protection_stopped']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop real-time protection: {e}")
            return False
    
    def _on_file_event(self, event_data: Dict[str, Any]):
        """Handle file system events"""
        try:
            file_path = event_data.get('file_path')
            event_type = event_data.get('event_type')
            
            if not file_path or self._is_excluded_file(file_path):
                return
            
            # Analyze file for threats
            if event_type in ['created', 'modified']:
                threat_result = self.threat_detector.analyze_file(file_path)
                
                if threat_result['is_threat']:
                    self._handle_file_threat(file_path, threat_result)
            
        except Exception as e:
            self.logger.error(f"Error handling file event: {e}")
    
    def _on_process_event(self, event_data: Dict[str, Any]):
        """Handle process events"""
        try:
            process_info = event_data.get('process_info')
            event_type = event_data.get('event_type')
            
            if not process_info or self._is_excluded_process(process_info.get('name', '')):
                return
            
            if event_type == 'process_started':
                self._analyze_new_process(process_info)
            elif event_type == 'suspicious_behavior':
                self._handle_suspicious_process(process_info, event_data.get('indicators', []))
            
        except Exception as e:
            self.logger.error(f"Error handling process event: {e}")
    
    def _on_registry_event(self, event_data: Dict[str, Any]):
        """Handle registry events"""
        try:
            key_path = event_data.get('key_path')
            value_name = event_data.get('value_name')
            event_type = event_data.get('event_type')
            
            # Check for suspicious registry modifications
            if self._is_suspicious_registry_change(key_path, value_name, event_type):
                self._handle_registry_threat(event_data)
            
        except Exception as e:
            self.logger.error(f"Error handling registry event: {e}")
    
    def _on_network_event(self, event_data: Dict[str, Any]):
        """Handle network events"""
        try:
            connection_info = event_data.get('connection_info')
            event_type = event_data.get('event_type')
            
            if event_type == 'suspicious_connection':
                self._handle_network_threat(connection_info)
            elif event_type == 'malicious_domain':
                self._block_malicious_domain(connection_info)
            
        except Exception as e:
            self.logger.error(f"Error handling network event: {e}")
    
    def _analyze_new_process(self, process_info: Dict[str, Any]):
        """Analyze newly started process"""
        try:
            exe_path = process_info.get('exe')
            if not exe_path or not os.path.exists(exe_path):
                return
            
            # Analyze executable file
            threat_result = self.threat_detector.analyze_file(exe_path)
            
            if threat_result['is_threat']:
                # Terminate malicious process
                pid = process_info.get('pid')
                if pid and self.auto_block:
                    self._terminate_process(pid, threat_result['threat_name'])
                
                # Log threat
                self.threat_manager.add_threat(
                    user_id=1,  # System user
                    threat_type=threat_result['threat_type'],
                    threat_name=threat_result['threat_name'],
                    file_path=exe_path,
                    severity=threat_result['severity']
                )
            
        except Exception as e:
            self.logger.error(f"Error analyzing new process: {e}")
    
    def _handle_file_threat(self, file_path: str, threat_result: Dict[str, Any]):
        """Handle detected file threat"""
        try:
            threat_name = threat_result['threat_name']
            severity = threat_result['severity']
            
            self.logger.warning(f"File threat detected: {file_path} - {threat_name}")
            
            # Auto-quarantine if enabled
            if self.auto_quarantine and severity in ['high', 'critical']:
                quarantine_path = self._quarantine_file(file_path)
                if quarantine_path:
                    self.logger.info(f"File quarantined: {file_path} -> {quarantine_path}")
            
            # Add to threat database
            threat_id = self.threat_manager.add_threat(
                user_id=1,
                threat_type=threat_result['threat_type'],
                threat_name=threat_name,
                file_path=file_path,
                severity=severity
            )
            
            # Notify callback
            if self.callbacks.get('on_threat_detected'):
                self.callbacks['on_threat_detected']({
                    'type': 'file_threat',
                    'file_path': file_path,
                    'threat_name': threat_name,
                    'severity': severity,
                    'threat_id': threat_id
                })
            
        except Exception as e:
            self.logger.error(f"Error handling file threat: {e}")
    
    def _handle_suspicious_process(self, process_info: Dict[str, Any], indicators: List[str]):
        """Handle suspicious process behavior"""
        try:
            pid = process_info.get('pid')
            name = process_info.get('name', 'Unknown')
            
            self.logger.warning(f"Suspicious process detected: {name} (PID: {pid})")
            
            # Terminate if high risk
            if self.auto_block and any('high_risk' in indicator for indicator in indicators):
                self._terminate_process(pid, f"Suspicious behavior: {', '.join(indicators)}")
            
            # Notify callback
            if self.callbacks.get('on_threat_detected'):
                self.callbacks['on_threat_detected']({
                    'type': 'suspicious_process',
                    'process_info': process_info,
                    'indicators': indicators
                })
            
        except Exception as e:
            self.logger.error(f"Error handling suspicious process: {e}")
    
    def _handle_registry_threat(self, event_data: Dict[str, Any]):
        """Handle registry threat"""
        try:
            key_path = event_data.get('key_path')
            
            self.logger.warning(f"Suspicious registry modification: {key_path}")
            
            # Revert changes if possible
            if self.auto_block and self.has_admin_rights:
                self._revert_registry_change(event_data)
            
            # Notify callback
            if self.callbacks.get('on_threat_detected'):
                self.callbacks['on_threat_detected']({
                    'type': 'registry_threat',
                    'event_data': event_data
                })
            
        except Exception as e:
            self.logger.error(f"Error handling registry threat: {e}")
    
    def _handle_network_threat(self, connection_info: Dict[str, Any]):
        """Handle network threat"""
        try:
            remote_ip = connection_info.get('remote_ip')
            
            self.logger.warning(f"Suspicious network connection: {remote_ip}")
            
            # Block connection if auto-block enabled
            if self.auto_block:
                self._block_ip_address(remote_ip)
            
            # Notify callback
            if self.callbacks.get('on_threat_detected'):
                self.callbacks['on_threat_detected']({
                    'type': 'network_threat',
                    'connection_info': connection_info
                })
            
        except Exception as e:
            self.logger.error(f"Error handling network threat: {e}")
    
    def _terminate_process(self, pid: int, reason: str) -> bool:
        """Terminate a process with admin privileges"""
        try:
            if not self.has_admin_rights:
                return False
            
            process = psutil.Process(pid)
            process.terminate()
            
            # Wait for termination
            process.wait(timeout=10)
            
            self.logger.info(f"Process {pid} terminated: {reason}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to terminate process {pid}: {e}")
            return False
    
    def _quarantine_file(self, file_path: str) -> Optional[str]:
        """Quarantine a malicious file"""
        try:
            quarantine_dir = Path("temp/quarantine")
            quarantine_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate unique quarantine filename
            import uuid
            file_hash = hashlib.md5(file_path.encode()).hexdigest()[:8]
            quarantine_name = f"{uuid.uuid4().hex[:8]}_{file_hash}_{Path(file_path).name}"
            quarantine_path = quarantine_dir / quarantine_name
            
            # Move file to quarantine
            import shutil
            shutil.move(file_path, quarantine_path)
            
            return str(quarantine_path)
            
        except Exception as e:
            self.logger.error(f"Failed to quarantine file {file_path}: {e}")
            return None
    
    def _block_ip_address(self, ip_address: str) -> bool:
        """Block IP address using Windows Firewall"""
        try:
            if not self.has_admin_rights:
                return False
            
            # Add firewall rule to block IP
            cmd = [
                'netsh', 'advfirewall', 'firewall', 'add', 'rule',
                f'name=CyberShield_Block_{ip_address}',
                'dir=out', 'action=block',
                f'remoteip={ip_address}'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"IP address blocked: {ip_address}")
                return True
            else:
                self.logger.error(f"Failed to block IP {ip_address}: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error blocking IP address {ip_address}: {e}")
            return False
    
    def _is_excluded_file(self, file_path: str) -> bool:
        """Check if file should be excluded from monitoring"""
        try:
            file_ext = Path(file_path).suffix.lower()
            if file_ext in self.exclusions['files']:
                return True
            
            for excluded_dir in self.exclusions['directories']:
                if file_path.startswith(excluded_dir):
                    return True
            
            return False
            
        except:
            return False
    
    def _is_excluded_process(self, process_name: str) -> bool:
        """Check if process should be excluded from monitoring"""
        return process_name.lower() in [p.lower() for p in self.exclusions['processes']]
    
    def _is_suspicious_registry_change(self, key_path: str, value_name: str, event_type: str) -> bool:
        """Check if registry change is suspicious"""
        suspicious_keys = [
            'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run',
            'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce',
            'SYSTEM\\CurrentControlSet\\Services',
            'SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon'
        ]
        
        return any(sus_key in key_path for sus_key in suspicious_keys)
    
    def _start_system_monitoring(self):
        """Start system integrity monitoring"""
        def monitor_system():
            while self.is_active:
                try:
                    # Monitor system files
                    self._check_system_integrity()
                    
                    # Monitor startup programs
                    self._check_startup_programs()
                    
                    # Monitor services
                    self._check_system_services()
                    
                    time.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        monitor_thread.start()
        self.monitor_threads.append(monitor_thread)
    
    def _check_system_integrity(self):
        """Check system file integrity"""
        # This would implement system file checking
        pass
    
    def _check_startup_programs(self):
        """Check for suspicious startup programs"""
        # This would check registry startup entries
        pass
    
    def _check_system_services(self):
        """Check for suspicious system services"""
        # This would monitor Windows services
        pass
    
    def get_protection_status(self) -> Dict[str, Any]:
        """Get current protection status"""
        return {
            'is_active': self.is_active,
            'protection_level': self.protection_level,
            'has_admin_rights': self.has_admin_rights,
            'auto_quarantine': self.auto_quarantine,
            'auto_block': self.auto_block,
            'monitored_directories': len(self.protected_dirs),
            'active_monitors': len([t for t in self.monitor_threads if t.is_alive()])
        }
    
    def set_protection_level(self, level: str):
        """Set protection level"""
        if level in ['low', 'medium', 'high', 'maximum']:
            self.protection_level = level
            self._update_protection_settings()
    
    def _update_protection_settings(self):
        """Update protection settings based on level"""
        if self.protection_level == 'low':
            self.auto_quarantine = False
            self.auto_block = False
        elif self.protection_level == 'medium':
            self.auto_quarantine = True
            self.auto_block = False
        elif self.protection_level == 'high':
            self.auto_quarantine = True
            self.auto_block = True
        elif self.protection_level == 'maximum':
            self.auto_quarantine = True
            self.auto_block = True
            # Add more aggressive settings


class FileSystemMonitor:
    """File system monitoring component"""
    
    def __init__(self, threat_detector: ThreatDetector):
        self.threat_detector = threat_detector
        self.observer = None
        self.handlers = []
        self.is_monitoring = False
    
    def start_monitoring(self, directories: List[str], callback: Callable) -> bool:
        """Start file system monitoring"""
        try:
            self.observer = Observer()
            
            for directory in directories:
                if os.path.exists(directory):
                    handler = FileEventHandler(callback, self.threat_detector)
                    self.observer.schedule(handler, directory, recursive=True)
                    self.handlers.append(handler)
            
            self.observer.start()
            self.is_monitoring = True
            return True
            
        except Exception as e:
            print(f"Failed to start file system monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop file system monitoring"""
        if self.observer:
            self.observer.stop()
            self.observer.join()
        self.is_monitoring = False


class FileEventHandler(FileSystemEventHandler):
    """File system event handler"""
    
    def __init__(self, callback: Callable, threat_detector: ThreatDetector):
        self.callback = callback
        self.threat_detector = threat_detector
    
    def on_created(self, event):
        if not event.is_directory:
            self.callback({
                'file_path': event.src_path,
                'event_type': 'created'
            })
    
    def on_modified(self, event):
        if not event.is_directory:
            self.callback({
                'file_path': event.src_path,
                'event_type': 'modified'
            })


class RegistryMonitor:
    """Windows Registry monitoring component"""
    
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, callback: Callable) -> bool:
        """Start registry monitoring"""
        try:
            self.callback = callback
            self.monitor_thread = threading.Thread(target=self._monitor_registry, daemon=True)
            self.monitor_thread.start()
            self.is_monitoring = True
            return True
        except Exception as e:
            print(f"Failed to start registry monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop registry monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_registry(self):
        """Monitor registry changes"""
        # This would implement registry monitoring using Windows API
        pass


class NetworkMonitor:
    """Network monitoring component"""
    
    def __init__(self):
        self.is_monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self, callback: Callable) -> bool:
        """Start network monitoring"""
        try:
            self.callback = callback
            self.monitor_thread = threading.Thread(target=self._monitor_network, daemon=True)
            self.monitor_thread.start()
            self.is_monitoring = True
            return True
        except Exception as e:
            print(f"Failed to start network monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop network monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
    
    def _monitor_network(self):
        """Monitor network connections"""
        while self.is_monitoring:
            try:
                # Monitor network connections using psutil
                connections = psutil.net_connections()
                for conn in connections:
                    if conn.raddr:
                        # Check for suspicious connections
                        self._check_connection(conn)
                
                time.sleep(5)
                
            except Exception as e:
                print(f"Error in network monitoring: {e}")
                time.sleep(10)
    
    def _check_connection(self, connection):
        """Check if connection is suspicious"""
        # This would implement connection analysis
        pass
