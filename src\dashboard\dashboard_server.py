#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard Server for Cyber Shield Pro
High-performance web server for the admin dashboard
"""

import os
import ssl
import threading
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
import uvicorn
from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
import jwt
from ..utils.logger import Logger

class DashboardServer:
    """High-performance dashboard server with WebSocket support"""
    
    def __init__(self, logger: Logger):
        """Initialize dashboard server"""
        self.logger = logger
        
        # FastAPI app
        self.app = FastAPI(
            title="Cyber Shield Pro Dashboard",
            description="Advanced Cybersecurity Monitoring Dashboard",
            version="1.0.0"
        )
        
        # Security
        self.security = HTTPBearer()
        self.jwt_secret = "cyber_shield_pro_secret_key_2025"  # Should be from config
        self.jwt_algorithm = "HS256"
        
        # WebSocket connections
        self.active_connections: List[WebSocket] = []
        self.connection_manager = ConnectionManager()
        
        # Server configuration
        self.config = {
            'host': '0.0.0.0',
            'port': 8443,
            'ssl_enabled': True,
            'ssl_cert_path': 'certs/dashboard.crt',
            'ssl_key_path': 'certs/dashboard.key',
            'workers': 1,
            'reload': False
        }
        
        # Dashboard data
        self.dashboard_data = {
            'stats': {},
            'threats': [],
            'alerts': [],
            'system_status': {},
            'network_status': {}
        }
        
        # Setup middleware and routes
        self._setup_middleware()
        self._setup_routes()
        self._setup_websocket_routes()
        
        # Background tasks
        self.background_tasks = []
        self.is_running = False
    
    def _setup_middleware(self):
        """Setup FastAPI middleware"""
        
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],  # Configure appropriately for production
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Static files
        if os.path.exists("static"):
            self.app.mount("/static", StaticFiles(directory="static"), name="static")
    
    def _setup_routes(self):
        """Setup API routes"""
        
        @self.app.get("/")
        async def dashboard_home():
            """Serve dashboard home page"""
            return HTMLResponse(self._get_dashboard_html())
        
        @self.app.get("/health")
        async def health_check():
            """Health check endpoint"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        
        @self.app.post("/api/auth/login")
        async def login(credentials: dict):
            """User authentication"""
            try:
                username = credentials.get("username")
                password = credentials.get("password")
                
                # Validate credentials (implement proper authentication)
                if self._validate_credentials(username, password):
                    token = self._generate_jwt_token(username)
                    return {
                        "access_token": token,
                        "token_type": "bearer",
                        "user": {
                            "username": username,
                            "role": self._get_user_role(username)
                        }
                    }
                else:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail="Invalid credentials"
                    )
                    
            except Exception as e:
                self.logger.error(f"Login error: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Authentication error"
                )
        
        @self.app.get("/api/dashboard/stats")
        async def get_dashboard_stats(current_user: dict = Depends(self._get_current_user)):
            """Get dashboard statistics"""
            try:
                return self.dashboard_data['stats']
            except Exception as e:
                self.logger.error(f"Error getting dashboard stats: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error retrieving dashboard statistics"
                )
        
        @self.app.get("/api/threats")
        async def get_threats(
            limit: int = 50,
            current_user: dict = Depends(self._get_current_user)
        ):
            """Get threat data"""
            try:
                return self.dashboard_data['threats'][:limit]
            except Exception as e:
                self.logger.error(f"Error getting threats: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error retrieving threat data"
                )
        
        @self.app.get("/api/alerts")
        async def get_alerts(current_user: dict = Depends(self._get_current_user)):
            """Get dashboard alerts"""
            try:
                return self.dashboard_data['alerts']
            except Exception as e:
                self.logger.error(f"Error getting alerts: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error retrieving alerts"
                )
        
        @self.app.post("/api/alerts/{alert_id}/acknowledge")
        async def acknowledge_alert(
            alert_id: str,
            current_user: dict = Depends(self._get_current_user)
        ):
            """Acknowledge an alert"""
            try:
                # Find and acknowledge alert
                for alert in self.dashboard_data['alerts']:
                    if alert.get('id') == alert_id:
                        alert['acknowledged'] = True
                        alert['acknowledged_by'] = current_user['username']
                        alert['acknowledged_at'] = datetime.now().isoformat()
                        
                        # Broadcast update
                        await self.connection_manager.broadcast({
                            'type': 'alert_acknowledged',
                            'alert_id': alert_id,
                            'acknowledged_by': current_user['username']
                        })
                        
                        return {"success": True}
                
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Alert not found"
                )
                
            except Exception as e:
                self.logger.error(f"Error acknowledging alert: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error acknowledging alert"
                )
        
        @self.app.get("/api/system/status")
        async def get_system_status(current_user: dict = Depends(self._get_current_user)):
            """Get system status"""
            try:
                return self.dashboard_data['system_status']
            except Exception as e:
                self.logger.error(f"Error getting system status: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error retrieving system status"
                )
        
        @self.app.post("/api/system/control")
        async def system_control(
            action_data: dict,
            current_user: dict = Depends(self._get_current_user)
        ):
            """Execute system control action"""
            try:
                # Check permissions
                if not self._check_permission(current_user, 'system_control'):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Insufficient permissions"
                    )
                
                action = action_data.get('action')
                target = action_data.get('target')
                
                # Execute action (implement actual system control)
                result = await self._execute_system_action(action, target, action_data)
                
                # Log action
                self.logger.info(f"System control action: {action} on {target} by {current_user['username']}")
                
                return result
                
            except Exception as e:
                self.logger.error(f"Error executing system control: {e}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Error executing system control"
                )
    
    def _setup_websocket_routes(self):
        """Setup WebSocket routes"""
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await self.connection_manager.connect(websocket)
            try:
                while True:
                    # Receive messages from client
                    data = await websocket.receive_text()
                    message = eval(data)  # Use proper JSON parsing in production
                    
                    # Handle different message types
                    if message.get('type') == 'subscribe':
                        # Subscribe to specific data streams
                        await self._handle_subscription(websocket, message)
                    elif message.get('type') == 'request_update':
                        # Send immediate update
                        await self._send_dashboard_update(websocket)
                    
            except WebSocketDisconnect:
                self.connection_manager.disconnect(websocket)
            except Exception as e:
                self.logger.error(f"WebSocket error: {e}")
                self.connection_manager.disconnect(websocket)
    
    async def _handle_subscription(self, websocket: WebSocket, message: dict):
        """Handle WebSocket subscription"""
        try:
            subscription_type = message.get('subscription')
            
            if subscription_type == 'dashboard_stats':
                await websocket.send_text(str({
                    'type': 'dashboard_stats',
                    'data': self.dashboard_data['stats']
                }))
            elif subscription_type == 'threats':
                await websocket.send_text(str({
                    'type': 'threats',
                    'data': self.dashboard_data['threats']
                }))
            elif subscription_type == 'alerts':
                await websocket.send_text(str({
                    'type': 'alerts',
                    'data': self.dashboard_data['alerts']
                }))
                
        except Exception as e:
            self.logger.error(f"Error handling subscription: {e}")
    
    async def _send_dashboard_update(self, websocket: WebSocket):
        """Send complete dashboard update"""
        try:
            update = {
                'type': 'dashboard_update',
                'timestamp': datetime.now().isoformat(),
                'data': {
                    'stats': self.dashboard_data['stats'],
                    'threats': self.dashboard_data['threats'][:20],  # Limit data
                    'alerts': self.dashboard_data['alerts'][:10],
                    'system_status': self.dashboard_data['system_status']
                }
            }
            
            await websocket.send_text(str(update))
            
        except Exception as e:
            self.logger.error(f"Error sending dashboard update: {e}")
    
    def _validate_credentials(self, username: str, password: str) -> bool:
        """Validate user credentials"""
        # Implement proper authentication
        # This is a simplified version
        valid_users = {
            'admin': 'CyberShield2025!',
            'operator': 'Operator2025!',
            'viewer': 'Viewer2025!'
        }
        
        return username in valid_users and valid_users[username] == password
    
    def _get_user_role(self, username: str) -> str:
        """Get user role"""
        user_roles = {
            'admin': 'admin',
            'operator': 'operator',
            'viewer': 'viewer'
        }
        
        return user_roles.get(username, 'viewer')
    
    def _generate_jwt_token(self, username: str) -> str:
        """Generate JWT token"""
        payload = {
            'username': username,
            'role': self._get_user_role(username),
            'exp': datetime.utcnow().timestamp() + 3600,  # 1 hour expiry
            'iat': datetime.utcnow().timestamp()
        }
        
        return jwt.encode(payload, self.jwt_secret, algorithm=self.jwt_algorithm)
    
    async def _get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())):
        """Get current authenticated user"""
        try:
            token = credentials.credentials
            payload = jwt.decode(token, self.jwt_secret, algorithms=[self.jwt_algorithm])
            
            username = payload.get('username')
            role = payload.get('role')
            
            if not username:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            return {
                'username': username,
                'role': role
            }
            
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token expired"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def _check_permission(self, user: dict, permission: str) -> bool:
        """Check user permissions"""
        role_permissions = {
            'admin': ['view_dashboard', 'manage_threats', 'system_control', 'generate_reports'],
            'operator': ['view_dashboard', 'manage_threats', 'generate_reports'],
            'viewer': ['view_dashboard']
        }
        
        user_role = user.get('role', 'viewer')
        return permission in role_permissions.get(user_role, [])
    
    async def _execute_system_action(self, action: str, target: str, data: dict) -> dict:
        """Execute system action"""
        # Implement actual system control actions
        # This is a placeholder
        
        allowed_actions = [
            'restart_service', 'stop_service', 'start_service',
            'block_ip', 'unblock_ip', 'terminate_process',
            'update_firewall_rule', 'scan_system'
        ]
        
        if action not in allowed_actions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Action not allowed"
            )
        
        # Simulate action execution
        await asyncio.sleep(1)  # Simulate processing time
        
        return {
            'success': True,
            'action': action,
            'target': target,
            'timestamp': datetime.now().isoformat(),
            'message': f'Action {action} executed on {target}'
        }
    
    def _get_dashboard_html(self) -> str:
        """Get dashboard HTML content"""
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Cyber Shield Pro - Dashboard</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 0;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    min-height: 100vh;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header {
                    text-align: center;
                    margin-bottom: 40px;
                }
                .logo {
                    font-size: 2.5em;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .subtitle {
                    font-size: 1.2em;
                    opacity: 0.9;
                }
                .dashboard-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 20px;
                    margin-top: 30px;
                }
                .card {
                    background: rgba(255, 255, 255, 0.1);
                    border-radius: 10px;
                    padding: 20px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                }
                .card h3 {
                    margin-top: 0;
                    color: #fff;
                }
                .status {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 10px 0;
                }
                .status-indicator {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: #4CAF50;
                }
                .status-indicator.warning {
                    background: #FF9800;
                }
                .status-indicator.critical {
                    background: #F44336;
                }
                .btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: none;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    margin: 5px;
                }
                .btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="logo">🛡️ Cyber Shield Pro</div>
                    <div class="subtitle">Advanced Cybersecurity Monitoring Dashboard</div>
                </div>
                
                <div class="dashboard-grid">
                    <div class="card">
                        <h3>🚨 Threat Status</h3>
                        <div class="status">
                            <span>Active Threats</span>
                            <span id="active-threats">Loading...</span>
                        </div>
                        <div class="status">
                            <span>Critical Alerts</span>
                            <span id="critical-alerts">Loading...</span>
                        </div>
                        <div class="status">
                            <span>System Status</span>
                            <div class="status-indicator" id="system-status"></div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3>📊 System Performance</h3>
                        <div class="status">
                            <span>CPU Usage</span>
                            <span id="cpu-usage">Loading...</span>
                        </div>
                        <div class="status">
                            <span>Memory Usage</span>
                            <span id="memory-usage">Loading...</span>
                        </div>
                        <div class="status">
                            <span>Network Activity</span>
                            <span id="network-activity">Loading...</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3>🌐 Network Security</h3>
                        <div class="status">
                            <span>Blocked IPs</span>
                            <span id="blocked-ips">Loading...</span>
                        </div>
                        <div class="status">
                            <span>Firewall Status</span>
                            <div class="status-indicator" id="firewall-status"></div>
                        </div>
                        <div class="status">
                            <span>Active Connections</span>
                            <span id="active-connections">Loading...</span>
                        </div>
                    </div>
                    
                    <div class="card">
                        <h3>⚙️ Quick Actions</h3>
                        <button class="btn" onclick="generateReport()">📄 Generate Report</button>
                        <button class="btn" onclick="scanSystem()">🔍 System Scan</button>
                        <button class="btn" onclick="updateRules()">🔧 Update Rules</button>
                        <button class="btn" onclick="viewLogs()">📋 View Logs</button>
                    </div>
                </div>
            </div>
            
            <script>
                // WebSocket connection for real-time updates
                const ws = new WebSocket('ws://localhost:8443/ws');
                
                ws.onopen = function(event) {
                    console.log('Connected to dashboard');
                    // Request initial data
                    ws.send(JSON.stringify({type: 'request_update'}));
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    updateDashboard(data);
                };
                
                function updateDashboard(data) {
                    if (data.type === 'dashboard_update') {
                        const stats = data.data.stats;
                        
                        // Update threat status
                        document.getElementById('active-threats').textContent = stats.threats?.active || 0;
                        document.getElementById('critical-alerts').textContent = stats.alerts?.critical || 0;
                        
                        // Update system performance
                        document.getElementById('cpu-usage').textContent = (stats.system?.cpu_usage || 0) + '%';
                        document.getElementById('memory-usage').textContent = (stats.system?.memory_usage || 0) + '%';
                        document.getElementById('network-activity').textContent = (stats.network?.bandwidth_usage || 0) + ' MB/s';
                        
                        // Update network security
                        document.getElementById('blocked-ips').textContent = stats.network?.blocked_ips || 0;
                        document.getElementById('active-connections').textContent = stats.network?.active_connections || 0;
                        
                        // Update status indicators
                        updateStatusIndicator('system-status', stats.system?.health);
                        updateStatusIndicator('firewall-status', 'active');
                    }
                }
                
                function updateStatusIndicator(elementId, status) {
                    const indicator = document.getElementById(elementId);
                    indicator.className = 'status-indicator';
                    
                    if (status === 'critical' || status === 'error') {
                        indicator.classList.add('critical');
                    } else if (status === 'warning') {
                        indicator.classList.add('warning');
                    }
                }
                
                // Quick action functions
                function generateReport() {
                    alert('Report generation started...');
                }
                
                function scanSystem() {
                    alert('System scan initiated...');
                }
                
                function updateRules() {
                    alert('Security rules updated...');
                }
                
                function viewLogs() {
                    alert('Opening log viewer...');
                }
                
                // Auto-refresh every 5 seconds
                setInterval(() => {
                    if (ws.readyState === WebSocket.OPEN) {
                        ws.send(JSON.stringify({type: 'request_update'}));
                    }
                }, 5000);
            </script>
        </body>
        </html>
        """
    
    async def start_server(self):
        """Start the dashboard server"""
        try:
            self.is_running = True
            self.logger.info(f"Starting dashboard server on {self.config['host']}:{self.config['port']}")
            
            # SSL configuration
            ssl_context = None
            if self.config['ssl_enabled']:
                if os.path.exists(self.config['ssl_cert_path']) and os.path.exists(self.config['ssl_key_path']):
                    ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                    ssl_context.load_cert_chain(self.config['ssl_cert_path'], self.config['ssl_key_path'])
                else:
                    self.logger.warning("SSL certificates not found, running without SSL")
            
            # Start server
            config = uvicorn.Config(
                self.app,
                host=self.config['host'],
                port=self.config['port'],
                ssl_keyfile=self.config['ssl_key_path'] if ssl_context else None,
                ssl_certfile=self.config['ssl_cert_path'] if ssl_context else None,
                workers=self.config['workers'],
                reload=self.config['reload']
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            self.logger.error(f"Error starting dashboard server: {e}")
            self.is_running = False
            raise
    
    def update_dashboard_data(self, data_type: str, data: Any):
        """Update dashboard data"""
        try:
            self.dashboard_data[data_type] = data
            
            # Broadcast update to connected clients
            asyncio.create_task(self.connection_manager.broadcast({
                'type': f'{data_type}_update',
                'data': data,
                'timestamp': datetime.now().isoformat()
            }))
            
        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")


class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        """Accept WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket):
        """Remove WebSocket connection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception:
            self.disconnect(websocket)
    
    async def broadcast(self, message: dict):
        """Broadcast message to all connected WebSockets"""
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(str(message))
            except Exception:
                disconnected.append(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection)
