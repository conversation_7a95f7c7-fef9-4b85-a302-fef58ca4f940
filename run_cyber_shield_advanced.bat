@echo off
REM Cyber Shield Pro Advanced - Quick Start Script
REM This script helps you run Cyber Shield Pro Advanced with proper setup

title Cyber Shield Pro Advanced - Launcher

echo.
echo ================================================================
echo                🛡️  Cyber Shield Pro Advanced 🛡️
echo                Advanced Cybersecurity Protection Suite
echo ================================================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python is installed
python --version

REM Check if we're running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  WARNING: Not running as Administrator
    echo For full functionality, please run this script as Administrator
    echo.
    echo Do you want to continue anyway? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" (
        echo Exiting...
        pause
        exit /b 1
    )
) else (
    echo ✅ Running with Administrator privileges
)

echo.
echo 📦 Checking dependencies...

REM Check if requirements are installed
pip show aiohttp >nul 2>&1
if errorlevel 1 (
    echo.
    echo 📥 Installing required dependencies...
    echo This may take a few minutes...
    pip install -r requirements_advanced.txt
    if errorlevel 1 (
        echo ❌ Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ Dependencies are already installed
)

echo.
echo 📁 Creating necessary directories...

REM Create directories
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp
if not exist "reports" mkdir reports
if not exist "quarantine" mkdir quarantine
if not exist "config" mkdir config

echo ✅ Directories created

echo.
echo 🔧 Checking configuration...

REM Check if config file exists
if not exist "config\cybershield_advanced.yaml" (
    echo ⚠️  Configuration file not found
    echo Using default configuration...
)

echo.
echo 🚀 Starting Cyber Shield Pro Advanced...
echo.
echo ================================================================
echo                        IMPORTANT NOTES
echo ================================================================
echo.
echo 📊 Dashboard will be available at: http://localhost:8080
echo 👤 Default login credentials:
echo    Username: admin
echo    Password: CyberShield2025!
echo.
echo 🛑 To stop the application, press Ctrl+C
echo.
echo ⚠️  First run may take longer as the system initializes
echo ================================================================
echo.

REM Start the application
python cyber_shield_advanced.py --config config\cybershield_advanced.yaml

echo.
echo 🛑 Cyber Shield Pro Advanced has stopped
echo.
pause
