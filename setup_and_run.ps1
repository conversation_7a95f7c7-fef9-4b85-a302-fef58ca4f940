# Cyber Shield Pro - Setup and Run Script
# PowerShell script to install Python and run the application

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Cyber Shield Pro v1.0.0" -ForegroundColor Green
Write-Host "   Advanced Cybersecurity Protection" -ForegroundColor Green
Write-Host "   Developed in Palestine 🇵🇸" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if Python is installed
function Test-PythonInstalled {
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion) {
            Write-Host "✓ Python is installed: $pythonVersion" -ForegroundColor Green
            return $true
        }
    } catch {}
    
    try {
        $pythonVersion = py --version 2>$null
        if ($pythonVersion) {
            Write-Host "✓ Python is installed: $pythonVersion" -ForegroundColor Green
            return $true
        }
    } catch {}
    
    return $false
}

# Function to install Python
function Install-Python {
    Write-Host "Python is not installed. Installing Python..." -ForegroundColor Yellow
    
    # Download Python installer
    $pythonUrl = "https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe"
    $installerPath = "$env:TEMP\python-installer.exe"
    
    try {
        Write-Host "Downloading Python installer..." -ForegroundColor Yellow
        Invoke-WebRequest -Uri $pythonUrl -OutFile $installerPath -UseBasicParsing
        
        Write-Host "Installing Python (this may take a few minutes)..." -ForegroundColor Yellow
        Start-Process -FilePath $installerPath -ArgumentList "/quiet", "InstallAllUsers=1", "PrependPath=1", "Include_test=0" -Wait
        
        # Remove installer
        Remove-Item $installerPath -Force
        
        Write-Host "✓ Python installation completed!" -ForegroundColor Green
        Write-Host "Please restart this script to continue." -ForegroundColor Yellow
        
        Read-Host "Press Enter to exit"
        exit
        
    } catch {
        Write-Host "✗ Failed to install Python automatically." -ForegroundColor Red
        Write-Host "Please download and install Python manually from: https://www.python.org/downloads/" -ForegroundColor Yellow
        Write-Host "Make sure to check 'Add Python to PATH' during installation." -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Function to install dependencies
function Install-Dependencies {
    Write-Host "Installing required Python packages..." -ForegroundColor Yellow
    
    $packages = @(
        "customtkinter==5.2.0",
        "Pillow==10.0.0",
        "requests==2.31.0",
        "psutil==5.9.5",
        "cryptography==41.0.3",
        "geocoder==1.38.1",
        "folium==0.14.0",
        "reportlab==4.0.4",
        "pycryptodome==3.18.0",
        "qrcode==7.4.2",
        "pyotp==2.9.0",
        "bcrypt==4.0.1"
    )
    
    foreach ($package in $packages) {
        try {
            Write-Host "Installing $package..." -ForegroundColor Gray
            
            # Try with python command first
            $result = python -m pip install $package 2>$null
            if ($LASTEXITCODE -ne 0) {
                # Try with py command
                py -m pip install $package
            }
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ $package installed successfully" -ForegroundColor Green
            } else {
                Write-Host "✗ Failed to install $package" -ForegroundColor Red
            }
        } catch {
            Write-Host "✗ Error installing $package" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "✓ Dependencies installation completed!" -ForegroundColor Green
}

# Function to run the application
function Start-Application {
    Write-Host "Starting Cyber Shield Pro..." -ForegroundColor Green
    Write-Host ""
    
    try {
        # Try with python command first
        python main.py
        if ($LASTEXITCODE -ne 0) {
            # Try with py command
            py main.py
        }
    } catch {
        Write-Host "✗ Failed to start the application." -ForegroundColor Red
        Write-Host "Please check the error messages above." -ForegroundColor Yellow
        Read-Host "Press Enter to exit"
    }
}

# Main execution
try {
    # Check if running as administrator
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    
    if (-not $isAdmin) {
        Write-Host "⚠️  For best results, run this script as Administrator" -ForegroundColor Yellow
        Write-Host ""
    }
    
    # Check if Python is installed
    if (-not (Test-PythonInstalled)) {
        Install-Python
    }
    
    # Install dependencies
    Install-Dependencies
    
    # Start the application
    Start-Application
    
} catch {
    Write-Host "✗ An unexpected error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
}

Write-Host ""
Write-Host "Application closed." -ForegroundColor Gray
Read-Host "Press Enter to exit"
