#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Report Generator for Cyber Shield Pro
Comprehensive security and system reports with PDF export
"""

import os
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum
from .pdf_generator import PDFGenerator
from .chart_generator import ChartGenerator
from .data_analyzer import DataAnalyzer
from ..database.log_manager import LogManager
from ..database.threat_manager import ThreatManager
from ..utils.logger import Logger

class ReportType(Enum):
    SECURITY_SUMMARY = "security_summary"
    THREAT_ANALYSIS = "threat_analysis"
    SYSTEM_PERFORMANCE = "system_performance"
    NETWORK_ACTIVITY = "network_activity"
    COMPLIANCE_AUDIT = "compliance_audit"
    INCIDENT_RESPONSE = "incident_response"
    COMPREHENSIVE = "comprehensive"

class ReportFormat(Enum):
    PDF = "pdf"
    HTML = "html"
    JSON = "json"
    CSV = "csv"

@dataclass
class ReportConfig:
    """Report configuration"""
    report_type: ReportType
    format: ReportFormat
    date_range: Dict[str, datetime]
    include_charts: bool = True
    include_recommendations: bool = True
    include_executive_summary: bool = True
    include_technical_details: bool = True
    custom_filters: Dict[str, Any] = None
    output_path: Optional[str] = None

@dataclass
class ReportSection:
    """Report section data"""
    title: str
    content: str
    charts: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    recommendations: List[str]
    severity: str  # info, warning, error, critical

class ReportGenerator:
    """Advanced Report Generation System"""
    
    def __init__(self, log_manager: LogManager, threat_manager: ThreatManager, logger: Logger):
        """Initialize report generator"""
        self.log_manager = log_manager
        self.threat_manager = threat_manager
        self.logger = logger
        
        # Report components
        self.pdf_generator = PDFGenerator()
        self.chart_generator = ChartGenerator()
        self.data_analyzer = DataAnalyzer()
        
        # Report templates
        self.report_templates = {
            ReportType.SECURITY_SUMMARY: self._generate_security_summary,
            ReportType.THREAT_ANALYSIS: self._generate_threat_analysis,
            ReportType.SYSTEM_PERFORMANCE: self._generate_system_performance,
            ReportType.NETWORK_ACTIVITY: self._generate_network_activity,
            ReportType.COMPLIANCE_AUDIT: self._generate_compliance_audit,
            ReportType.INCIDENT_RESPONSE: self._generate_incident_response,
            ReportType.COMPREHENSIVE: self._generate_comprehensive_report
        }
        
        # Report cache
        self.report_cache = {}
        
        # Default configurations
        self.default_configs = {
            ReportType.SECURITY_SUMMARY: {
                'include_charts': True,
                'include_recommendations': True,
                'include_executive_summary': True
            },
            ReportType.THREAT_ANALYSIS: {
                'include_charts': True,
                'include_technical_details': True,
                'include_recommendations': True
            }
        }
    
    def generate_report(self, config: ReportConfig, system_data: Dict[str, Any] = None) -> str:
        """Generate comprehensive security report"""
        try:
            self.logger.info(f"Generating {config.report_type.value} report in {config.format.value} format")
            
            # Get report generator function
            generator_func = self.report_templates.get(config.report_type)
            if not generator_func:
                raise ValueError(f"Unsupported report type: {config.report_type}")
            
            # Generate report data
            report_data = generator_func(config, system_data)
            
            # Format and export report
            output_path = self._export_report(report_data, config)
            
            self.logger.info(f"Report generated successfully: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error generating report: {e}")
            raise
    
    def _generate_security_summary(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate security summary report"""
        try:
            start_date = config.date_range['start']
            end_date = config.date_range['end']
            
            # Get security data
            threats = self.threat_manager.get_threats_by_date_range(start_date, end_date)
            security_logs = self.log_manager.get_logs_by_category('security', start_date, end_date)
            
            # Analyze threat data
            threat_analysis = self.data_analyzer.analyze_threats(threats)
            
            # Create report sections
            sections = []
            
            # Executive Summary
            if config.include_executive_summary:
                exec_summary = self._create_executive_summary(threat_analysis, security_logs)
                sections.append(exec_summary)
            
            # Threat Overview
            threat_overview = self._create_threat_overview(threats, threat_analysis)
            sections.append(threat_overview)
            
            # Security Events
            security_events = self._create_security_events_section(security_logs)
            sections.append(security_events)
            
            # System Health
            if system_data:
                system_health = self._create_system_health_section(system_data)
                sections.append(system_health)
            
            # Recommendations
            if config.include_recommendations:
                recommendations = self._create_recommendations_section(threat_analysis, system_data)
                sections.append(recommendations)
            
            return {
                'title': 'Security Summary Report',
                'subtitle': f'Period: {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'total_threats': len(threats),
                    'total_events': len(security_logs),
                    'report_type': config.report_type.value,
                    'date_range': config.date_range
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating security summary: {e}")
            raise
    
    def _generate_threat_analysis(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate detailed threat analysis report"""
        try:
            start_date = config.date_range['start']
            end_date = config.date_range['end']
            
            # Get threat data
            threats = self.threat_manager.get_threats_by_date_range(start_date, end_date)
            
            # Perform detailed analysis
            threat_analysis = self.data_analyzer.analyze_threats(threats)
            trend_analysis = self.data_analyzer.analyze_threat_trends(threats)
            pattern_analysis = self.data_analyzer.analyze_threat_patterns(threats)
            
            sections = []
            
            # Threat Statistics
            threat_stats = self._create_threat_statistics_section(threat_analysis)
            sections.append(threat_stats)
            
            # Threat Trends
            threat_trends = self._create_threat_trends_section(trend_analysis)
            sections.append(threat_trends)
            
            # Attack Patterns
            attack_patterns = self._create_attack_patterns_section(pattern_analysis)
            sections.append(attack_patterns)
            
            # Top Threats
            top_threats = self._create_top_threats_section(threats)
            sections.append(top_threats)
            
            # Mitigation Strategies
            if config.include_recommendations:
                mitigation = self._create_mitigation_strategies_section(threat_analysis)
                sections.append(mitigation)
            
            return {
                'title': 'Threat Analysis Report',
                'subtitle': f'Detailed Analysis: {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'total_threats': len(threats),
                    'analysis_period': (end_date - start_date).days,
                    'report_type': config.report_type.value
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating threat analysis: {e}")
            raise
    
    def _generate_system_performance(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate system performance report"""
        try:
            sections = []
            
            if system_data:
                # Performance Overview
                perf_overview = self._create_performance_overview_section(system_data)
                sections.append(perf_overview)
                
                # Resource Utilization
                resource_util = self._create_resource_utilization_section(system_data)
                sections.append(resource_util)
                
                # Performance Trends
                perf_trends = self._create_performance_trends_section(system_data)
                sections.append(perf_trends)
                
                # System Health
                system_health = self._create_system_health_section(system_data)
                sections.append(system_health)
            
            return {
                'title': 'System Performance Report',
                'subtitle': f'Generated on {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'report_type': config.report_type.value,
                    'system_monitored': bool(system_data)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating system performance report: {e}")
            raise
    
    def _generate_network_activity(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate network activity report"""
        try:
            start_date = config.date_range['start']
            end_date = config.date_range['end']
            
            # Get network logs
            network_logs = self.log_manager.get_logs_by_category('network', start_date, end_date)
            
            sections = []
            
            # Network Overview
            network_overview = self._create_network_overview_section(network_logs, system_data)
            sections.append(network_overview)
            
            # Traffic Analysis
            traffic_analysis = self._create_traffic_analysis_section(network_logs)
            sections.append(traffic_analysis)
            
            # Connection Analysis
            connection_analysis = self._create_connection_analysis_section(network_logs)
            sections.append(connection_analysis)
            
            # Security Events
            network_security = self._create_network_security_section(network_logs)
            sections.append(network_security)
            
            return {
                'title': 'Network Activity Report',
                'subtitle': f'Network Analysis: {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'total_events': len(network_logs),
                    'report_type': config.report_type.value
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating network activity report: {e}")
            raise
    
    def _generate_compliance_audit(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate compliance audit report"""
        try:
            sections = []
            
            # Compliance Overview
            compliance_overview = self._create_compliance_overview_section(system_data)
            sections.append(compliance_overview)
            
            # Security Controls
            security_controls = self._create_security_controls_section(system_data)
            sections.append(security_controls)
            
            # Policy Compliance
            policy_compliance = self._create_policy_compliance_section(system_data)
            sections.append(policy_compliance)
            
            # Audit Findings
            audit_findings = self._create_audit_findings_section(system_data)
            sections.append(audit_findings)
            
            return {
                'title': 'Compliance Audit Report',
                'subtitle': f'Audit Date: {datetime.now().strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'report_type': config.report_type.value,
                    'audit_scope': 'Full System Audit'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating compliance audit: {e}")
            raise
    
    def _generate_incident_response(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate incident response report"""
        try:
            start_date = config.date_range['start']
            end_date = config.date_range['end']
            
            # Get incident data
            incidents = self.threat_manager.get_high_severity_threats(start_date, end_date)
            incident_logs = self.log_manager.get_logs_by_severity(['error', 'critical'], start_date, end_date)
            
            sections = []
            
            # Incident Summary
            incident_summary = self._create_incident_summary_section(incidents, incident_logs)
            sections.append(incident_summary)
            
            # Timeline Analysis
            timeline_analysis = self._create_timeline_analysis_section(incidents, incident_logs)
            sections.append(timeline_analysis)
            
            # Impact Assessment
            impact_assessment = self._create_impact_assessment_section(incidents)
            sections.append(impact_assessment)
            
            # Response Actions
            response_actions = self._create_response_actions_section(incidents)
            sections.append(response_actions)
            
            return {
                'title': 'Incident Response Report',
                'subtitle': f'Incident Period: {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': sections,
                'metadata': {
                    'total_incidents': len(incidents),
                    'report_type': config.report_type.value
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating incident response report: {e}")
            raise
    
    def _generate_comprehensive_report(self, config: ReportConfig, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive security report"""
        try:
            # Generate all report types and combine
            security_config = ReportConfig(
                report_type=ReportType.SECURITY_SUMMARY,
                format=config.format,
                date_range=config.date_range
            )
            
            threat_config = ReportConfig(
                report_type=ReportType.THREAT_ANALYSIS,
                format=config.format,
                date_range=config.date_range
            )
            
            performance_config = ReportConfig(
                report_type=ReportType.SYSTEM_PERFORMANCE,
                format=config.format,
                date_range=config.date_range
            )
            
            network_config = ReportConfig(
                report_type=ReportType.NETWORK_ACTIVITY,
                format=config.format,
                date_range=config.date_range
            )
            
            # Generate individual reports
            security_report = self._generate_security_summary(security_config, system_data)
            threat_report = self._generate_threat_analysis(threat_config, system_data)
            performance_report = self._generate_system_performance(performance_config, system_data)
            network_report = self._generate_network_activity(network_config, system_data)
            
            # Combine all sections
            all_sections = []
            all_sections.extend(security_report['sections'])
            all_sections.extend(threat_report['sections'])
            all_sections.extend(performance_report['sections'])
            all_sections.extend(network_report['sections'])
            
            return {
                'title': 'Comprehensive Security Report',
                'subtitle': f'Complete Analysis: {config.date_range["start"].strftime("%Y-%m-%d")} to {config.date_range["end"].strftime("%Y-%m-%d")}',
                'generated_at': datetime.now(),
                'sections': all_sections,
                'metadata': {
                    'report_type': config.report_type.value,
                    'comprehensive': True,
                    'total_sections': len(all_sections)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error generating comprehensive report: {e}")
            raise
    
    def _create_executive_summary(self, threat_analysis: Dict[str, Any], security_logs: List[Dict[str, Any]]) -> ReportSection:
        """Create executive summary section"""
        try:
            total_threats = threat_analysis.get('total_threats', 0)
            critical_threats = threat_analysis.get('critical_threats', 0)
            resolved_threats = threat_analysis.get('resolved_threats', 0)
            
            content = f"""
            <h2>Executive Summary</h2>
            <p>During the reporting period, the security monitoring system detected {total_threats} total threats, 
            with {critical_threats} classified as critical severity. {resolved_threats} threats have been successfully 
            resolved or mitigated.</p>
            
            <p>The overall security posture shows {'good' if critical_threats < 5 else 'concerning'} levels of 
            threat activity. Immediate attention is {'not ' if critical_threats < 5 else ''}required for critical threats.</p>
            """
            
            recommendations = []
            if critical_threats > 0:
                recommendations.append("Immediate review and response to critical threats required")
            if total_threats > 50:
                recommendations.append("Consider implementing additional preventive security measures")
            
            return ReportSection(
                title="Executive Summary",
                content=content,
                charts=[],
                tables=[],
                recommendations=recommendations,
                severity="critical" if critical_threats > 5 else "warning" if critical_threats > 0 else "info"
            )
            
        except Exception as e:
            self.logger.error(f"Error creating executive summary: {e}")
            return ReportSection("Executive Summary", "Error generating summary", [], [], [], "error")
    
    def _create_threat_overview(self, threats: List[Dict[str, Any]], analysis: Dict[str, Any]) -> ReportSection:
        """Create threat overview section"""
        try:
            # Create threat severity chart
            severity_data = analysis.get('severity_breakdown', {})
            severity_chart = self.chart_generator.create_pie_chart(
                data=severity_data,
                title="Threats by Severity",
                colors=['#ff4444', '#ff8800', '#ffcc00', '#44ff44']
            )
            
            # Create threat types table
            threat_types = analysis.get('threat_types', {})
            threat_table = {
                'headers': ['Threat Type', 'Count', 'Percentage'],
                'rows': [[t_type, count, f"{(count/len(threats)*100):.1f}%"] 
                        for t_type, count in threat_types.items()]
            }
            
            content = f"""
            <h2>Threat Overview</h2>
            <p>Total threats detected: {len(threats)}</p>
            <p>Most common threat type: {max(threat_types.items(), key=lambda x: x[1])[0] if threat_types else 'None'}</p>
            """
            
            return ReportSection(
                title="Threat Overview",
                content=content,
                charts=[severity_chart],
                tables=[threat_table],
                recommendations=[],
                severity="info"
            )
            
        except Exception as e:
            self.logger.error(f"Error creating threat overview: {e}")
            return ReportSection("Threat Overview", "Error generating overview", [], [], [], "error")
    
    def _export_report(self, report_data: Dict[str, Any], config: ReportConfig) -> str:
        """Export report in specified format"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{config.report_type.value}_{timestamp}"
            
            if config.output_path:
                output_dir = config.output_path
            else:
                output_dir = "reports"
            
            os.makedirs(output_dir, exist_ok=True)
            
            if config.format == ReportFormat.PDF:
                output_path = os.path.join(output_dir, f"{filename}.pdf")
                self.pdf_generator.generate_pdf_report(report_data, output_path)
                
            elif config.format == ReportFormat.HTML:
                output_path = os.path.join(output_dir, f"{filename}.html")
                self._generate_html_report(report_data, output_path)
                
            elif config.format == ReportFormat.JSON:
                output_path = os.path.join(output_dir, f"{filename}.json")
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(report_data, f, indent=2, default=str)
                    
            else:
                raise ValueError(f"Unsupported format: {config.format}")
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"Error exporting report: {e}")
            raise
    
    def _generate_html_report(self, report_data: Dict[str, Any], output_path: str):
        """Generate HTML report"""
        try:
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{report_data['title']}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    h1 {{ color: #2c3e50; }}
                    h2 {{ color: #34495e; }}
                    .section {{ margin-bottom: 30px; }}
                    .chart {{ text-align: center; margin: 20px 0; }}
                    table {{ border-collapse: collapse; width: 100%; }}
                    th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                    th {{ background-color: #f2f2f2; }}
                    .recommendations {{ background-color: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; }}
                </style>
            </head>
            <body>
                <h1>{report_data['title']}</h1>
                <p><strong>{report_data['subtitle']}</strong></p>
                <p>Generated: {report_data['generated_at'].strftime('%Y-%m-%d %H:%M:%S')}</p>
            """
            
            for section in report_data['sections']:
                html_content += f"""
                <div class="section">
                    {section.content}
                    
                    {self._render_charts_html(section.charts)}
                    {self._render_tables_html(section.tables)}
                    {self._render_recommendations_html(section.recommendations)}
                </div>
                """
            
            html_content += """
            </body>
            </html>
            """
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
        except Exception as e:
            self.logger.error(f"Error generating HTML report: {e}")
            raise
    
    def _render_charts_html(self, charts: List[Dict[str, Any]]) -> str:
        """Render charts in HTML"""
        html = ""
        for chart in charts:
            html += f'<div class="chart"><img src="{chart.get("path", "")}" alt="{chart.get("title", "Chart")}"></div>'
        return html
    
    def _render_tables_html(self, tables: List[Dict[str, Any]]) -> str:
        """Render tables in HTML"""
        html = ""
        for table in tables:
            html += "<table>"
            
            # Headers
            if 'headers' in table:
                html += "<tr>"
                for header in table['headers']:
                    html += f"<th>{header}</th>"
                html += "</tr>"
            
            # Rows
            if 'rows' in table:
                for row in table['rows']:
                    html += "<tr>"
                    for cell in row:
                        html += f"<td>{cell}</td>"
                    html += "</tr>"
            
            html += "</table>"
        
        return html
    
    def _render_recommendations_html(self, recommendations: List[str]) -> str:
        """Render recommendations in HTML"""
        if not recommendations:
            return ""
        
        html = '<div class="recommendations"><h3>Recommendations:</h3><ul>'
        for rec in recommendations:
            html += f"<li>{rec}</li>"
        html += "</ul></div>"
        
        return html
    
    # Placeholder methods for other sections (would be implemented similarly)
    def _create_security_events_section(self, logs): return ReportSection("Security Events", "", [], [], [], "info")
    def _create_system_health_section(self, data): return ReportSection("System Health", "", [], [], [], "info")
    def _create_recommendations_section(self, analysis, data): return ReportSection("Recommendations", "", [], [], [], "info")
    def _create_threat_statistics_section(self, analysis): return ReportSection("Threat Statistics", "", [], [], [], "info")
    def _create_threat_trends_section(self, trends): return ReportSection("Threat Trends", "", [], [], [], "info")
    def _create_attack_patterns_section(self, patterns): return ReportSection("Attack Patterns", "", [], [], [], "info")
    def _create_top_threats_section(self, threats): return ReportSection("Top Threats", "", [], [], [], "info")
    def _create_mitigation_strategies_section(self, analysis): return ReportSection("Mitigation Strategies", "", [], [], [], "info")
    def _create_performance_overview_section(self, data): return ReportSection("Performance Overview", "", [], [], [], "info")
    def _create_resource_utilization_section(self, data): return ReportSection("Resource Utilization", "", [], [], [], "info")
    def _create_performance_trends_section(self, data): return ReportSection("Performance Trends", "", [], [], [], "info")
    def _create_network_overview_section(self, logs, data): return ReportSection("Network Overview", "", [], [], [], "info")
    def _create_traffic_analysis_section(self, logs): return ReportSection("Traffic Analysis", "", [], [], [], "info")
    def _create_connection_analysis_section(self, logs): return ReportSection("Connection Analysis", "", [], [], [], "info")
    def _create_network_security_section(self, logs): return ReportSection("Network Security", "", [], [], [], "info")
    def _create_compliance_overview_section(self, data): return ReportSection("Compliance Overview", "", [], [], [], "info")
    def _create_security_controls_section(self, data): return ReportSection("Security Controls", "", [], [], [], "info")
    def _create_policy_compliance_section(self, data): return ReportSection("Policy Compliance", "", [], [], [], "info")
    def _create_audit_findings_section(self, data): return ReportSection("Audit Findings", "", [], [], [], "info")
    def _create_incident_summary_section(self, incidents, logs): return ReportSection("Incident Summary", "", [], [], [], "info")
    def _create_timeline_analysis_section(self, incidents, logs): return ReportSection("Timeline Analysis", "", [], [], [], "info")
    def _create_impact_assessment_section(self, incidents): return ReportSection("Impact Assessment", "", [], [], [], "info")
    def _create_response_actions_section(self, incidents): return ReportSection("Response Actions", "", [], [], [], "info")
    
    def get_available_report_types(self) -> List[str]:
        """Get list of available report types"""
        return [report_type.value for report_type in ReportType]
    
    def get_report_template(self, report_type: ReportType) -> Dict[str, Any]:
        """Get report template configuration"""
        return self.default_configs.get(report_type, {})
    
    def schedule_report(self, config: ReportConfig, schedule: str) -> bool:
        """Schedule automatic report generation"""
        try:
            # This would implement report scheduling
            # For now, just log the request
            self.logger.info(f"Report scheduled: {config.report_type.value} - {schedule}")
            return True
        except Exception as e:
            self.logger.error(f"Error scheduling report: {e}")
            return False
