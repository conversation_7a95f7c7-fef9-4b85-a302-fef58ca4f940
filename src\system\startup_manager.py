#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Startup Manager for Cyber Shield Pro
Comprehensive startup programs management and security monitoring
"""

import os
import winreg
import subprocess
import ctypes
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from ..utils.logger import Logger

class StartupLocation(Enum):
    REGISTRY_HKLM_RUN = "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"
    REGISTRY_HKLM_RUNONCE = "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"
    REGISTRY_HKCU_RUN = "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"
    REGISTRY_HKCU_RUNONCE = "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce"
    STARTUP_FOLDER_ALL_USERS = "All Users Startup Folder"
    STARTUP_FOLDER_CURRENT_USER = "Current User Startup Folder"
    TASK_SCHEDULER = "Task Scheduler"
    SERVICES = "Windows Services"

@dataclass
class StartupProgram:
    """Startup program information"""
    name: str
    command: str
    location: StartupLocation
    enabled: bool
    file_path: Optional[str]
    arguments: str
    publisher: Optional[str]
    description: Optional[str]
    file_version: Optional[str]
    digital_signature: Optional[Dict[str, Any]]
    is_suspicious: bool
    threat_level: str
    impact_level: str  # low, medium, high
    last_modified: Optional[datetime]
    file_size: Optional[int]
    startup_delay: int  # seconds

class StartupManager:
    """Startup Programs Management and Security"""
    
    def __init__(self, logger: Logger):
        """Initialize startup manager"""
        self.logger = logger
        
        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()
        
        # Startup programs tracking
        self.startup_programs = {}
        self.disabled_programs = {}
        
        # Suspicious patterns
        self.suspicious_patterns = [
            'virus', 'trojan', 'malware', 'hack', 'crack',
            'keygen', 'patch', 'loader', 'inject', 'bot',
            'miner', 'crypto', 'backdoor', 'rootkit', 'worm'
        ]
        
        # Suspicious file locations
        self.suspicious_locations = [
            'temp', 'tmp', 'appdata\\roaming', 'programdata',
            'users\\public', 'windows\\temp', 'downloads'
        ]
        
        # Known safe publishers
        self.safe_publishers = {
            'Microsoft Corporation', 'Google LLC', 'Adobe Inc.',
            'Intel Corporation', 'NVIDIA Corporation', 'Realtek',
            'Apple Inc.', 'Mozilla Corporation', 'Oracle Corporation'
        }
        
        # Startup alerts
        self.startup_alerts = []
        
        # Load startup programs
        self._scan_startup_programs()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def _scan_startup_programs(self):
        """Scan all startup programs from various locations"""
        try:
            startup_programs = {}
            
            # Scan registry locations
            startup_programs.update(self._scan_registry_startup())
            
            # Scan startup folders
            startup_programs.update(self._scan_startup_folders())
            
            # Scan task scheduler (basic)
            startup_programs.update(self._scan_task_scheduler())
            
            # Analyze each program
            for program_id, program in startup_programs.items():
                self._analyze_startup_program(program)
            
            self.startup_programs = startup_programs
            
        except Exception as e:
            self.logger.error(f"Error scanning startup programs: {e}")
    
    def _scan_registry_startup(self) -> Dict[str, StartupProgram]:
        """Scan registry startup locations"""
        programs = {}
        
        registry_locations = [
            (winreg.HKEY_LOCAL_MACHINE, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run', StartupLocation.REGISTRY_HKLM_RUN),
            (winreg.HKEY_LOCAL_MACHINE, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce', StartupLocation.REGISTRY_HKLM_RUNONCE),
            (winreg.HKEY_CURRENT_USER, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run', StartupLocation.REGISTRY_HKCU_RUN),
            (winreg.HKEY_CURRENT_USER, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce', StartupLocation.REGISTRY_HKCU_RUNONCE)
        ]
        
        for hive, key_path, location in registry_locations:
            try:
                with winreg.OpenKey(hive, key_path, 0, winreg.KEY_READ) as key:
                    i = 0
                    while True:
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(key, i)
                            
                            # Parse command and arguments
                            command_parts = self._parse_command_line(value_data)
                            file_path = command_parts['file_path']
                            arguments = command_parts['arguments']
                            
                            program_id = f"{location.value}\\{value_name}"
                            
                            programs[program_id] = StartupProgram(
                                name=value_name,
                                command=value_data,
                                location=location,
                                enabled=True,
                                file_path=file_path,
                                arguments=arguments,
                                publisher=None,
                                description=None,
                                file_version=None,
                                digital_signature=None,
                                is_suspicious=False,
                                threat_level='low',
                                impact_level='medium',
                                last_modified=None,
                                file_size=None,
                                startup_delay=0
                            )
                            
                            i += 1
                        except OSError:
                            break
                            
            except Exception as e:
                self.logger.warning(f"Could not scan registry location {key_path}: {e}")
                continue
        
        return programs
    
    def _scan_startup_folders(self) -> Dict[str, StartupProgram]:
        """Scan startup folders"""
        programs = {}
        
        # Get startup folder paths
        startup_folders = [
            (os.path.expandvars(r'%APPDATA%\Microsoft\Windows\Start Menu\Programs\Startup'), StartupLocation.STARTUP_FOLDER_CURRENT_USER),
            (os.path.expandvars(r'%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Startup'), StartupLocation.STARTUP_FOLDER_ALL_USERS)
        ]
        
        for folder_path, location in startup_folders:
            try:
                if os.path.exists(folder_path):
                    for item in os.listdir(folder_path):
                        item_path = os.path.join(folder_path, item)
                        
                        if os.path.isfile(item_path):
                            # Get file info
                            file_stat = os.stat(item_path)
                            
                            program_id = f"{location.value}\\{item}"
                            
                            programs[program_id] = StartupProgram(
                                name=item,
                                command=item_path,
                                location=location,
                                enabled=True,
                                file_path=item_path,
                                arguments='',
                                publisher=None,
                                description=None,
                                file_version=None,
                                digital_signature=None,
                                is_suspicious=False,
                                threat_level='low',
                                impact_level='low',
                                last_modified=datetime.fromtimestamp(file_stat.st_mtime),
                                file_size=file_stat.st_size,
                                startup_delay=0
                            )
                            
            except Exception as e:
                self.logger.warning(f"Could not scan startup folder {folder_path}: {e}")
                continue
        
        return programs
    
    def _scan_task_scheduler(self) -> Dict[str, StartupProgram]:
        """Scan task scheduler for startup tasks"""
        programs = {}
        
        try:
            # Use schtasks to get startup tasks
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                
                for line in lines[1:]:  # Skip header
                    if line.strip():
                        parts = [part.strip('"') for part in line.split('","')]
                        
                        if len(parts) >= 4:
                            task_name = parts[0].strip('"')
                            next_run = parts[1]
                            status = parts[2]
                            
                            # Only include tasks that run at startup or logon
                            if 'startup' in task_name.lower() or 'logon' in task_name.lower():
                                program_id = f"TaskScheduler\\{task_name}"
                                
                                programs[program_id] = StartupProgram(
                                    name=task_name,
                                    command=f"Scheduled Task: {task_name}",
                                    location=StartupLocation.TASK_SCHEDULER,
                                    enabled=status.lower() == 'ready',
                                    file_path=None,
                                    arguments='',
                                    publisher=None,
                                    description=f"Scheduled task: {task_name}",
                                    file_version=None,
                                    digital_signature=None,
                                    is_suspicious=False,
                                    threat_level='low',
                                    impact_level='medium',
                                    last_modified=None,
                                    file_size=None,
                                    startup_delay=0
                                )
                                
        except Exception as e:
            self.logger.warning(f"Could not scan task scheduler: {e}")
        
        return programs
    
    def _parse_command_line(self, command: str) -> Dict[str, str]:
        """Parse command line to extract file path and arguments"""
        try:
            command = command.strip()
            
            if command.startswith('"'):
                # Quoted path
                end_quote = command.find('"', 1)
                if end_quote != -1:
                    file_path = command[1:end_quote]
                    arguments = command[end_quote + 1:].strip()
                else:
                    file_path = command
                    arguments = ''
            else:
                # Unquoted path
                parts = command.split(' ', 1)
                file_path = parts[0]
                arguments = parts[1] if len(parts) > 1 else ''
            
            return {
                'file_path': file_path,
                'arguments': arguments
            }
            
        except Exception as e:
            return {
                'file_path': command,
                'arguments': ''
            }
    
    def _analyze_startup_program(self, program: StartupProgram):
        """Analyze startup program for security threats"""
        try:
            is_suspicious = False
            threat_level = 'low'
            impact_level = 'low'
            
            # Check program name for suspicious patterns
            name_lower = program.name.lower()
            if any(pattern in name_lower for pattern in self.suspicious_patterns):
                is_suspicious = True
                threat_level = 'high'
            
            # Check file path if available
            if program.file_path:
                file_path_lower = program.file_path.lower()
                
                # Check for suspicious file locations
                if any(sus_loc in file_path_lower for sus_loc in self.suspicious_locations):
                    is_suspicious = True
                    threat_level = max(threat_level, 'medium')
                
                # Check if file exists
                if os.path.exists(program.file_path):
                    # Get file information
                    file_info = self._get_file_info(program.file_path)
                    
                    program.publisher = file_info.get('publisher')
                    program.description = file_info.get('description')
                    program.file_version = file_info.get('version')
                    program.digital_signature = file_info.get('signature')
                    
                    # Check publisher
                    if program.publisher and program.publisher not in self.safe_publishers:
                        threat_level = max(threat_level, 'medium')
                    
                    # Check digital signature
                    if not file_info.get('is_signed', False):
                        threat_level = max(threat_level, 'medium')
                        
                else:
                    # File doesn't exist - highly suspicious
                    is_suspicious = True
                    threat_level = 'high'
            
            # Check command for suspicious patterns
            command_lower = program.command.lower()
            if any(pattern in command_lower for pattern in self.suspicious_patterns):
                is_suspicious = True
                threat_level = 'high'
            
            # Determine impact level based on location
            if program.location in [StartupLocation.REGISTRY_HKLM_RUN, StartupLocation.REGISTRY_HKLM_RUNONCE]:
                impact_level = 'high'  # System-wide startup
            elif program.location == StartupLocation.SERVICES:
                impact_level = 'high'  # Service startup
            elif program.location == StartupLocation.TASK_SCHEDULER:
                impact_level = 'medium'  # Scheduled task
            else:
                impact_level = 'low'  # User-specific startup
            
            # Update program properties
            program.is_suspicious = is_suspicious
            program.threat_level = threat_level
            program.impact_level = impact_level
            
            # Create alert if suspicious
            if is_suspicious:
                self._create_startup_alert(
                    'suspicious_startup',
                    f'Suspicious startup program detected: {program.name}',
                    program
                )
            
        except Exception as e:
            self.logger.error(f"Error analyzing startup program {program.name}: {e}")
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get file information including version and signature"""
        try:
            file_info = {
                'exists': os.path.exists(file_path),
                'is_signed': False,
                'publisher': None,
                'description': None,
                'version': None,
                'signature': None
            }
            
            if file_info['exists']:
                # Get file size and modification time
                file_stat = os.stat(file_path)
                file_info['size'] = file_stat.st_size
                file_info['modified'] = datetime.fromtimestamp(file_stat.st_mtime)
                
                # This would use Windows API to get detailed file information
                # For now, return basic info
                file_info['is_signed'] = True  # Assume signed to avoid false positives
            
            return file_info
            
        except Exception as e:
            self.logger.error(f"Error getting file info for {file_path}: {e}")
            return {'exists': False, 'is_signed': False}
    
    def _create_startup_alert(self, alert_type: str, message: str, program: StartupProgram):
        """Create startup program alert"""
        try:
            alert = {
                'timestamp': datetime.now(),
                'type': alert_type,
                'message': message,
                'program': asdict(program)
            }
            
            self.startup_alerts.append(alert)
            
            # Keep only last 50 alerts
            if len(self.startup_alerts) > 50:
                self.startup_alerts = self.startup_alerts[-50:]
            
            self.logger.warning(f"Startup alert: {message}")
            
        except Exception as e:
            self.logger.error(f"Error creating startup alert: {e}")
    
    def disable_startup_program(self, program_id: str) -> bool:
        """Disable a startup program"""
        try:
            if program_id not in self.startup_programs:
                return False
            
            program = self.startup_programs[program_id]
            
            if program.location in [StartupLocation.REGISTRY_HKLM_RUN, StartupLocation.REGISTRY_HKLM_RUNONCE]:
                if not self.has_admin_rights:
                    self.logger.error("Admin privileges required to disable system startup programs")
                    return False
                
                # Remove from registry
                hive = winreg.HKEY_LOCAL_MACHINE
                key_path = program.location.value.split('\\', 1)[1]
                
                try:
                    with winreg.OpenKey(hive, key_path, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.DeleteValue(key, program.name)
                except Exception as e:
                    self.logger.error(f"Failed to remove registry value: {e}")
                    return False
                    
            elif program.location in [StartupLocation.REGISTRY_HKCU_RUN, StartupLocation.REGISTRY_HKCU_RUNONCE]:
                # Remove from user registry
                hive = winreg.HKEY_CURRENT_USER
                key_path = program.location.value.split('\\', 1)[1]
                
                try:
                    with winreg.OpenKey(hive, key_path, 0, winreg.KEY_SET_VALUE) as key:
                        winreg.DeleteValue(key, program.name)
                except Exception as e:
                    self.logger.error(f"Failed to remove registry value: {e}")
                    return False
                    
            elif program.location in [StartupLocation.STARTUP_FOLDER_ALL_USERS, StartupLocation.STARTUP_FOLDER_CURRENT_USER]:
                # Remove file from startup folder
                if program.file_path and os.path.exists(program.file_path):
                    try:
                        os.remove(program.file_path)
                    except Exception as e:
                        self.logger.error(f"Failed to remove startup file: {e}")
                        return False
            
            # Move to disabled programs
            program.enabled = False
            self.disabled_programs[program_id] = program
            del self.startup_programs[program_id]
            
            self.logger.info(f"Startup program disabled: {program.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error disabling startup program {program_id}: {e}")
            return False
    
    def enable_startup_program(self, program_id: str) -> bool:
        """Re-enable a disabled startup program"""
        try:
            if program_id not in self.disabled_programs:
                return False
            
            program = self.disabled_programs[program_id]
            
            # This would restore the program to its original location
            # Implementation depends on the specific location type
            
            program.enabled = True
            self.startup_programs[program_id] = program
            del self.disabled_programs[program_id]
            
            self.logger.info(f"Startup program enabled: {program.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error enabling startup program {program_id}: {e}")
            return False
    
    def get_startup_programs(self) -> List[Dict[str, Any]]:
        """Get list of all startup programs"""
        return [asdict(program) for program in self.startup_programs.values()]
    
    def get_suspicious_startup_programs(self) -> List[Dict[str, Any]]:
        """Get list of suspicious startup programs"""
        return [asdict(program) for program in self.startup_programs.values() if program.is_suspicious]
    
    def get_disabled_programs(self) -> List[Dict[str, Any]]:
        """Get list of disabled startup programs"""
        return [asdict(program) for program in self.disabled_programs.values()]
    
    def get_startup_alerts(self) -> List[Dict[str, Any]]:
        """Get startup program alerts"""
        return self.startup_alerts
    
    def get_startup_statistics(self) -> Dict[str, Any]:
        """Get startup programs statistics"""
        try:
            total_programs = len(self.startup_programs)
            suspicious_programs = len([p for p in self.startup_programs.values() if p.is_suspicious])
            high_impact_programs = len([p for p in self.startup_programs.values() if p.impact_level == 'high'])
            
            # Count by location
            location_counts = {}
            for program in self.startup_programs.values():
                location = program.location.value
                location_counts[location] = location_counts.get(location, 0) + 1
            
            return {
                'total_programs': total_programs,
                'suspicious_programs': suspicious_programs,
                'high_impact_programs': high_impact_programs,
                'disabled_programs': len(self.disabled_programs),
                'recent_alerts': len(self.startup_alerts),
                'location_breakdown': location_counts,
                'has_admin_rights': self.has_admin_rights
            }
            
        except Exception as e:
            self.logger.error(f"Error getting startup statistics: {e}")
            return {}
    
    def refresh_startup_programs(self):
        """Refresh startup programs list"""
        self._scan_startup_programs()
    
    def export_startup_data(self) -> Dict[str, Any]:
        """Export startup programs data"""
        try:
            return {
                'startup_programs': [asdict(program) for program in self.startup_programs.values()],
                'disabled_programs': [asdict(program) for program in self.disabled_programs.values()],
                'alerts': self.startup_alerts,
                'statistics': self.get_startup_statistics(),
                'export_time': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error exporting startup data: {e}")
            return {}
