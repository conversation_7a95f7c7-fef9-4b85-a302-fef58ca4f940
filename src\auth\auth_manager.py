#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Authentication Manager for Cyber Shield Pro
Handles user authentication, registration, and session management
"""

import secrets
import hashlib
import datetime
import re
from typing import Optional, Dict, Any, Tuple
from ..database.user_manager import User<PERSON>anager
from ..database.log_manager import LogManager
from .session_manager import SessionManager
from .two_factor_auth import TwoFactorAuth
from .password_manager import PasswordManager

class AuthManager:
    """Main authentication manager"""
    
    def __init__(self, user_manager: User<PERSON>anager, log_manager: LogManager):
        """Initialize authentication manager"""
        self.user_manager = user_manager
        self.log_manager = log_manager
        self.session_manager = SessionManager(user_manager.db)
        self.two_factor = TwoFactorAuth()
        self.password_manager = PasswordManager()
        
        # Login attempt tracking
        self.failed_attempts = {}
        self.max_attempts = 5
        self.lockout_duration = 300  # 5 minutes
    
    def register_user(self, username: str, email: str, password: str, 
                     confirm_password: str, ip_address: str = None) -> <PERSON><PERSON>[bool, str]:
        """Register a new user"""
        try:
            # Validate input
            validation_result = self._validate_registration_data(
                username, email, password, confirm_password
            )
            if not validation_result[0]:
                return validation_result
            
            # Check if user already exists
            if self.user_manager.get_user_by_username(username):
                return False, "اسم المستخدم موجود بالفعل"
            
            if self.user_manager.get_user_by_email(email):
                return False, "البريد الإلكتروني مسجل بالفعل"
            
            # Create user
            user_id = self.user_manager.create_user(username, email, password)
            if not user_id:
                return False, "فشل في إنشاء الحساب"
            
            # Log registration
            self.log_manager.log_info(
                user_id, 'user', 'User registered successfully',
                {'username': username, 'email': email}, ip_address
            )
            
            return True, "تم إنشاء الحساب بنجاح"
            
        except Exception as e:
            print(f"Registration error: {e}")
            return False, "خطأ في النظام"
    
    def login_user(self, username: str, password: str, 
                  two_factor_code: str = None, ip_address: str = None) -> Tuple[bool, str, Dict[str, Any]]:
        """Authenticate user login"""
        try:
            # Check for account lockout
            if self._is_account_locked(username):
                return False, "الحساب مقفل مؤقتاً بسبب محاولات دخول فاشلة", {}
            
            # Authenticate user
            user = self.user_manager.authenticate_user(username, password)
            if not user:
                self._record_failed_attempt(username, ip_address)
                return False, "اسم المستخدم أو كلمة المرور غير صحيحة", {}
            
            # Check two-factor authentication
            if user['two_factor_enabled']:
                if not two_factor_code:
                    return False, "مطلوب رمز التحقق الثنائي", {'requires_2fa': True}
                
                if not self.two_factor.verify_token(user['two_factor_secret'], two_factor_code):
                    self._record_failed_attempt(username, ip_address)
                    return False, "رمز التحقق الثنائي غير صحيح", {}
            
            # Clear failed attempts
            self._clear_failed_attempts(username)
            
            # Create session
            session_token = self.session_manager.create_session(user['id'], ip_address)
            if not session_token:
                return False, "فشل في إنشاء الجلسة", {}
            
            # Log successful login
            self.log_manager.log_info(
                user['id'], 'user', 'User logged in successfully',
                {'username': username}, ip_address
            )
            
            # Return user data with session
            user_data = {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'is_admin': user['is_admin'],
                'session_token': session_token,
                'two_factor_enabled': user['two_factor_enabled']
            }
            
            return True, "تم تسجيل الدخول بنجاح", user_data
            
        except Exception as e:
            print(f"Login error: {e}")
            return False, "خطأ في النظام", {}
    
    def logout_user(self, session_token: str, ip_address: str = None) -> bool:
        """Logout user and invalidate session"""
        try:
            # Get session info
            session = self.session_manager.get_session(session_token)
            if session:
                # Log logout
                self.log_manager.log_info(
                    session['user_id'], 'user', 'User logged out',
                    {}, ip_address
                )
            
            # Invalidate session
            return self.session_manager.invalidate_session(session_token)
            
        except Exception as e:
            print(f"Logout error: {e}")
            return False
    
    def validate_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Validate user session"""
        try:
            session = self.session_manager.get_session(session_token)
            if not session or not session['is_active']:
                return None
            
            # Check if session is expired
            if self.session_manager.is_session_expired(session_token):
                self.session_manager.invalidate_session(session_token)
                return None
            
            # Get user data
            user = self.user_manager.get_user_by_id(session['user_id'])
            if not user or not user['is_active']:
                return None
            
            # Update session activity
            self.session_manager.update_session_activity(session_token)
            
            return {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'is_admin': user['is_admin'],
                'session_token': session_token
            }
            
        except Exception as e:
            print(f"Session validation error: {e}")
            return None
    
    def change_password(self, user_id: int, current_password: str, 
                       new_password: str, confirm_password: str) -> Tuple[bool, str]:
        """Change user password"""
        try:
            # Get user
            user = self.user_manager.get_user_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"
            
            # Verify current password
            if not self.user_manager._verify_password(current_password, user['password_hash'], user['salt']):
                return False, "كلمة المرور الحالية غير صحيحة"
            
            # Validate new password
            if new_password != confirm_password:
                return False, "كلمات المرور الجديدة غير متطابقة"
            
            if not self.password_manager.is_password_strong(new_password):
                return False, "كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أرقام ورموز"
            
            # Update password
            if self.user_manager.update_password(user_id, new_password):
                # Log password change
                self.log_manager.log_info(
                    user_id, 'security', 'Password changed successfully'
                )
                return True, "تم تغيير كلمة المرور بنجاح"
            
            return False, "فشل في تغيير كلمة المرور"
            
        except Exception as e:
            print(f"Password change error: {e}")
            return False, "خطأ في النظام"
    
    def enable_two_factor(self, user_id: int) -> Tuple[bool, str, str]:
        """Enable two-factor authentication"""
        try:
            # Generate secret
            secret = self.two_factor.generate_secret()
            
            # Get user for QR code
            user = self.user_manager.get_user_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود", ""
            
            # Generate QR code URL
            qr_url = self.two_factor.generate_qr_url(secret, user['username'])
            
            # Enable 2FA in database
            if self.user_manager.enable_two_factor(user_id, secret):
                # Log 2FA enablement
                self.log_manager.log_info(
                    user_id, 'security', 'Two-factor authentication enabled'
                )
                return True, "تم تفعيل التحقق الثنائي", qr_url
            
            return False, "فشل في تفعيل التحقق الثنائي", ""
            
        except Exception as e:
            print(f"2FA enable error: {e}")
            return False, "خطأ في النظام", ""
    
    def disable_two_factor(self, user_id: int, password: str) -> Tuple[bool, str]:
        """Disable two-factor authentication"""
        try:
            # Verify password
            user = self.user_manager.get_user_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"
            
            if not self.user_manager._verify_password(password, user['password_hash'], user['salt']):
                return False, "كلمة المرور غير صحيحة"
            
            # Disable 2FA
            if self.user_manager.disable_two_factor(user_id):
                # Log 2FA disablement
                self.log_manager.log_info(
                    user_id, 'security', 'Two-factor authentication disabled'
                )
                return True, "تم إلغاء التحقق الثنائي"
            
            return False, "فشل في إلغاء التحقق الثنائي"
            
        except Exception as e:
            print(f"2FA disable error: {e}")
            return False, "خطأ في النظام"
    
    def _validate_registration_data(self, username: str, email: str, 
                                  password: str, confirm_password: str) -> Tuple[bool, str]:
        """Validate registration data"""
        # Username validation
        if not username or len(username) < 3:
            return False, "اسم المستخدم يجب أن يكون 3 أحرف على الأقل"
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط"
        
        # Email validation
        if not email or not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return False, "البريد الإلكتروني غير صحيح"
        
        # Password validation
        if password != confirm_password:
            return False, "كلمات المرور غير متطابقة"
        
        if not self.password_manager.is_password_strong(password):
            return False, "كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل مع أرقام ورموز"
        
        return True, "البيانات صحيحة"
    
    def _is_account_locked(self, username: str) -> bool:
        """Check if account is locked due to failed attempts"""
        if username not in self.failed_attempts:
            return False
        
        attempts_data = self.failed_attempts[username]
        if attempts_data['count'] >= self.max_attempts:
            # Check if lockout period has expired
            if datetime.datetime.now() - attempts_data['last_attempt'] > datetime.timedelta(seconds=self.lockout_duration):
                del self.failed_attempts[username]
                return False
            return True
        
        return False
    
    def _record_failed_attempt(self, username: str, ip_address: str = None):
        """Record failed login attempt"""
        now = datetime.datetime.now()
        
        if username not in self.failed_attempts:
            self.failed_attempts[username] = {'count': 0, 'last_attempt': now}
        
        self.failed_attempts[username]['count'] += 1
        self.failed_attempts[username]['last_attempt'] = now
        
        # Log failed attempt
        self.log_manager.log_warning(
            None, 'security', f'Failed login attempt for username: {username}',
            {'username': username, 'attempt_count': self.failed_attempts[username]['count']},
            ip_address
        )
    
    def _clear_failed_attempts(self, username: str):
        """Clear failed login attempts for user"""
        if username in self.failed_attempts:
            del self.failed_attempts[username]
