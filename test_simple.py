#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Test Script for Cyber Shield Pro Advanced
Tests basic functionality without complex dependencies
"""

import sys
import os
import asyncio
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all modules can be imported"""
    print("🔍 Testing module imports...")
    
    try:
        from src.utils.logger import Logger
        print("✅ Logger imported successfully")
    except Exception as e:
        print(f"❌ Logger import failed: {e}")
        return False
    
    try:
        from src.utils.config_manager import ConfigManager
        print("✅ ConfigManager imported successfully")
    except Exception as e:
        print(f"❌ ConfigManager import failed: {e}")
        return False
    
    try:
        from src.database.database_manager import DatabaseManager
        print("✅ DatabaseManager imported successfully")
    except Exception as e:
        print(f"❌ DatabaseManager import failed: {e}")
        return False
    
    try:
        from src.security.threat_detector import ThreatDetector
        print("✅ ThreatDetector imported successfully")
    except Exception as e:
        print(f"❌ ThreatDetector import failed: {e}")
        return False
    
    try:
        from src.security.malware_scanner import MalwareScanner
        print("✅ MalwareScanner imported successfully")
    except Exception as e:
        print(f"❌ MalwareScanner import failed: {e}")
        return False
    
    try:
        from src.security.firewall_manager import FirewallManager
        print("✅ FirewallManager imported successfully")
    except Exception as e:
        print(f"❌ FirewallManager import failed: {e}")
        return False
    
    try:
        from src.network.network_monitor import NetworkMonitor
        print("✅ NetworkMonitor imported successfully")
    except Exception as e:
        print(f"❌ NetworkMonitor import failed: {e}")
        return False
    
    try:
        from src.system.process_monitor import ProcessMonitor
        print("✅ ProcessMonitor imported successfully")
    except Exception as e:
        print(f"❌ ProcessMonitor import failed: {e}")
        return False
    
    print("✅ All core modules imported successfully!")
    return True

async def test_basic_functionality():
    """Test basic functionality"""
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test Logger
        from src.utils.logger import Logger
        logger = Logger()
        logger.info("Test log message")
        print("✅ Logger working")
        
        # Test ConfigManager
        from src.utils.config_manager import ConfigManager
        config = ConfigManager()
        config.set('test.key', 'test_value')
        value = config.get('test.key')
        assert value == 'test_value'
        print("✅ ConfigManager working")
        
        # Test DatabaseManager
        from src.database.database_manager import DatabaseManager
        db = DatabaseManager('test.db', logger)
        await db.initialize()
        print("✅ DatabaseManager working")
        
        # Test ThreatDetector
        from src.security.threat_detector import ThreatDetector
        from src.database.threat_manager import ThreatManager
        from src.database.log_manager import LogManager
        
        threat_manager = ThreatManager(db, logger)
        log_manager = LogManager(db, logger)
        threat_detector = ThreatDetector(threat_manager, log_manager, logger)
        threat_detector.start_monitoring()
        print("✅ ThreatDetector working")
        
        # Test MalwareScanner
        from src.security.malware_scanner import MalwareScanner
        scanner = MalwareScanner(threat_manager, logger)
        scanner.start_monitoring()
        print("✅ MalwareScanner working")
        
        # Test FirewallManager
        from src.security.firewall_manager import FirewallManager
        firewall = FirewallManager(logger)
        firewall.initialize_firewall()
        print("✅ FirewallManager working")
        
        # Test NetworkMonitor
        from src.network.network_monitor import NetworkMonitor
        network_monitor = NetworkMonitor(logger)
        print("✅ NetworkMonitor working")
        
        # Test ProcessMonitor
        from src.system.process_monitor import ProcessMonitor
        process_monitor = ProcessMonitor(logger)
        process_monitor.start_monitoring()
        print("✅ ProcessMonitor working")
        
        print("✅ All basic functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

async def test_dashboard():
    """Test dashboard functionality"""
    print("\n🌐 Testing dashboard...")
    
    try:
        from src.dashboard.admin_dashboard import AdminDashboard
        from src.utils.logger import Logger
        
        logger = Logger()
        dashboard = AdminDashboard(logger, port=8081)  # Use different port for test
        
        # Test dashboard data update
        dashboard.update_data({
            'threats_detected': 5,
            'active_connections': 10,
            'cpu_usage': 25.5,
            'memory_usage': 45.2
        })
        
        status = dashboard.get_status()
        print(f"✅ Dashboard status: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Dashboard test failed: {e}")
        return False

def test_file_structure():
    """Test if all required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        'src/utils/logger.py',
        'src/utils/config_manager.py',
        'src/database/database_manager.py',
        'src/database/threat_manager.py',
        'src/database/log_manager.py',
        'src/security/threat_detector.py',
        'src/security/malware_scanner.py',
        'src/security/firewall_manager.py',
        'src/security/intrusion_detector.py',
        'src/network/network_monitor.py',
        'src/network/packet_analyzer.py',
        'src/network/connection_tracker.py',
        'src/system/process_monitor.py',
        'src/dashboard/admin_dashboard.py',
        'src/notifications/notification_manager.py',
        'src/reports/report_generator.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        return False
    else:
        print("\n✅ All required files present!")
        return True

async def run_simple_demo():
    """Run a simple demo of the system"""
    print("\n🚀 Running simple demo...")
    
    try:
        from src.utils.logger import Logger
        from src.utils.config_manager import ConfigManager
        from src.database.database_manager import DatabaseManager
        from src.database.threat_manager import ThreatManager
        from src.security.threat_detector import ThreatDetector
        from src.notifications.notification_manager import NotificationManager
        
        # Initialize components
        logger = Logger()
        config = ConfigManager()
        db = DatabaseManager('demo.db', logger)
        await db.initialize()
        
        threat_manager = ThreatManager(db, logger)
        notification_manager = NotificationManager(logger)
        
        # Start services
        notification_manager.start_notifications()
        
        # Simulate some activity
        print("📊 Simulating security activity...")
        
        # Log a threat
        await threat_manager.log_threat(
            threat_type='demo_threat',
            severity='medium',
            source='test_system',
            description='Demo threat for testing'
        )
        
        # Send notification
        await notification_manager.send_notification(
            title="Demo Alert",
            message="This is a demo security alert",
            severity='info'
        )
        
        # Get statistics
        stats = await threat_manager.get_threat_statistics()
        print(f"📈 Threat statistics: {stats}")
        
        print("✅ Demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🛡️  Cyber Shield Pro Advanced - System Test")
    print("=" * 50)
    
    # Test file structure
    if not test_file_structure():
        print("\n❌ File structure test failed. Cannot continue.")
        return False
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Cannot continue.")
        return False
    
    # Test basic functionality
    if not await test_basic_functionality():
        print("\n❌ Basic functionality test failed.")
        return False
    
    # Test dashboard
    if not await test_dashboard():
        print("\n❌ Dashboard test failed.")
        return False
    
    # Run demo
    if not await run_simple_demo():
        print("\n❌ Demo failed.")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! System is working correctly.")
    print("🛡️  Cyber Shield Pro Advanced is ready to use!")
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n✅ Test completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ Test failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        sys.exit(1)
