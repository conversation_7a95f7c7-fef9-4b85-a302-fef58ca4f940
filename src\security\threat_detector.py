#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Threat Detector for Cyber Shield Pro
Detects various types of malware and suspicious activities
"""

import os
import hashlib
import re
import struct
from typing import Dict, List, Any, Optional
from pathlib import Path

class ThreatDetector:
    """Threat detection engine"""
    
    def __init__(self):
        """Initialize threat detector"""
        # Known malware signatures (simplified for demo)
        self.malware_signatures = {
            # Common malware file hashes
            'eicar_test': '44d88612fea8a8f36de82e1278abb02f',  # EICAR test file
            'sample_virus': 'da39a3ee5e6b4b0d3255bfef95601890',  # Sample hash
        }
        
        # Suspicious file patterns
        self.suspicious_patterns = [
            # Executable patterns
            rb'\x4d\x5a',  # MZ header (PE files)
            rb'\x50\x4b\x03\x04',  # ZIP header
            rb'\x52\x61\x72\x21',  # RAR header
            
            # Script patterns
            rb'<script[^>]*>.*?</script>',
            rb'eval\s*\(',
            rb'document\.write\s*\(',
            rb'window\.location',
            
            # PowerShell patterns
            rb'powershell',
            rb'invoke-expression',
            rb'downloadstring',
            rb'base64',
        ]
        
        # Suspicious strings
        self.suspicious_strings = [
            # Network activity
            'http://', 'https://', 'ftp://',
            'socket', 'connect', 'bind', 'listen',
            
            # File operations
            'createfile', 'writefile', 'deletefile',
            'copyfile', 'movefile',
            
            # Registry operations
            'regcreatekeyex', 'regsetvalueex', 'regdeletekey',
            
            # Process operations
            'createprocess', 'terminateprocess',
            'openprocess', 'writeprocessmemory',
            
            # Crypto operations
            'cryptacquirecontext', 'cryptencrypt', 'cryptdecrypt',
            
            # Suspicious behaviors
            'keylogger', 'screenshot', 'webcam',
            'password', 'credential', 'bitcoin',
            'ransomware', 'encrypt', 'decrypt',
        ]
        
        # File type signatures
        self.file_signatures = {
            'PE': rb'\x4d\x5a',  # Windows executable
            'ELF': rb'\x7f\x45\x4c\x46',  # Linux executable
            'PDF': rb'\x25\x50\x44\x46',  # PDF file
            'ZIP': rb'\x50\x4b\x03\x04',  # ZIP archive
            'RAR': rb'\x52\x61\x72\x21',  # RAR archive
            'JPEG': rb'\xff\xd8\xff',  # JPEG image
            'PNG': rb'\x89\x50\x4e\x47',  # PNG image
        }
        
        # Threat categories
        self.threat_categories = {
            'virus': 'Virus',
            'trojan': 'Trojan Horse',
            'worm': 'Worm',
            'ransomware': 'Ransomware',
            'spyware': 'Spyware',
            'adware': 'Adware',
            'rootkit': 'Rootkit',
            'backdoor': 'Backdoor',
            'keylogger': 'Keylogger',
            'suspicious': 'Suspicious File'
        }
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Analyze a file for threats"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'low',
            'confidence': 0,
            'description': '',
            'details': {}
        }
        
        try:
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                return result
            
            # Get file info
            file_info = self._get_file_info(file_path)
            result['details']['file_info'] = file_info
            
            # Check file hash against known malware
            hash_result = self._check_hash_signature(file_path)
            if hash_result['is_threat']:
                result.update(hash_result)
                return result
            
            # Check file content for suspicious patterns
            content_result = self._check_content_patterns(file_path)
            if content_result['is_threat']:
                result.update(content_result)
                return result
            
            # Check file structure
            structure_result = self._check_file_structure(file_path)
            if structure_result['is_threat']:
                result.update(structure_result)
                return result
            
            # Check filename patterns
            filename_result = self._check_filename_patterns(file_path)
            if filename_result['is_threat']:
                result.update(filename_result)
                return result
            
            # Behavioral analysis
            behavior_result = self._analyze_behavior(file_path)
            if behavior_result['is_threat']:
                result.update(behavior_result)
                return result
            
        except Exception as e:
            result['details']['error'] = str(e)
        
        return result
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get basic file information"""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'accessed': stat.st_atime,
                'extension': Path(file_path).suffix.lower(),
                'name': Path(file_path).name
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _check_hash_signature(self, file_path: str) -> Dict[str, Any]:
        """Check file hash against known malware signatures"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'high',
            'confidence': 100,
            'description': ''
        }
        
        try:
            # Calculate MD5 hash
            md5_hash = self._calculate_md5(file_path)
            
            # Check against known signatures
            for threat_name, signature_hash in self.malware_signatures.items():
                if md5_hash == signature_hash:
                    result['is_threat'] = True
                    result['threat_type'] = 'virus'
                    result['threat_name'] = threat_name
                    result['description'] = f'Known malware detected: {threat_name}'
                    break
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _check_content_patterns(self, file_path: str) -> Dict[str, Any]:
        """Check file content for suspicious patterns"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'medium',
            'confidence': 70,
            'description': ''
        }
        
        try:
            # Read file content (first 1MB only for performance)
            with open(file_path, 'rb') as f:
                content = f.read(1024 * 1024)  # 1MB
            
            suspicious_count = 0
            found_patterns = []
            
            # Check for suspicious patterns
            for pattern in self.suspicious_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    suspicious_count += 1
                    found_patterns.append(pattern.decode('utf-8', errors='ignore'))
            
            # Check for suspicious strings
            content_str = content.decode('utf-8', errors='ignore').lower()
            for sus_string in self.suspicious_strings:
                if sus_string.lower() in content_str:
                    suspicious_count += 1
                    found_patterns.append(sus_string)
            
            # Determine threat level
            if suspicious_count >= 5:
                result['is_threat'] = True
                result['threat_type'] = 'suspicious'
                result['threat_name'] = 'Suspicious Content'
                result['severity'] = 'high'
                result['description'] = f'File contains {suspicious_count} suspicious patterns'
            elif suspicious_count >= 3:
                result['is_threat'] = True
                result['threat_type'] = 'suspicious'
                result['threat_name'] = 'Potentially Suspicious'
                result['severity'] = 'medium'
                result['description'] = f'File contains {suspicious_count} potentially suspicious patterns'
            
            result['details'] = {
                'suspicious_count': suspicious_count,
                'patterns_found': found_patterns[:10]  # Limit to first 10
            }
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _check_file_structure(self, file_path: str) -> Dict[str, Any]:
        """Check file structure for anomalies"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'medium',
            'confidence': 60,
            'description': ''
        }
        
        try:
            with open(file_path, 'rb') as f:
                header = f.read(512)  # Read first 512 bytes
            
            # Check file signature
            file_type = self._identify_file_type(header)
            file_ext = Path(file_path).suffix.lower()
            
            # Check for file type mismatch
            expected_types = {
                '.exe': ['PE'],
                '.dll': ['PE'],
                '.pdf': ['PDF'],
                '.jpg': ['JPEG'],
                '.jpeg': ['JPEG'],
                '.png': ['PNG'],
                '.zip': ['ZIP'],
                '.rar': ['RAR']
            }
            
            if file_ext in expected_types:
                if file_type not in expected_types[file_ext]:
                    result['is_threat'] = True
                    result['threat_type'] = 'suspicious'
                    result['threat_name'] = 'File Type Mismatch'
                    result['description'] = f'File extension {file_ext} does not match content type {file_type}'
            
            # Check for embedded executables
            if file_type != 'PE' and rb'\x4d\x5a' in header[100:]:  # MZ header not at beginning
                result['is_threat'] = True
                result['threat_type'] = 'suspicious'
                result['threat_name'] = 'Embedded Executable'
                result['description'] = 'File contains embedded executable code'
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _check_filename_patterns(self, file_path: str) -> Dict[str, Any]:
        """Check filename for suspicious patterns"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'low',
            'confidence': 40,
            'description': ''
        }
        
        try:
            filename = Path(file_path).name.lower()
            
            # Suspicious filename patterns
            suspicious_names = [
                'virus', 'trojan', 'malware', 'hack', 'crack',
                'keygen', 'patch', 'loader', 'inject',
                'backdoor', 'rootkit', 'keylog', 'steal',
                'bitcoin', 'miner', 'ransom', 'encrypt'
            ]
            
            for sus_name in suspicious_names:
                if sus_name in filename:
                    result['is_threat'] = True
                    result['threat_type'] = 'suspicious'
                    result['threat_name'] = 'Suspicious Filename'
                    result['description'] = f'Filename contains suspicious term: {sus_name}'
                    break
            
            # Check for double extensions
            if filename.count('.') >= 2:
                parts = filename.split('.')
                if len(parts) >= 3 and parts[-2] in ['exe', 'scr', 'bat', 'cmd']:
                    result['is_threat'] = True
                    result['threat_type'] = 'suspicious'
                    result['threat_name'] = 'Double Extension'
                    result['description'] = 'File has suspicious double extension'
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _analyze_behavior(self, file_path: str) -> Dict[str, Any]:
        """Analyze file for behavioral indicators"""
        result = {
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'medium',
            'confidence': 50,
            'description': ''
        }
        
        try:
            # Check file location
            path_lower = file_path.lower()
            
            # Suspicious locations
            if any(loc in path_lower for loc in ['temp', 'tmp', 'appdata\\local\\temp']):
                if Path(file_path).suffix.lower() in ['.exe', '.scr', '.bat', '.cmd']:
                    result['is_threat'] = True
                    result['threat_type'] = 'suspicious'
                    result['threat_name'] = 'Suspicious Location'
                    result['description'] = 'Executable file in temporary directory'
            
            # Check file size anomalies
            file_size = os.path.getsize(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            # Unusually small executables
            if file_ext in ['.exe', '.dll'] and file_size < 1024:  # Less than 1KB
                result['is_threat'] = True
                result['threat_type'] = 'suspicious'
                result['threat_name'] = 'Unusually Small Executable'
                result['description'] = f'Executable file is unusually small ({file_size} bytes)'
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def _calculate_md5(self, file_path: str) -> str:
        """Calculate MD5 hash of file"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def _identify_file_type(self, header: bytes) -> str:
        """Identify file type from header"""
        for file_type, signature in self.file_signatures.items():
            if header.startswith(signature):
                return file_type
        return 'Unknown'
    
    def update_signatures(self, new_signatures: Dict[str, str]) -> bool:
        """Update malware signatures"""
        try:
            self.malware_signatures.update(new_signatures)
            return True
        except Exception:
            return False
    
    def get_threat_info(self, threat_type: str) -> Dict[str, str]:
        """Get information about a threat type"""
        return {
            'name': self.threat_categories.get(threat_type, 'Unknown Threat'),
            'description': f'Detected threat of type: {threat_type}'
        }
