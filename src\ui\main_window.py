#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Main Window for Cyber Shield Pro
The primary application window that manages all UI components
"""

import tkinter as tk
import customtkinter as ctk
import threading
import time
from typing import Optional, Dict, Any
from .cyber_theme import CyberTheme
from .login_window import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .dashboard import Dashboard
from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..database.db_manager import DatabaseManager

class CyberShieldApp:
    """Main application window"""
    
    def __init__(self, app_name: str, version: str, copyright: str,
                 logger: Logger, config_manager: ConfigManager, db_manager: DatabaseManager):
        """Initialize the main application"""
        self.app_name = app_name
        self.version = version
        self.copyright = copyright
        self.logger = logger
        self.config_manager = config_manager
        self.db_manager = db_manager
        
        # Initialize theme
        self.theme = CyberTheme()
        self.theme.configure_customtkinter()
        
        # Application state
        self.current_user = None
        self.is_authenticated = False
        self.current_window = None
        
        # Initialize main window
        self.root = None
        self.login_window = None
        self.dashboard = None
        
        self._setup_main_window()
    
    def _setup_main_window(self):
        """Setup the main application window"""
        self.root = ctk.CTk()
        self.root.title(f"{self.app_name} v{self.version}")
        self.root.geometry("1200x800")
        self.root.minsize(
            self.theme.sizes['window_min_width'],
            self.theme.sizes['window_min_height']
        )
        
        # Configure window
        self.root.configure(fg_color=self.theme.colors['bg_primary'])
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap("assets/icons/app_icon.ico")
        except:
            pass
        
        # Center window on screen
        self._center_window()
        
        # Setup window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        
        # Start with login window
        self.show_login()
    
    def _center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def show_login(self):
        """Show login window"""
        try:
            # Clear current window
            self._clear_current_window()
            
            # Create login window
            self.login_window = LoginWindow(
                parent=self.root,
                theme=self.theme,
                app_name=self.app_name,
                version=self.version,
                copyright=self.copyright,
                on_login_success=self._on_login_success,
                on_register_success=self._on_register_success,
                logger=self.logger,
                config_manager=self.config_manager,
                db_manager=self.db_manager
            )
            
            self.current_window = self.login_window
            self.logger.info("Login window displayed")
            
        except Exception as e:
            self.logger.error(f"Error showing login window: {e}")
    
    def show_dashboard(self):
        """Show main dashboard"""
        try:
            # Clear current window
            self._clear_current_window()
            
            # Create dashboard
            self.dashboard = Dashboard(
                parent=self.root,
                theme=self.theme,
                current_user=self.current_user,
                app_name=self.app_name,
                version=self.version,
                on_logout=self._on_logout,
                logger=self.logger,
                config_manager=self.config_manager,
                db_manager=self.db_manager
            )
            
            self.current_window = self.dashboard
            self.logger.info("Dashboard displayed")
            
        except Exception as e:
            self.logger.error(f"Error showing dashboard: {e}")
    
    def _on_login_success(self, user_data: Dict[str, Any]):
        """Handle successful login"""
        try:
            self.current_user = user_data
            self.is_authenticated = True
            
            self.logger.info(f"User {user_data['username']} logged in successfully")
            
            # Show dashboard
            self.show_dashboard()
            
        except Exception as e:
            self.logger.error(f"Error handling login success: {e}")
    
    def _on_register_success(self, user_data: Dict[str, Any]):
        """Handle successful registration"""
        try:
            self.logger.info(f"User {user_data['username']} registered successfully")
            
            # Show login window again
            self.show_login()
            
        except Exception as e:
            self.logger.error(f"Error handling registration success: {e}")
    
    def _on_logout(self):
        """Handle user logout"""
        try:
            if self.current_user:
                self.logger.info(f"User {self.current_user['username']} logged out")
            
            self.current_user = None
            self.is_authenticated = False
            
            # Show login window
            self.show_login()
            
        except Exception as e:
            self.logger.error(f"Error handling logout: {e}")
    
    def _clear_current_window(self):
        """Clear the current window content"""
        try:
            # Destroy all child widgets
            for widget in self.root.winfo_children():
                widget.destroy()
            
            # Reset current window reference
            self.current_window = None
            
        except Exception as e:
            self.logger.error(f"Error clearing current window: {e}")
    
    def _on_closing(self):
        """Handle window closing"""
        try:
            self.logger.info("Application closing...")
            
            # Cleanup resources
            if self.db_manager:
                self.db_manager.close()
            
            # Save configuration
            if self.config_manager:
                self.config_manager.save_config()
            
            # Destroy window
            self.root.destroy()
            
        except Exception as e:
            self.logger.error(f"Error during application closing: {e}")
            self.root.destroy()
    
    def run(self):
        """Run the main application loop"""
        try:
            self.logger.info("Starting Cyber Shield Pro application")
            self.root.mainloop()
            
        except Exception as e:
            self.logger.error(f"Error in main application loop: {e}")
            raise
