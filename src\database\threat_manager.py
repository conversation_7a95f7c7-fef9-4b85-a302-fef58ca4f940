#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Threat Management for Cyber Shield Pro
Handles threat detection, quarantine, and management
"""

import datetime
import hashlib
import os
from typing import Optional, List, Dict, Any
from .db_manager import DatabaseManager

class ThreatManager:
    """Threat management class"""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize threat manager"""
        self.db = db_manager
    
    def add_threat(self, user_id: int, threat_type: str, threat_name: str, 
                   file_path: str = None, severity: str = 'medium') -> Optional[int]:
        """Add a new threat to the database"""
        try:
            # Calculate file hash if file path provided
            file_hash = None
            if file_path and os.path.exists(file_path):
                file_hash = self._calculate_file_hash(file_path)
            
            query = '''
                INSERT INTO threats (user_id, threat_type, threat_name, file_path, 
                                   file_hash, severity, status, detected_at)
                VALUES (?, ?, ?, ?, ?, ?, 'detected', CURRENT_TIMESTAMP)
            '''
            
            if self.db.execute_update(query, (user_id, threat_type, threat_name, 
                                            file_path, file_hash, severity)):
                return self.db.get_last_insert_id()
            
            return None
            
        except Exception as e:
            print(f"Error adding threat: {e}")
            return None
    
    def quarantine_threat(self, threat_id: int, quarantine_path: str) -> bool:
        """Move threat to quarantine"""
        try:
            query = '''
                UPDATE threats 
                SET status = 'quarantined', action_taken = 'quarantine', 
                    quarantine_path = ?, resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (quarantine_path, threat_id))
            
        except Exception as e:
            print(f"Error quarantining threat: {e}")
            return False
    
    def delete_threat(self, threat_id: int) -> bool:
        """Mark threat as deleted"""
        try:
            query = '''
                UPDATE threats 
                SET status = 'deleted', action_taken = 'delete', 
                    resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (threat_id,))
            
        except Exception as e:
            print(f"Error deleting threat: {e}")
            return False
    
    def ignore_threat(self, threat_id: int) -> bool:
        """Mark threat as ignored"""
        try:
            query = '''
                UPDATE threats 
                SET status = 'ignored', action_taken = 'ignore', 
                    resolved_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (threat_id,))
            
        except Exception as e:
            print(f"Error ignoring threat: {e}")
            return False
    
    def get_threat_by_id(self, threat_id: int) -> Optional[Dict[str, Any]]:
        """Get threat by ID"""
        try:
            query = "SELECT * FROM threats WHERE id = ?"
            result = self.db.execute_query(query, (threat_id,))
            
            if result:
                return dict(result[0])
            return None
            
        except Exception as e:
            print(f"Error getting threat by ID: {e}")
            return None
    
    def get_threats_by_user(self, user_id: int, status: str = None) -> List[Dict[str, Any]]:
        """Get threats for a specific user"""
        try:
            if status:
                query = '''
                    SELECT * FROM threats 
                    WHERE user_id = ? AND status = ?
                    ORDER BY detected_at DESC
                '''
                result = self.db.execute_query(query, (user_id, status))
            else:
                query = '''
                    SELECT * FROM threats 
                    WHERE user_id = ?
                    ORDER BY detected_at DESC
                '''
                result = self.db.execute_query(query, (user_id,))
            
            return [dict(row) for row in result] if result else []
            
        except Exception as e:
            print(f"Error getting threats by user: {e}")
            return []
    
    def get_all_threats(self, status: str = None) -> List[Dict[str, Any]]:
        """Get all threats (admin only)"""
        try:
            if status:
                query = '''
                    SELECT t.*, u.username 
                    FROM threats t
                    LEFT JOIN users u ON t.user_id = u.id
                    WHERE t.status = ?
                    ORDER BY t.detected_at DESC
                '''
                result = self.db.execute_query(query, (status,))
            else:
                query = '''
                    SELECT t.*, u.username 
                    FROM threats t
                    LEFT JOIN users u ON t.user_id = u.id
                    ORDER BY t.detected_at DESC
                '''
                result = self.db.execute_query(query)
            
            return [dict(row) for row in result] if result else []
            
        except Exception as e:
            print(f"Error getting all threats: {e}")
            return []
    
    def get_threat_statistics(self, user_id: int = None) -> Dict[str, int]:
        """Get threat statistics"""
        try:
            stats = {
                'total_threats': 0,
                'active_threats': 0,
                'quarantined': 0,
                'deleted': 0,
                'ignored': 0,
                'high_severity': 0,
                'medium_severity': 0,
                'low_severity': 0
            }
            
            # Base query condition
            where_clause = "WHERE user_id = ?" if user_id else ""
            params = (user_id,) if user_id else ()
            
            # Total threats
            query = f"SELECT COUNT(*) as count FROM threats {where_clause}"
            result = self.db.execute_query(query, params)
            if result:
                stats['total_threats'] = result[0]['count']
            
            # Threats by status
            statuses = ['detected', 'quarantined', 'deleted', 'ignored']
            for status in statuses:
                query = f"SELECT COUNT(*) as count FROM threats {where_clause}"
                if where_clause:
                    query += " AND status = ?"
                    query_params = params + (status,)
                else:
                    query += "WHERE status = ?"
                    query_params = (status,)
                
                result = self.db.execute_query(query, query_params)
                if result:
                    if status == 'detected':
                        stats['active_threats'] = result[0]['count']
                    else:
                        stats[status] = result[0]['count']
            
            # Threats by severity
            severities = ['high', 'medium', 'low']
            for severity in severities:
                query = f"SELECT COUNT(*) as count FROM threats {where_clause}"
                if where_clause:
                    query += " AND severity = ?"
                    query_params = params + (severity,)
                else:
                    query += "WHERE severity = ?"
                    query_params = (severity,)
                
                result = self.db.execute_query(query, query_params)
                if result:
                    stats[f'{severity}_severity'] = result[0]['count']
            
            return stats
            
        except Exception as e:
            print(f"Error getting threat statistics: {e}")
            return {}
    
    def get_recent_threats(self, user_id: int = None, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent threats"""
        try:
            if user_id:
                query = '''
                    SELECT * FROM threats 
                    WHERE user_id = ?
                    ORDER BY detected_at DESC
                    LIMIT ?
                '''
                result = self.db.execute_query(query, (user_id, limit))
            else:
                query = '''
                    SELECT t.*, u.username 
                    FROM threats t
                    LEFT JOIN users u ON t.user_id = u.id
                    ORDER BY t.detected_at DESC
                    LIMIT ?
                '''
                result = self.db.execute_query(query, (limit,))
            
            return [dict(row) for row in result] if result else []
            
        except Exception as e:
            print(f"Error getting recent threats: {e}")
            return []
    
    def cleanup_old_threats(self, days: int = 90) -> bool:
        """Clean up old resolved threats"""
        try:
            query = '''
                DELETE FROM threats 
                WHERE status IN ('deleted', 'ignored') 
                AND resolved_at < datetime('now', '-{} days')
            '''.format(days)
            
            return self.db.execute_update(query)
            
        except Exception as e:
            print(f"Error cleaning up old threats: {e}")
            return False
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA256 hash of a file"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            print(f"Error calculating file hash: {e}")
            return ""
