#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Registry Manager for Cyber Shield Pro
Windows Registry monitoring and management with security focus
"""

import winreg
import threading
import time
import ctypes
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from ..utils.logger import Logger

class RegistryHive(Enum):
    HKEY_CLASSES_ROOT = winreg.HKEY_CLASSES_ROOT
    HKEY_CURRENT_USER = winreg.HKEY_CURRENT_USER
    HKEY_LOCAL_MACHINE = winreg.HKEY_LOCAL_MACHINE
    HKEY_USERS = winreg.HKEY_USERS
    HKEY_CURRENT_CONFIG = winreg.HKEY_CURRENT_CONFIG

class RegistryValueType(Enum):
    REG_NONE = winreg.REG_NONE
    REG_SZ = winreg.REG_SZ
    REG_EXPAND_SZ = winreg.REG_EXPAND_SZ
    REG_BINARY = winreg.REG_BINARY
    REG_DWORD = winreg.REG_DWORD
    REG_DWORD_BIG_ENDIAN = winreg.REG_DWORD_BIG_ENDIAN
    REG_LINK = winreg.REG_LINK
    REG_MULTI_SZ = winreg.REG_MULTI_SZ
    REG_RESOURCE_LIST = winreg.REG_RESOURCE_LIST
    REG_FULL_RESOURCE_DESCRIPTOR = winreg.REG_FULL_RESOURCE_DESCRIPTOR
    REG_RESOURCE_REQUIREMENTS_LIST = winreg.REG_RESOURCE_REQUIREMENTS_LIST
    REG_QWORD = winreg.REG_QWORD

@dataclass
class RegistryEntry:
    """Registry entry information"""
    hive: str
    key_path: str
    value_name: str
    value_data: Any
    value_type: RegistryValueType
    is_suspicious: bool
    threat_level: str
    last_modified: datetime
    description: str

@dataclass
class RegistryChange:
    """Registry change information"""
    timestamp: datetime
    action: str  # 'created', 'modified', 'deleted'
    hive: str
    key_path: str
    value_name: str
    old_value: Any
    new_value: Any
    is_suspicious: bool

class RegistryManager:
    """Windows Registry Management and Monitoring"""
    
    def __init__(self, logger: Logger):
        """Initialize registry manager"""
        self.logger = logger
        
        # Registry monitoring
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()
        
        # Registry snapshots for change detection
        self.registry_snapshots = {}
        self.registry_changes = []
        
        # Critical registry keys to monitor
        self.critical_keys = {
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run': 'Startup Programs',
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce': 'Run Once Programs',
            'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run': 'User Startup Programs',
            'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce': 'User Run Once Programs',
            'HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services': 'System Services',
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Winlogon': 'Windows Logon',
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies': 'System Policies',
            'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies': 'User Policies',
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\exefile\\shell\\open\\command': 'EXE File Association',
            'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\\Image File Execution Options': 'Image File Execution Options'
        }
        
        # Suspicious registry patterns
        self.suspicious_patterns = [
            'virus', 'trojan', 'malware', 'hack', 'crack',
            'keygen', 'patch', 'loader', 'inject', 'bot',
            'miner', 'crypto', 'backdoor', 'rootkit'
        ]
        
        # Registry alerts
        self.registry_alerts = []
        
        # Take initial snapshot
        self._take_registry_snapshot()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def start_monitoring(self) -> bool:
        """Start registry monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            
            self.is_monitoring = True
            self.logger.info("Registry monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start registry monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop registry monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Registry monitoring stopped")
    
    def _monitor_worker(self):
        """Registry monitoring worker thread"""
        while self.is_monitoring:
            try:
                # Check for registry changes
                self._check_registry_changes()
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in registry monitoring: {e}")
                time.sleep(120)
    
    def _take_registry_snapshot(self):
        """Take snapshot of critical registry keys"""
        try:
            snapshot = {}
            
            for key_path, description in self.critical_keys.items():
                try:
                    hive_name, subkey_path = key_path.split('\\', 1)
                    hive = getattr(winreg, hive_name)
                    
                    key_data = self._read_registry_key(hive, subkey_path)
                    if key_data:
                        snapshot[key_path] = key_data
                        
                except Exception as e:
                    self.logger.warning(f"Could not snapshot registry key {key_path}: {e}")
                    continue
            
            self.registry_snapshots[datetime.now()] = snapshot
            
            # Keep only last 10 snapshots
            if len(self.registry_snapshots) > 10:
                oldest_time = min(self.registry_snapshots.keys())
                del self.registry_snapshots[oldest_time]
            
        except Exception as e:
            self.logger.error(f"Error taking registry snapshot: {e}")
    
    def _read_registry_key(self, hive: int, key_path: str) -> Optional[Dict[str, Any]]:
        """Read all values from a registry key"""
        try:
            key_data = {'values': {}, 'subkeys': []}
            
            with winreg.OpenKey(hive, key_path, 0, winreg.KEY_READ) as key:
                # Read values
                try:
                    i = 0
                    while True:
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(key, i)
                            key_data['values'][value_name] = {
                                'data': value_data,
                                'type': value_type
                            }
                            i += 1
                        except OSError:
                            break
                except Exception as e:
                    pass
                
                # Read subkeys
                try:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            key_data['subkeys'].append(subkey_name)
                            i += 1
                        except OSError:
                            break
                except Exception as e:
                    pass
            
            return key_data
            
        except Exception as e:
            return None
    
    def _check_registry_changes(self):
        """Check for changes in critical registry keys"""
        try:
            current_snapshot = {}
            
            # Take new snapshot
            for key_path, description in self.critical_keys.items():
                try:
                    hive_name, subkey_path = key_path.split('\\', 1)
                    hive = getattr(winreg, hive_name)
                    
                    key_data = self._read_registry_key(hive, subkey_path)
                    if key_data:
                        current_snapshot[key_path] = key_data
                        
                except Exception as e:
                    continue
            
            # Compare with last snapshot
            if self.registry_snapshots:
                last_snapshot_time = max(self.registry_snapshots.keys())
                last_snapshot = self.registry_snapshots[last_snapshot_time]
                
                changes = self._compare_snapshots(last_snapshot, current_snapshot)
                
                for change in changes:
                    self._handle_registry_change(change)
            
            # Store current snapshot
            self.registry_snapshots[datetime.now()] = current_snapshot
            
        except Exception as e:
            self.logger.error(f"Error checking registry changes: {e}")
    
    def _compare_snapshots(self, old_snapshot: Dict, new_snapshot: Dict) -> List[RegistryChange]:
        """Compare two registry snapshots"""
        changes = []
        
        try:
            # Check all keys in new snapshot
            for key_path, new_data in new_snapshot.items():
                old_data = old_snapshot.get(key_path, {'values': {}, 'subkeys': []})
                
                # Check for new or modified values
                for value_name, value_info in new_data['values'].items():
                    old_value_info = old_data['values'].get(value_name)
                    
                    if old_value_info is None:
                        # New value
                        change = RegistryChange(
                            timestamp=datetime.now(),
                            action='created',
                            hive=key_path.split('\\')[0],
                            key_path=key_path,
                            value_name=value_name,
                            old_value=None,
                            new_value=value_info['data'],
                            is_suspicious=self._is_suspicious_registry_change(key_path, value_name, value_info['data'])
                        )
                        changes.append(change)
                        
                    elif old_value_info['data'] != value_info['data']:
                        # Modified value
                        change = RegistryChange(
                            timestamp=datetime.now(),
                            action='modified',
                            hive=key_path.split('\\')[0],
                            key_path=key_path,
                            value_name=value_name,
                            old_value=old_value_info['data'],
                            new_value=value_info['data'],
                            is_suspicious=self._is_suspicious_registry_change(key_path, value_name, value_info['data'])
                        )
                        changes.append(change)
                
                # Check for deleted values
                for value_name, old_value_info in old_data['values'].items():
                    if value_name not in new_data['values']:
                        change = RegistryChange(
                            timestamp=datetime.now(),
                            action='deleted',
                            hive=key_path.split('\\')[0],
                            key_path=key_path,
                            value_name=value_name,
                            old_value=old_value_info['data'],
                            new_value=None,
                            is_suspicious=False  # Deletions are generally less suspicious
                        )
                        changes.append(change)
            
            return changes
            
        except Exception as e:
            self.logger.error(f"Error comparing snapshots: {e}")
            return []
    
    def _is_suspicious_registry_change(self, key_path: str, value_name: str, value_data: Any) -> bool:
        """Check if registry change is suspicious"""
        try:
            # Convert value to string for analysis
            value_str = str(value_data).lower() if value_data else ""
            
            # Check for suspicious patterns in value data
            if any(pattern in value_str for pattern in self.suspicious_patterns):
                return True
            
            # Check for suspicious file paths
            suspicious_paths = [
                'temp', 'tmp', 'appdata\\roaming', 'programdata',
                'users\\public', 'windows\\temp'
            ]
            
            if any(sus_path in value_str for sus_path in suspicious_paths):
                return True
            
            # Check for suspicious registry locations
            if 'run' in key_path.lower() and value_str:
                # Startup entries with suspicious characteristics
                if not value_str.startswith(('c:\\program files', 'c:\\windows')):
                    return True
            
            # Check for policy modifications
            if 'policies' in key_path.lower():
                return True
            
            # Check for file association changes
            if 'classes' in key_path.lower() and 'shell' in key_path.lower():
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking suspicious registry change: {e}")
            return False
    
    def _handle_registry_change(self, change: RegistryChange):
        """Handle detected registry change"""
        try:
            # Add to changes list
            self.registry_changes.append(change)
            
            # Keep only last 100 changes
            if len(self.registry_changes) > 100:
                self.registry_changes = self.registry_changes[-100:]
            
            # Log change
            self.logger.info(f"Registry change detected: {change.action} {change.key_path}\\{change.value_name}")
            
            # Create alert if suspicious
            if change.is_suspicious:
                self._create_registry_alert(
                    'suspicious_change',
                    f'Suspicious registry change: {change.key_path}\\{change.value_name}',
                    change
                )
            
        except Exception as e:
            self.logger.error(f"Error handling registry change: {e}")
    
    def _create_registry_alert(self, alert_type: str, message: str, change: RegistryChange):
        """Create registry alert"""
        try:
            alert = {
                'timestamp': datetime.now(),
                'type': alert_type,
                'message': message,
                'change': asdict(change)
            }
            
            self.registry_alerts.append(alert)
            
            # Keep only last 50 alerts
            if len(self.registry_alerts) > 50:
                self.registry_alerts = self.registry_alerts[-50:]
            
            self.logger.warning(f"Registry alert: {message}")
            
        except Exception as e:
            self.logger.error(f"Error creating registry alert: {e}")
    
    def read_registry_value(self, hive: RegistryHive, key_path: str, value_name: str) -> Optional[Tuple[Any, RegistryValueType]]:
        """Read a specific registry value"""
        try:
            with winreg.OpenKey(hive.value, key_path, 0, winreg.KEY_READ) as key:
                value_data, value_type = winreg.QueryValueEx(key, value_name)
                return value_data, RegistryValueType(value_type)
                
        except Exception as e:
            self.logger.error(f"Error reading registry value {hive.name}\\{key_path}\\{value_name}: {e}")
            return None
    
    def write_registry_value(self, hive: RegistryHive, key_path: str, value_name: str, 
                           value_data: Any, value_type: RegistryValueType) -> bool:
        """Write a registry value"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to write registry values")
                return False
            
            with winreg.CreateKey(hive.value, key_path) as key:
                winreg.SetValueEx(key, value_name, 0, value_type.value, value_data)
                
            self.logger.info(f"Registry value written: {hive.name}\\{key_path}\\{value_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error writing registry value: {e}")
            return False
    
    def delete_registry_value(self, hive: RegistryHive, key_path: str, value_name: str) -> bool:
        """Delete a registry value"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to delete registry values")
                return False
            
            with winreg.OpenKey(hive.value, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.DeleteValue(key, value_name)
                
            self.logger.info(f"Registry value deleted: {hive.name}\\{key_path}\\{value_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting registry value: {e}")
            return False
    
    def create_registry_key(self, hive: RegistryHive, key_path: str) -> bool:
        """Create a registry key"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to create registry keys")
                return False
            
            winreg.CreateKey(hive.value, key_path)
            self.logger.info(f"Registry key created: {hive.name}\\{key_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating registry key: {e}")
            return False
    
    def delete_registry_key(self, hive: RegistryHive, key_path: str) -> bool:
        """Delete a registry key"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to delete registry keys")
                return False
            
            winreg.DeleteKey(hive.value, key_path)
            self.logger.info(f"Registry key deleted: {hive.name}\\{key_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting registry key: {e}")
            return False
    
    def get_startup_programs(self) -> List[Dict[str, Any]]:
        """Get list of startup programs from registry"""
        startup_programs = []
        
        startup_keys = [
            (RegistryHive.HKEY_LOCAL_MACHINE, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run'),
            (RegistryHive.HKEY_LOCAL_MACHINE, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce'),
            (RegistryHive.HKEY_CURRENT_USER, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run'),
            (RegistryHive.HKEY_CURRENT_USER, 'SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\RunOnce')
        ]
        
        for hive, key_path in startup_keys:
            try:
                with winreg.OpenKey(hive.value, key_path, 0, winreg.KEY_READ) as key:
                    i = 0
                    while True:
                        try:
                            value_name, value_data, value_type = winreg.EnumValue(key, i)
                            
                            is_suspicious = self._is_suspicious_registry_change(key_path, value_name, value_data)
                            
                            startup_programs.append({
                                'name': value_name,
                                'command': value_data,
                                'location': f"{hive.name}\\{key_path}",
                                'type': RegistryValueType(value_type).name,
                                'is_suspicious': is_suspicious,
                                'threat_level': 'high' if is_suspicious else 'low'
                            })
                            
                            i += 1
                        except OSError:
                            break
                            
            except Exception as e:
                continue
        
        return startup_programs
    
    def get_registry_changes(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent registry changes"""
        recent_changes = self.registry_changes[-limit:]
        return [asdict(change) for change in recent_changes]
    
    def get_registry_alerts(self) -> List[Dict[str, Any]]:
        """Get registry alerts"""
        return self.registry_alerts
    
    def get_registry_status(self) -> Dict[str, Any]:
        """Get registry monitoring status"""
        try:
            return {
                'is_monitoring': self.is_monitoring,
                'has_admin_rights': self.has_admin_rights,
                'monitored_keys': len(self.critical_keys),
                'total_changes': len(self.registry_changes),
                'suspicious_changes': len([c for c in self.registry_changes if c.is_suspicious]),
                'recent_alerts': len(self.registry_alerts),
                'snapshots_count': len(self.registry_snapshots)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting registry status: {e}")
            return {}
    
    def backup_registry_key(self, hive: RegistryHive, key_path: str) -> Optional[Dict[str, Any]]:
        """Backup a registry key"""
        try:
            backup_data = self._read_registry_key(hive.value, key_path)
            if backup_data:
                backup_data['backup_time'] = datetime.now().isoformat()
                backup_data['hive'] = hive.name
                backup_data['key_path'] = key_path
                
            return backup_data
            
        except Exception as e:
            self.logger.error(f"Error backing up registry key: {e}")
            return None
    
    def restore_registry_key(self, backup_data: Dict[str, Any]) -> bool:
        """Restore a registry key from backup"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to restore registry keys")
                return False
            
            hive = getattr(RegistryHive, backup_data['hive'])
            key_path = backup_data['key_path']
            
            # Create the key
            with winreg.CreateKey(hive.value, key_path) as key:
                # Restore values
                for value_name, value_info in backup_data['values'].items():
                    winreg.SetValueEx(key, value_name, 0, value_info['type'], value_info['data'])
            
            self.logger.info(f"Registry key restored: {hive.name}\\{key_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error restoring registry key: {e}")
            return False
