#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Build Executable for Cyber Shield Pro
Creates a standalone executable file using PyInstaller
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        print("✓ PyInstaller is available")
        return True
    except ImportError:
        print("✗ PyInstaller not found. Installing...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✓ PyInstaller installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("✗ Failed to install PyInstaller")
            return False

def create_spec_file():
    """Create PyInstaller spec file"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('assets', 'assets'),
        ('src', 'src'),
    ],
    hiddenimports=[
        'customtkinter',
        'PIL',
        'PIL._tkinter_finder',
        'requests',
        'psutil',
        'cryptography',
        'sqlite3',
        'hashlib',
        'secrets',
        'threading',
        'datetime',
        'json',
        'os',
        'sys',
        'pathlib',
        'typing',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CyberShieldPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icons/app_icon.ico' if os.path.exists('assets/icons/app_icon.ico') else None,
    version_file='version_info.txt' if os.path.exists('version_info.txt') else None,
)
'''
    
    with open('cyber_shield.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ Spec file created")

def create_version_info():
    """Create version info file for Windows executable"""
    version_info = '''# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1, 0, 0, 0),
    prodvers=(1, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'Cyber Shield Pro Team'),
        StringStruct(u'FileDescription', u'Cyber Shield Pro - Advanced Cybersecurity Protection'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'CyberShieldPro'),
        StringStruct(u'LegalCopyright', u'© 2025 Cyber Shield Pro Team. All rights reserved. Developed in Palestine 🇵🇸'),
        StringStruct(u'OriginalFilename', u'CyberShieldPro.exe'),
        StringStruct(u'ProductName', u'Cyber Shield Pro'),
        StringStruct(u'ProductVersion', u'*******')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
    
    with open('version_info.txt', 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print("✓ Version info file created")

def create_app_icon():
    """Create a simple app icon if none exists"""
    icon_dir = Path('assets/icons')
    icon_dir.mkdir(parents=True, exist_ok=True)
    
    icon_path = icon_dir / 'app_icon.ico'
    
    if not icon_path.exists():
        print("ℹ️  No app icon found. You can add app_icon.ico to assets/icons/ for a custom icon.")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 Building executable...")
    
    try:
        # Clean previous builds
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # Build using spec file
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "cyber_shield.spec"]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Executable built successfully!")
            
            # Check if executable was created
            exe_path = Path('dist/CyberShieldPro.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"✓ Executable created: {exe_path}")
                print(f"✓ File size: {size_mb:.1f} MB")
                
                # Create distribution folder
                create_distribution()
                
                return True
            else:
                print("✗ Executable file not found in dist folder")
                return False
        else:
            print("✗ Build failed!")
            print("Error output:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ Build error: {e}")
        return False

def create_distribution():
    """Create distribution package"""
    print("📦 Creating distribution package...")
    
    dist_dir = Path('CyberShieldPro_v1.0.0')
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    
    dist_dir.mkdir()
    
    # Copy executable
    shutil.copy2('dist/CyberShieldPro.exe', dist_dir)
    
    # Copy documentation
    docs_to_copy = [
        'README.md',
        'QUICK_START_GUIDE.md',
        'INSTALLATION_GUIDE.md',
        'CHANGELOG.md',
        'LICENSE'
    ]
    
    for doc in docs_to_copy:
        if os.path.exists(doc):
            shutil.copy2(doc, dist_dir)
    
    # Copy batch files
    batch_files = [
        'START_CYBER_SHIELD.bat',
        'run.bat',
        'install_and_run.bat'
    ]
    
    for batch in batch_files:
        if os.path.exists(batch):
            shutil.copy2(batch, dist_dir)
    
    # Copy PowerShell script
    if os.path.exists('setup_and_run.ps1'):
        shutil.copy2('setup_and_run.ps1', dist_dir)
    
    # Create config directory
    config_dist = dist_dir / 'config'
    if os.path.exists('config'):
        shutil.copytree('config', config_dist)
    
    # Create empty directories
    for folder in ['database', 'logs', 'temp', 'assets']:
        (dist_dir / folder).mkdir(exist_ok=True)
    
    print(f"✓ Distribution package created: {dist_dir}")
    print(f"✓ Ready for distribution!")

def cleanup():
    """Clean up build files"""
    files_to_remove = [
        'cyber_shield.spec',
        'version_info.txt'
    ]
    
    dirs_to_remove = [
        'build',
        '__pycache__'
    ]
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
    
    for dir in dirs_to_remove:
        if os.path.exists(dir):
            shutil.rmtree(dir)
    
    print("✓ Build files cleaned up")

def main():
    """Main build function"""
    print("=" * 60)
    print("Cyber Shield Pro - Executable Builder")
    print("=" * 60)
    
    # Check requirements
    if not check_pyinstaller():
        return False
    
    # Create necessary files
    create_spec_file()
    create_version_info()
    create_app_icon()
    
    # Build executable
    success = build_executable()
    
    if success:
        print("\n🎉 Build completed successfully!")
        print("\nNext steps:")
        print("1. Test the executable in the dist/ folder")
        print("2. Check the distribution package")
        print("3. Create installer if needed")
        
        # Ask about cleanup
        response = input("\nClean up build files? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            cleanup()
    else:
        print("\n❌ Build failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nBuild cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
