#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Network Monitor for Cyber Shield Pro
Real-time network monitoring with threat detection and geographic tracking
"""

import os
import time
import threading
import socket
import struct
import psutil
import requests
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import ipaddress
from .ip_tracker import IPTracker
from .geo_locator import GeoLocator
from .traffic_analyzer import TrafficAnalyzer
from ..database.log_manager import LogManager
from ..utils.logger import Logger

@dataclass
class NetworkConnection:
    """Network connection data structure"""
    local_ip: str
    local_port: int
    remote_ip: str
    remote_port: int
    protocol: str
    status: str
    pid: Optional[int]
    process_name: Optional[str]
    timestamp: datetime
    bytes_sent: int = 0
    bytes_received: int = 0
    country: Optional[str] = None
    city: Optional[str] = None
    organization: Optional[str] = None
    threat_level: str = "low"
    is_malicious: bool = False

@dataclass
class NetworkInterface:
    """Network interface information"""
    name: str
    ip_address: str
    netmask: str
    broadcast: str
    mac_address: str
    is_up: bool
    bytes_sent: int
    bytes_received: int
    packets_sent: int
    packets_received: int
    errors_in: int
    errors_out: int
    drops_in: int
    drops_out: int

class NetworkMonitor:
    """Advanced Network Monitoring System"""
    
    def __init__(self, log_manager: LogManager, logger: Logger):
        """Initialize network monitor"""
        self.log_manager = log_manager
        self.logger = logger
        
        # Components
        self.ip_tracker = IPTracker()
        self.geo_locator = GeoLocator()
        self.traffic_analyzer = TrafficAnalyzer()
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_threads = []
        
        # Data storage
        self.active_connections = {}
        self.connection_history = []
        self.network_interfaces = {}
        self.bandwidth_usage = {}
        
        # Threat detection
        self.malicious_ips = set()
        self.suspicious_connections = []
        self.blocked_connections = []
        
        # Callbacks
        self.callbacks = {}
        
        # Configuration
        self.monitor_interval = 2  # seconds
        self.history_limit = 1000
        self.threat_check_interval = 30  # seconds
        
        # Load threat intelligence
        self._load_threat_intelligence()
    
    def start_monitoring(self, callbacks: Dict[str, Callable] = None) -> bool:
        """Start network monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.callbacks = callbacks or {}
            
            # Start monitoring threads
            self._start_connection_monitor()
            self._start_interface_monitor()
            self._start_traffic_monitor()
            self._start_threat_monitor()
            
            self.is_monitoring = True
            self.logger.info("Network monitoring started")
            
            if self.callbacks.get('on_monitoring_started'):
                self.callbacks['on_monitoring_started']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start network monitoring: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop network monitoring"""
        try:
            if not self.is_monitoring:
                return True
            
            self.is_monitoring = False
            
            # Wait for threads to finish
            for thread in self.monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=5)
            
            self.monitor_threads.clear()
            self.logger.info("Network monitoring stopped")
            
            if self.callbacks.get('on_monitoring_stopped'):
                self.callbacks['on_monitoring_stopped']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop network monitoring: {e}")
            return False
    
    def _start_connection_monitor(self):
        """Start connection monitoring thread"""
        def monitor_connections():
            while self.is_monitoring:
                try:
                    self._scan_connections()
                    time.sleep(self.monitor_interval)
                except Exception as e:
                    self.logger.error(f"Error in connection monitoring: {e}")
                    time.sleep(10)
        
        thread = threading.Thread(target=monitor_connections, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
    
    def _start_interface_monitor(self):
        """Start interface monitoring thread"""
        def monitor_interfaces():
            while self.is_monitoring:
                try:
                    self._scan_interfaces()
                    time.sleep(self.monitor_interval * 2)
                except Exception as e:
                    self.logger.error(f"Error in interface monitoring: {e}")
                    time.sleep(10)
        
        thread = threading.Thread(target=monitor_interfaces, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
    
    def _start_traffic_monitor(self):
        """Start traffic monitoring thread"""
        def monitor_traffic():
            while self.is_monitoring:
                try:
                    self._analyze_traffic()
                    time.sleep(self.monitor_interval * 3)
                except Exception as e:
                    self.logger.error(f"Error in traffic monitoring: {e}")
                    time.sleep(10)
        
        thread = threading.Thread(target=monitor_traffic, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
    
    def _start_threat_monitor(self):
        """Start threat monitoring thread"""
        def monitor_threats():
            while self.is_monitoring:
                try:
                    self._check_threats()
                    time.sleep(self.threat_check_interval)
                except Exception as e:
                    self.logger.error(f"Error in threat monitoring: {e}")
                    time.sleep(30)
        
        thread = threading.Thread(target=monitor_threats, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)
    
    def _scan_connections(self):
        """Scan active network connections"""
        try:
            connections = psutil.net_connections(kind='inet')
            current_connections = {}
            
            for conn in connections:
                if not conn.raddr:
                    continue
                
                # Create connection key
                conn_key = f"{conn.laddr.ip}:{conn.laddr.port}-{conn.raddr.ip}:{conn.raddr.port}"
                
                # Get process information
                process_name = None
                if conn.pid:
                    try:
                        process = psutil.Process(conn.pid)
                        process_name = process.name()
                    except:
                        pass
                
                # Create connection object
                network_conn = NetworkConnection(
                    local_ip=conn.laddr.ip,
                    local_port=conn.laddr.port,
                    remote_ip=conn.raddr.ip,
                    remote_port=conn.raddr.port,
                    protocol=conn.type.name.lower(),
                    status=conn.status,
                    pid=conn.pid,
                    process_name=process_name,
                    timestamp=datetime.now()
                )
                
                # Enhance with geographic information
                self._enhance_connection_info(network_conn)
                
                # Check for threats
                self._check_connection_threat(network_conn)
                
                current_connections[conn_key] = network_conn
                
                # Check if this is a new connection
                if conn_key not in self.active_connections:
                    self._handle_new_connection(network_conn)
            
            # Update active connections
            self.active_connections = current_connections
            
            # Clean up old history
            self._cleanup_history()
            
        except Exception as e:
            self.logger.error(f"Error scanning connections: {e}")
    
    def _scan_interfaces(self):
        """Scan network interfaces"""
        try:
            interfaces = psutil.net_if_addrs()
            stats = psutil.net_if_stats()
            io_counters = psutil.net_io_counters(pernic=True)
            
            for interface_name, addresses in interfaces.items():
                # Get IPv4 address
                ipv4_addr = None
                for addr in addresses:
                    if addr.family == socket.AF_INET:
                        ipv4_addr = addr
                        break
                
                if not ipv4_addr:
                    continue
                
                # Get interface stats
                interface_stats = stats.get(interface_name)
                interface_io = io_counters.get(interface_name)
                
                if interface_stats and interface_io:
                    network_interface = NetworkInterface(
                        name=interface_name,
                        ip_address=ipv4_addr.address,
                        netmask=ipv4_addr.netmask,
                        broadcast=ipv4_addr.broadcast or "",
                        mac_address=self._get_mac_address(addresses),
                        is_up=interface_stats.isup,
                        bytes_sent=interface_io.bytes_sent,
                        bytes_received=interface_io.bytes_recv,
                        packets_sent=interface_io.packets_sent,
                        packets_received=interface_io.packets_recv,
                        errors_in=interface_io.errin,
                        errors_out=interface_io.errout,
                        drops_in=interface_io.dropin,
                        drops_out=interface_io.dropout
                    )
                    
                    self.network_interfaces[interface_name] = network_interface
            
        except Exception as e:
            self.logger.error(f"Error scanning interfaces: {e}")
    
    def _analyze_traffic(self):
        """Analyze network traffic patterns"""
        try:
            # Get current network I/O
            io_counters = psutil.net_io_counters()
            
            current_time = datetime.now()
            
            # Calculate bandwidth usage
            if hasattr(self, '_last_io_counters') and hasattr(self, '_last_io_time'):
                time_delta = (current_time - self._last_io_time).total_seconds()
                
                if time_delta > 0:
                    bytes_sent_per_sec = (io_counters.bytes_sent - self._last_io_counters.bytes_sent) / time_delta
                    bytes_recv_per_sec = (io_counters.bytes_recv - self._last_io_counters.bytes_recv) / time_delta
                    
                    self.bandwidth_usage[current_time] = {
                        'upload': bytes_sent_per_sec,
                        'download': bytes_recv_per_sec,
                        'total': bytes_sent_per_sec + bytes_recv_per_sec
                    }
                    
                    # Analyze traffic patterns
                    self.traffic_analyzer.analyze_bandwidth(bytes_sent_per_sec, bytes_recv_per_sec)
            
            self._last_io_counters = io_counters
            self._last_io_time = current_time
            
            # Clean up old bandwidth data
            cutoff_time = current_time - timedelta(hours=1)
            self.bandwidth_usage = {
                k: v for k, v in self.bandwidth_usage.items() if k > cutoff_time
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing traffic: {e}")
    
    def _check_threats(self):
        """Check for network threats"""
        try:
            # Update threat intelligence
            self._update_threat_intelligence()
            
            # Check active connections for threats
            for conn in self.active_connections.values():
                if conn.remote_ip in self.malicious_ips:
                    self._handle_malicious_connection(conn)
                elif self._is_suspicious_connection(conn):
                    self._handle_suspicious_connection(conn)
            
        except Exception as e:
            self.logger.error(f"Error checking threats: {e}")
    
    def _enhance_connection_info(self, connection: NetworkConnection):
        """Enhance connection with additional information"""
        try:
            # Get geographic information
            geo_info = self.geo_locator.get_location(connection.remote_ip)
            if geo_info:
                connection.country = geo_info.get('country')
                connection.city = geo_info.get('city')
                connection.organization = geo_info.get('organization')
            
            # Track IP
            self.ip_tracker.track_ip(connection.remote_ip, {
                'timestamp': connection.timestamp,
                'local_port': connection.local_port,
                'remote_port': connection.remote_port,
                'protocol': connection.protocol,
                'process': connection.process_name
            })
            
        except Exception as e:
            self.logger.error(f"Error enhancing connection info: {e}")
    
    def _check_connection_threat(self, connection: NetworkConnection):
        """Check if connection is threatening"""
        try:
            threat_level = "low"
            is_malicious = False
            
            # Check against malicious IPs
            if connection.remote_ip in self.malicious_ips:
                threat_level = "high"
                is_malicious = True
            
            # Check for suspicious ports
            elif self._is_suspicious_port(connection.remote_port):
                threat_level = "medium"
            
            # Check for suspicious processes
            elif self._is_suspicious_process(connection.process_name):
                threat_level = "medium"
            
            # Check connection frequency
            elif self._is_high_frequency_connection(connection.remote_ip):
                threat_level = "medium"
            
            connection.threat_level = threat_level
            connection.is_malicious = is_malicious
            
        except Exception as e:
            self.logger.error(f"Error checking connection threat: {e}")
    
    def _handle_new_connection(self, connection: NetworkConnection):
        """Handle new network connection"""
        try:
            # Add to history
            self.connection_history.append(connection)
            
            # Log connection
            self.log_manager.log_info(
                1, 'network', f'New connection: {connection.remote_ip}:{connection.remote_port}',
                asdict(connection)
            )
            
            # Notify callback
            if self.callbacks.get('on_new_connection'):
                self.callbacks['on_new_connection'](connection)
            
            # Check for immediate threats
            if connection.is_malicious:
                self._handle_malicious_connection(connection)
            elif connection.threat_level in ['medium', 'high']:
                self._handle_suspicious_connection(connection)
            
        except Exception as e:
            self.logger.error(f"Error handling new connection: {e}")
    
    def _handle_malicious_connection(self, connection: NetworkConnection):
        """Handle malicious connection"""
        try:
            self.logger.warning(f"Malicious connection detected: {connection.remote_ip}")
            
            # Add to blocked connections
            self.blocked_connections.append(connection)
            
            # Notify callback
            if self.callbacks.get('on_malicious_connection'):
                self.callbacks['on_malicious_connection'](connection)
            
            # Log threat
            self.log_manager.log_warning(
                1, 'security', f'Malicious connection blocked: {connection.remote_ip}',
                asdict(connection)
            )
            
        except Exception as e:
            self.logger.error(f"Error handling malicious connection: {e}")
    
    def _handle_suspicious_connection(self, connection: NetworkConnection):
        """Handle suspicious connection"""
        try:
            self.logger.warning(f"Suspicious connection detected: {connection.remote_ip}")
            
            # Add to suspicious connections
            self.suspicious_connections.append(connection)
            
            # Notify callback
            if self.callbacks.get('on_suspicious_connection'):
                self.callbacks['on_suspicious_connection'](connection)
            
        except Exception as e:
            self.logger.error(f"Error handling suspicious connection: {e}")
    
    def _is_suspicious_port(self, port: int) -> bool:
        """Check if port is suspicious"""
        suspicious_ports = {
            # Common malware ports
            4444, 5555, 6666, 7777, 8888, 9999,
            # Backdoor ports
            12345, 54321, 31337,
            # Trojan ports
            1243, 1999, 2001, 6969, 6970,
            # P2P ports
            6881, 6882, 6883, 6884, 6885
        }
        
        return port in suspicious_ports
    
    def _is_suspicious_process(self, process_name: str) -> bool:
        """Check if process is suspicious"""
        if not process_name:
            return False
        
        suspicious_names = [
            'virus', 'trojan', 'malware', 'hack', 'crack',
            'keygen', 'patch', 'loader', 'inject', 'bot'
        ]
        
        return any(name in process_name.lower() for name in suspicious_names)
    
    def _is_high_frequency_connection(self, ip: str) -> bool:
        """Check for high frequency connections"""
        # Count connections to this IP in the last minute
        cutoff_time = datetime.now() - timedelta(minutes=1)
        count = sum(1 for conn in self.connection_history 
                   if conn.remote_ip == ip and conn.timestamp > cutoff_time)
        
        return count > 10  # More than 10 connections per minute
    
    def _get_mac_address(self, addresses) -> str:
        """Get MAC address from interface addresses"""
        for addr in addresses:
            if addr.family == psutil.AF_LINK:
                return addr.address
        return ""
    
    def _load_threat_intelligence(self):
        """Load threat intelligence data"""
        try:
            # Load from local file or API
            self._load_malicious_ips_from_file()
            
        except Exception as e:
            self.logger.error(f"Error loading threat intelligence: {e}")
    
    def _load_malicious_ips_from_file(self):
        """Load malicious IPs from local file"""
        try:
            # This would load from a threat intelligence file
            # For demo, adding some test IPs
            test_malicious_ips = {
                "0.0.0.0",  # Invalid IP
                "*************",  # Test IP
            }
            self.malicious_ips.update(test_malicious_ips)
            
        except Exception as e:
            self.logger.error(f"Error loading malicious IPs: {e}")
    
    def _update_threat_intelligence(self):
        """Update threat intelligence from external sources"""
        try:
            # This would update from threat intelligence feeds
            pass
            
        except Exception as e:
            self.logger.error(f"Error updating threat intelligence: {e}")
    
    def _cleanup_history(self):
        """Clean up old connection history"""
        if len(self.connection_history) > self.history_limit:
            self.connection_history = self.connection_history[-self.history_limit:]
    
    def _is_suspicious_connection(self, connection: NetworkConnection) -> bool:
        """Check if connection is suspicious"""
        return (
            self._is_suspicious_port(connection.remote_port) or
            self._is_suspicious_process(connection.process_name) or
            self._is_high_frequency_connection(connection.remote_ip)
        )
    
    def get_network_statistics(self) -> Dict[str, Any]:
        """Get network monitoring statistics"""
        try:
            active_count = len(self.active_connections)
            malicious_count = sum(1 for conn in self.active_connections.values() if conn.is_malicious)
            suspicious_count = sum(1 for conn in self.active_connections.values() 
                                 if conn.threat_level in ['medium', 'high'] and not conn.is_malicious)
            
            # Get bandwidth statistics
            current_bandwidth = list(self.bandwidth_usage.values())[-1] if self.bandwidth_usage else {}
            
            return {
                'is_monitoring': self.is_monitoring,
                'active_connections': active_count,
                'malicious_connections': malicious_count,
                'suspicious_connections': suspicious_count,
                'total_history': len(self.connection_history),
                'network_interfaces': len(self.network_interfaces),
                'current_upload': current_bandwidth.get('upload', 0),
                'current_download': current_bandwidth.get('download', 0),
                'blocked_connections': len(self.blocked_connections),
                'malicious_ips_known': len(self.malicious_ips)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting network statistics: {e}")
            return {}
    
    def get_active_connections(self) -> List[Dict[str, Any]]:
        """Get list of active connections"""
        return [asdict(conn) for conn in self.active_connections.values()]
    
    def get_connection_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Get connection history"""
        return [asdict(conn) for conn in self.connection_history[-limit:]]
    
    def get_network_interfaces(self) -> List[Dict[str, Any]]:
        """Get network interfaces information"""
        return [asdict(interface) for interface in self.network_interfaces.values()]
    
    def get_bandwidth_usage(self, hours: int = 1) -> Dict[str, Any]:
        """Get bandwidth usage for specified hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        filtered_data = {
            k: v for k, v in self.bandwidth_usage.items() if k > cutoff_time
        }
        
        return {
            'timestamps': [k.isoformat() for k in filtered_data.keys()],
            'upload': [v['upload'] for v in filtered_data.values()],
            'download': [v['download'] for v in filtered_data.values()],
            'total': [v['total'] for v in filtered_data.values()]
        }
    
    def block_ip(self, ip_address: str) -> bool:
        """Block IP address"""
        try:
            self.malicious_ips.add(ip_address)
            self.logger.info(f"IP address blocked: {ip_address}")
            return True
        except Exception as e:
            self.logger.error(f"Error blocking IP {ip_address}: {e}")
            return False
    
    def unblock_ip(self, ip_address: str) -> bool:
        """Unblock IP address"""
        try:
            self.malicious_ips.discard(ip_address)
            self.logger.info(f"IP address unblocked: {ip_address}")
            return True
        except Exception as e:
            self.logger.error(f"Error unblocking IP {ip_address}: {e}")
            return False
