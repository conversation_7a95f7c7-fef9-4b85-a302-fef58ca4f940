# Cyber Shield Pro Advanced - Requirements
# Advanced cybersecurity suite dependencies

# Core Dependencies
asyncio-mqtt==0.16.1
aiofiles==23.2.1
aiohttp==3.9.1
aiosqlite==0.19.0

# Database
sqlite3  # Built-in Python module
sqlalchemy==2.0.23

# Network and Security
scapy==2.5.0
psutil==5.9.6
cryptography==41.0.8
pycryptodome==3.19.0
requests==2.31.0
urllib3==2.1.0

# Web Framework and Dashboard
flask==3.0.0
flask-socketio==5.3.6
fastapi==0.104.1
uvicorn==0.24.0
websockets==12.0

# Data Analysis and Visualization
pandas==2.1.4
numpy==1.25.2
matplotlib==3.8.2
seaborn==0.13.0

# Report Generation
reportlab==4.0.7
weasyprint==60.2
jinja2==3.1.2

# Notifications
smtplib  # Built-in Python module
twilio==8.11.0
plyer==2.1.0

# Windows-specific (for Windows systems)
pywin32==306; sys_platform == "win32"
wmi==1.5.1; sys_platform == "win32"
win10toast==0.9; sys_platform == "win32"

# System Monitoring
watchdog==3.0.0
schedule==1.2.0

# Configuration and Logging
pyyaml==6.0.1
configparser  # Built-in Python module
logging  # Built-in Python module

# Utilities
python-dateutil==2.8.2
pytz==2023.3
click==8.1.7
rich==13.7.0
colorama==0.4.6

# Development and Testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0

# Optional: Machine Learning for Advanced Threat Detection
scikit-learn==1.3.2
tensorflow==2.15.0  # Optional, for advanced ML features

# Optional: Additional Security Libraries
yara-python==4.3.1  # For malware detection rules
python-nmap==0.7.1  # For network scanning
netaddr==0.9.0  # For IP address manipulation

# Optional: GUI Framework (if needed)
tkinter  # Built-in Python module
customtkinter==5.2.0

# Optional: Advanced Networking
dpkt==1.9.8  # For packet parsing
pypcap==1.2.3  # For packet capture (Linux/macOS)

# Optional: Cloud Integration
boto3==1.34.0  # For AWS integration
azure-storage-blob==12.19.0  # For Azure integration
google-cloud-storage==2.10.0  # For Google Cloud integration

# Optional: Advanced Analytics
elasticsearch==8.11.0  # For log analysis
redis==5.0.1  # For caching and queuing

# Optional: API Documentation
swagger-ui-bundle==0.0.9
flasgger==*******
