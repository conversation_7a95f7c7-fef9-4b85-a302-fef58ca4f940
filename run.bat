@echo off
title Cyber Shield Pro
echo ========================================
echo   Cyber Shield Pro v1.0.0
echo   Advanced Cybersecurity Protection
echo   Developed in Palestine 🇵🇸
echo ========================================
echo.

REM Try different Python commands
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with python command...
    python main.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with py command...
    py main.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo Starting with python3 command...
    python3 main.py
    goto :end
)

echo ERROR: Python is not installed or not in PATH
echo.
echo Please install Python 3.8 or higher from:
echo https://www.python.org/downloads/
echo.
echo Make sure to check "Add Python to PATH" during installation.
echo.

:end
pause
