#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Firewall System for Cyber Shield Pro
Complete firewall management with Windows Firewall integration
"""

import os
import subprocess
import threading
import time
import socket
import struct
import ctypes
import ipaddress
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import psutil
import requests
from ..database.log_manager import LogManager
from ..utils.logger import Logger

class FirewallAction(Enum):
    ALLOW = "allow"
    BLOCK = "block"
    ASK = "ask"

class FirewallDirection(Enum):
    INBOUND = "in"
    OUTBOUND = "out"
    BOTH = "both"

class Protocol(Enum):
    TCP = "tcp"
    UDP = "udp"
    ICMP = "icmp"
    ANY = "any"

@dataclass
class FirewallRule:
    name: str
    direction: FirewallDirection
    action: FirewallAction
    protocol: Protocol
    local_port: Optional[str] = None
    remote_port: Optional[str] = None
    local_ip: Optional[str] = None
    remote_ip: Optional[str] = None
    program: Optional[str] = None
    enabled: bool = True
    priority: int = 100
    description: str = ""

class AdvancedFirewall:
    """Advanced Firewall Management System"""
    
    def __init__(self, log_manager: LogManager, logger: Logger):
        """Initialize advanced firewall"""
        self.log_manager = log_manager
        self.logger = logger
        
        # Firewall state
        self.is_enabled = False
        self.is_monitoring = False
        self.protection_level = "high"
        
        # Rules management
        self.custom_rules = []
        self.blocked_ips = set()
        self.allowed_ips = set()
        self.blocked_domains = set()
        
        # Monitoring
        self.monitor_thread = None
        self.packet_monitor = None
        
        # Callbacks
        self.callbacks = {}
        
        # Admin privileges check
        self.has_admin_rights = self._check_admin_privileges()
        
        # Threat intelligence
        self.malicious_ips = set()
        self.malicious_domains = set()
        self._load_threat_intelligence()
        
        # Default rules
        self._create_default_rules()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def enable_firewall(self) -> bool:
        """Enable Windows Firewall"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to enable firewall")
                return False
            
            # Enable Windows Firewall for all profiles
            profiles = ['domain', 'private', 'public']
            
            for profile in profiles:
                cmd = ['netsh', 'advfirewall', 'set', f'{profile}profile', 'state', 'on']
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode != 0:
                    self.logger.error(f"Failed to enable firewall for {profile} profile: {result.stderr}")
                    return False
            
            self.is_enabled = True
            self.logger.info("Windows Firewall enabled for all profiles")
            
            # Start monitoring
            self.start_monitoring()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error enabling firewall: {e}")
            return False
    
    def disable_firewall(self) -> bool:
        """Disable Windows Firewall"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to disable firewall")
                return False
            
            # Stop monitoring first
            self.stop_monitoring()
            
            # Disable Windows Firewall for all profiles
            profiles = ['domain', 'private', 'public']
            
            for profile in profiles:
                cmd = ['netsh', 'advfirewall', 'set', f'{profile}profile', 'state', 'off']
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode != 0:
                    self.logger.error(f"Failed to disable firewall for {profile} profile: {result.stderr}")
                    return False
            
            self.is_enabled = False
            self.logger.info("Windows Firewall disabled for all profiles")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error disabling firewall: {e}")
            return False
    
    def add_rule(self, rule: FirewallRule) -> bool:
        """Add firewall rule"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to add firewall rules")
                return False
            
            # Build netsh command
            cmd = ['netsh', 'advfirewall', 'firewall', 'add', 'rule']
            cmd.extend(['name=' + rule.name])
            cmd.extend(['dir=' + rule.direction.value])
            cmd.extend(['action=' + rule.action.value])
            
            if rule.protocol != Protocol.ANY:
                cmd.extend(['protocol=' + rule.protocol.value])
            
            if rule.local_port:
                cmd.extend(['localport=' + rule.local_port])
            
            if rule.remote_port:
                cmd.extend(['remoteport=' + rule.remote_port])
            
            if rule.local_ip:
                cmd.extend(['localip=' + rule.local_ip])
            
            if rule.remote_ip:
                cmd.extend(['remoteip=' + rule.remote_ip])
            
            if rule.program:
                cmd.extend(['program=' + rule.program])
            
            if not rule.enabled:
                cmd.extend(['enable=no'])
            
            # Execute command
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.custom_rules.append(rule)
                self.logger.info(f"Firewall rule added: {rule.name}")
                
                # Log to database
                self.log_manager.log_info(
                    1, 'security', f'Firewall rule added: {rule.name}',
                    {'rule': rule.__dict__}
                )
                
                return True
            else:
                self.logger.error(f"Failed to add firewall rule: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error adding firewall rule: {e}")
            return False
    
    def remove_rule(self, rule_name: str) -> bool:
        """Remove firewall rule"""
        try:
            if not self.has_admin_rights:
                return False
            
            cmd = ['netsh', 'advfirewall', 'firewall', 'delete', 'rule', f'name={rule_name}']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # Remove from custom rules
                self.custom_rules = [r for r in self.custom_rules if r.name != rule_name]
                self.logger.info(f"Firewall rule removed: {rule_name}")
                return True
            else:
                self.logger.error(f"Failed to remove firewall rule: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error removing firewall rule: {e}")
            return False
    
    def block_ip(self, ip_address: str, rule_name: str = None) -> bool:
        """Block specific IP address"""
        try:
            # Validate IP address
            ipaddress.ip_address(ip_address)
            
            if not rule_name:
                rule_name = f"CyberShield_Block_{ip_address}"
            
            rule = FirewallRule(
                name=rule_name,
                direction=FirewallDirection.BOTH,
                action=FirewallAction.BLOCK,
                protocol=Protocol.ANY,
                remote_ip=ip_address,
                description=f"Block IP {ip_address}"
            )
            
            if self.add_rule(rule):
                self.blocked_ips.add(ip_address)
                self.logger.info(f"IP address blocked: {ip_address}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error blocking IP {ip_address}: {e}")
            return False
    
    def unblock_ip(self, ip_address: str) -> bool:
        """Unblock specific IP address"""
        try:
            rule_name = f"CyberShield_Block_{ip_address}"
            
            if self.remove_rule(rule_name):
                self.blocked_ips.discard(ip_address)
                self.logger.info(f"IP address unblocked: {ip_address}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error unblocking IP {ip_address}: {e}")
            return False
    
    def block_port(self, port: int, protocol: Protocol = Protocol.TCP, direction: FirewallDirection = FirewallDirection.INBOUND) -> bool:
        """Block specific port"""
        try:
            rule_name = f"CyberShield_Block_Port_{port}_{protocol.value}_{direction.value}"
            
            rule = FirewallRule(
                name=rule_name,
                direction=direction,
                action=FirewallAction.BLOCK,
                protocol=protocol,
                local_port=str(port) if direction == FirewallDirection.INBOUND else None,
                remote_port=str(port) if direction == FirewallDirection.OUTBOUND else None,
                description=f"Block port {port}/{protocol.value}"
            )
            
            if self.add_rule(rule):
                self.logger.info(f"Port blocked: {port}/{protocol.value}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error blocking port {port}: {e}")
            return False
    
    def allow_program(self, program_path: str, rule_name: str = None) -> bool:
        """Allow specific program through firewall"""
        try:
            if not os.path.exists(program_path):
                self.logger.error(f"Program not found: {program_path}")
                return False
            
            if not rule_name:
                program_name = os.path.basename(program_path)
                rule_name = f"CyberShield_Allow_{program_name}"
            
            rule = FirewallRule(
                name=rule_name,
                direction=FirewallDirection.BOTH,
                action=FirewallAction.ALLOW,
                protocol=Protocol.ANY,
                program=program_path,
                description=f"Allow program {os.path.basename(program_path)}"
            )
            
            if self.add_rule(rule):
                self.logger.info(f"Program allowed: {program_path}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error allowing program {program_path}: {e}")
            return False
    
    def block_domain(self, domain: str) -> bool:
        """Block domain by resolving to IP and blocking"""
        try:
            # Resolve domain to IP addresses
            ips = self._resolve_domain(domain)
            
            blocked_count = 0
            for ip in ips:
                rule_name = f"CyberShield_Block_Domain_{domain}_{ip}"
                if self.block_ip(ip, rule_name):
                    blocked_count += 1
            
            if blocked_count > 0:
                self.blocked_domains.add(domain)
                self.logger.info(f"Domain blocked: {domain} ({blocked_count} IPs)")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error blocking domain {domain}: {e}")
            return False
    
    def start_monitoring(self) -> bool:
        """Start firewall monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.monitor_thread = threading.Thread(target=self._monitor_connections, daemon=True)
            self.monitor_thread.start()
            
            self.is_monitoring = True
            self.logger.info("Firewall monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting firewall monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop firewall monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Firewall monitoring stopped")
    
    def _monitor_connections(self):
        """Monitor network connections"""
        while self.is_monitoring:
            try:
                connections = psutil.net_connections()
                
                for conn in connections:
                    if conn.raddr:
                        self._analyze_connection(conn)
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in connection monitoring: {e}")
                time.sleep(10)
    
    def _analyze_connection(self, connection):
        """Analyze network connection for threats"""
        try:
            remote_ip = connection.raddr.ip
            remote_port = connection.raddr.port
            local_port = connection.laddr.port if connection.laddr else None
            
            # Check against blocked IPs
            if remote_ip in self.blocked_ips:
                self._handle_blocked_connection(connection, "Blocked IP")
                return
            
            # Check against malicious IPs
            if remote_ip in self.malicious_ips:
                self._handle_malicious_connection(connection, "Malicious IP")
                return
            
            # Check for suspicious ports
            if self._is_suspicious_port(remote_port):
                self._handle_suspicious_connection(connection, f"Suspicious port {remote_port}")
                return
            
            # Check connection frequency
            if self._is_high_frequency_connection(remote_ip):
                self._handle_suspicious_connection(connection, "High frequency connections")
                return
            
        except Exception as e:
            self.logger.error(f"Error analyzing connection: {e}")
    
    def _handle_blocked_connection(self, connection, reason: str):
        """Handle blocked connection"""
        remote_ip = connection.raddr.ip
        self.logger.warning(f"Blocked connection detected: {remote_ip} - {reason}")
        
        if self.callbacks.get('on_blocked_connection'):
            self.callbacks['on_blocked_connection']({
                'connection': connection,
                'reason': reason
            })
    
    def _handle_malicious_connection(self, connection, reason: str):
        """Handle malicious connection"""
        remote_ip = connection.raddr.ip
        self.logger.warning(f"Malicious connection detected: {remote_ip} - {reason}")
        
        # Auto-block if not already blocked
        if remote_ip not in self.blocked_ips:
            self.block_ip(remote_ip, f"Auto_Block_Malicious_{remote_ip}")
        
        if self.callbacks.get('on_malicious_connection'):
            self.callbacks['on_malicious_connection']({
                'connection': connection,
                'reason': reason
            })
    
    def _handle_suspicious_connection(self, connection, reason: str):
        """Handle suspicious connection"""
        remote_ip = connection.raddr.ip
        self.logger.warning(f"Suspicious connection detected: {remote_ip} - {reason}")
        
        if self.callbacks.get('on_suspicious_connection'):
            self.callbacks['on_suspicious_connection']({
                'connection': connection,
                'reason': reason
            })
    
    def _is_suspicious_port(self, port: int) -> bool:
        """Check if port is suspicious"""
        suspicious_ports = {
            # Common malware ports
            4444, 5555, 6666, 7777, 8888, 9999,
            # Backdoor ports
            12345, 54321, 31337,
            # Trojan ports
            1243, 1999, 2001, 6969, 6970
        }
        
        return port in suspicious_ports
    
    def _is_high_frequency_connection(self, ip: str) -> bool:
        """Check for high frequency connections from same IP"""
        # This would implement connection frequency analysis
        return False
    
    def _resolve_domain(self, domain: str) -> List[str]:
        """Resolve domain to IP addresses"""
        try:
            import socket
            result = socket.getaddrinfo(domain, None)
            ips = list(set([addr[4][0] for addr in result]))
            return ips
        except Exception as e:
            self.logger.error(f"Error resolving domain {domain}: {e}")
            return []
    
    def _load_threat_intelligence(self):
        """Load threat intelligence data"""
        try:
            # Load malicious IPs from threat feeds
            self._load_malicious_ips()
            
            # Load malicious domains
            self._load_malicious_domains()
            
        except Exception as e:
            self.logger.error(f"Error loading threat intelligence: {e}")
    
    def _load_malicious_ips(self):
        """Load malicious IP addresses"""
        # This would load from threat intelligence feeds
        # For demo, adding some known malicious IPs
        known_malicious = {
            "0.0.0.0",  # Invalid
            "127.0.0.1",  # Localhost (for testing)
        }
        self.malicious_ips.update(known_malicious)
    
    def _load_malicious_domains(self):
        """Load malicious domains"""
        # This would load from threat intelligence feeds
        known_malicious = {
            "malware.com",
            "phishing.net",
            "trojan.org"
        }
        self.malicious_domains.update(known_malicious)
    
    def _create_default_rules(self):
        """Create default firewall rules"""
        default_rules = [
            FirewallRule(
                name="CyberShield_Block_Malware_Ports",
                direction=FirewallDirection.INBOUND,
                action=FirewallAction.BLOCK,
                protocol=Protocol.TCP,
                local_port="4444,5555,6666,7777,8888,9999",
                description="Block common malware ports"
            ),
            FirewallRule(
                name="CyberShield_Block_Backdoor_Ports",
                direction=FirewallDirection.BOTH,
                action=FirewallAction.BLOCK,
                protocol=Protocol.TCP,
                local_port="12345,54321,31337",
                description="Block backdoor ports"
            )
        ]
        
        # Add default rules if admin rights available
        if self.has_admin_rights:
            for rule in default_rules:
                self.add_rule(rule)
    
    def get_firewall_status(self) -> Dict[str, Any]:
        """Get firewall status"""
        try:
            # Get Windows Firewall status
            cmd = ['netsh', 'advfirewall', 'show', 'allprofiles', 'state']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            profiles_status = {}
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_profile = None
                
                for line in lines:
                    if 'Profile' in line:
                        current_profile = line.split()[0].lower()
                    elif 'State' in line and current_profile:
                        state = 'ON' in line.upper()
                        profiles_status[current_profile] = state
            
            return {
                'is_enabled': self.is_enabled,
                'is_monitoring': self.is_monitoring,
                'has_admin_rights': self.has_admin_rights,
                'protection_level': self.protection_level,
                'profiles_status': profiles_status,
                'custom_rules_count': len(self.custom_rules),
                'blocked_ips_count': len(self.blocked_ips),
                'blocked_domains_count': len(self.blocked_domains),
                'malicious_ips_count': len(self.malicious_ips)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting firewall status: {e}")
            return {'error': str(e)}
    
    def get_blocked_connections(self) -> List[Dict[str, Any]]:
        """Get list of blocked connections"""
        # This would return recent blocked connections
        return []
    
    def export_rules(self) -> str:
        """Export firewall rules to file"""
        try:
            cmd = ['netsh', 'advfirewall', 'export', 'firewall_backup.wfw']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                return "firewall_backup.wfw"
            else:
                self.logger.error(f"Failed to export rules: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error exporting rules: {e}")
            return None
    
    def import_rules(self, backup_file: str) -> bool:
        """Import firewall rules from file"""
        try:
            if not os.path.exists(backup_file):
                return False
            
            cmd = ['netsh', 'advfirewall', 'import', backup_file]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info("Firewall rules imported successfully")
                return True
            else:
                self.logger.error(f"Failed to import rules: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error importing rules: {e}")
            return False
