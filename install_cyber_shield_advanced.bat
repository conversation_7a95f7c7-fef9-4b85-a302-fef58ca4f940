@echo off
REM Cyber Shield Pro Advanced - Installation Script
REM This script sets up Cyber Shield Pro Advanced on Windows

title Cyber Shield Pro Advanced - Installer

echo.
echo ================================================================
echo                🛡️  Cyber Shield Pro Advanced 🛡️
echo                     Installation Script
echo ================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ❌ This installer requires Administrator privileges
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo ✅ Running with Administrator privileges

REM Check if Python is installed
echo.
echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed
    echo.
    echo 📥 Would you like to download Python? (y/n)
    set /p download_python=
    if /i "%download_python%"=="y" (
        echo Opening Python download page...
        start https://www.python.org/downloads/
        echo Please install Python 3.8 or higher and run this installer again
        pause
        exit /b 1
    ) else (
        echo Please install Python 3.8 or higher manually
        pause
        exit /b 1
    )
)

echo ✅ Python is installed
python --version

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set python_version=%%i
echo Python version: %python_version%

REM Upgrade pip
echo.
echo 📦 Upgrading pip...
python -m pip install --upgrade pip

REM Create virtual environment (optional but recommended)
echo.
echo 🔧 Setting up virtual environment...
if not exist "venv" (
    python -m venv venv
    echo ✅ Virtual environment created
) else (
    echo ✅ Virtual environment already exists
)

REM Activate virtual environment
echo.
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install dependencies
echo.
echo 📥 Installing dependencies...
echo This may take several minutes...
pip install -r requirements_advanced.txt
if errorlevel 1 (
    echo ❌ Failed to install some dependencies
    echo This might be due to network issues or missing system libraries
    echo.
    echo 🔧 Trying to install essential dependencies only...
    pip install aiohttp aiosqlite psutil flask fastapi uvicorn
    if errorlevel 1 (
        echo ❌ Failed to install essential dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo ✅ Dependencies installed successfully

REM Create necessary directories
echo.
echo 📁 Creating application directories...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp
if not exist "reports" mkdir reports
if not exist "quarantine" mkdir quarantine
if not exist "config" mkdir config
if not exist "certs" mkdir certs
if not exist "assets" mkdir assets
if not exist "assets\icons" mkdir assets\icons
if not exist "assets\images" mkdir assets\images

echo ✅ Directories created

REM Set up Windows Firewall exception
echo.
echo 🔥 Setting up Windows Firewall exception...
netsh advfirewall firewall add rule name="Cyber Shield Pro Advanced Dashboard" dir=in action=allow protocol=TCP localport=8080 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Could not add firewall exception automatically
    echo You may need to manually allow port 8080 in Windows Firewall
) else (
    echo ✅ Firewall exception added for dashboard (port 8080)
)

REM Create desktop shortcut
echo.
echo 🖥️  Creating desktop shortcut...
set desktop=%USERPROFILE%\Desktop
set shortcut_path=%desktop%\Cyber Shield Pro Advanced.lnk
set target_path=%CD%\run_cyber_shield_advanced.bat
set icon_path=%CD%\assets\icons\cybershield.ico

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%shortcut_path%" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%target_path%" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> CreateShortcut.vbs
echo oLink.Description = "Cyber Shield Pro Advanced - Cybersecurity Suite" >> CreateShortcut.vbs
if exist "%icon_path%" (
    echo oLink.IconLocation = "%icon_path%" >> CreateShortcut.vbs
)
echo oLink.Save >> CreateShortcut.vbs

cscript CreateShortcut.vbs >nul 2>&1
del CreateShortcut.vbs >nul 2>&1

if exist "%shortcut_path%" (
    echo ✅ Desktop shortcut created
) else (
    echo ⚠️  Could not create desktop shortcut
)

REM Create Start Menu shortcut
echo.
echo 📋 Creating Start Menu shortcut...
set startmenu=%APPDATA%\Microsoft\Windows\Start Menu\Programs
set startmenu_shortcut=%startmenu%\Cyber Shield Pro Advanced.lnk

echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateStartMenuShortcut.vbs
echo sLinkFile = "%startmenu_shortcut%" >> CreateStartMenuShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateStartMenuShortcut.vbs
echo oLink.TargetPath = "%target_path%" >> CreateStartMenuShortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> CreateStartMenuShortcut.vbs
echo oLink.Description = "Cyber Shield Pro Advanced - Cybersecurity Suite" >> CreateStartMenuShortcut.vbs
if exist "%icon_path%" (
    echo oLink.IconLocation = "%icon_path%" >> CreateStartMenuShortcut.vbs
)
echo oLink.Save >> CreateStartMenuShortcut.vbs

cscript CreateStartMenuShortcut.vbs >nul 2>&1
del CreateStartMenuShortcut.vbs >nul 2>&1

if exist "%startmenu_shortcut%" (
    echo ✅ Start Menu shortcut created
) else (
    echo ⚠️  Could not create Start Menu shortcut
)

REM Test installation
echo.
echo 🧪 Testing installation...
python -c "import aiohttp, aiosqlite, psutil; print('✅ Core modules imported successfully')" 2>nul
if errorlevel 1 (
    echo ⚠️  Some modules may not be properly installed
    echo The application might still work with limited functionality
) else (
    echo ✅ Installation test passed
)

REM Create uninstaller
echo.
echo 🗑️  Creating uninstaller...
echo @echo off > uninstall_cyber_shield_advanced.bat
echo title Cyber Shield Pro Advanced - Uninstaller >> uninstall_cyber_shield_advanced.bat
echo echo Uninstalling Cyber Shield Pro Advanced... >> uninstall_cyber_shield_advanced.bat
echo. >> uninstall_cyber_shield_advanced.bat
echo REM Remove firewall rule >> uninstall_cyber_shield_advanced.bat
echo netsh advfirewall firewall delete rule name="Cyber Shield Pro Advanced Dashboard" ^>nul 2^>^&1 >> uninstall_cyber_shield_advanced.bat
echo. >> uninstall_cyber_shield_advanced.bat
echo REM Remove shortcuts >> uninstall_cyber_shield_advanced.bat
echo del "%USERPROFILE%\Desktop\Cyber Shield Pro Advanced.lnk" ^>nul 2^>^&1 >> uninstall_cyber_shield_advanced.bat
echo del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Cyber Shield Pro Advanced.lnk" ^>nul 2^>^&1 >> uninstall_cyber_shield_advanced.bat
echo. >> uninstall_cyber_shield_advanced.bat
echo REM Remove virtual environment >> uninstall_cyber_shield_advanced.bat
echo if exist "venv" rmdir /s /q "venv" >> uninstall_cyber_shield_advanced.bat
echo. >> uninstall_cyber_shield_advanced.bat
echo echo ✅ Cyber Shield Pro Advanced uninstalled successfully >> uninstall_cyber_shield_advanced.bat
echo pause >> uninstall_cyber_shield_advanced.bat

echo ✅ Uninstaller created

echo.
echo ================================================================
echo                    🎉 INSTALLATION COMPLETE! 🎉
echo ================================================================
echo.
echo ✅ Cyber Shield Pro Advanced has been installed successfully!
echo.
echo 🚀 To start the application:
echo    • Double-click the desktop shortcut, or
echo    • Run "run_cyber_shield_advanced.bat", or
echo    • Use the Start Menu shortcut
echo.
echo 📊 Dashboard will be available at: http://localhost:8080
echo 👤 Default login credentials:
echo    Username: admin
echo    Password: CyberShield2025!
echo.
echo 📚 Documentation:
echo    • README_ADVANCED.md - Complete user guide
echo    • config/cybershield_advanced.yaml - Configuration file
echo.
echo 🔧 To uninstall: Run "uninstall_cyber_shield_advanced.bat"
echo.
echo ⚠️  IMPORTANT SECURITY NOTES:
echo    • Change default password after first login
echo    • Review configuration settings
echo    • Ensure Windows Defender exclusions if needed
echo    • Run as Administrator for full functionality
echo.
echo ================================================================
echo.

REM Ask if user wants to start the application now
set /p start_now=Would you like to start Cyber Shield Pro Advanced now? (y/n): 
if /i "%start_now%"=="y" (
    echo.
    echo 🚀 Starting Cyber Shield Pro Advanced...
    call run_cyber_shield_advanced.bat
) else (
    echo.
    echo 👋 Installation complete. You can start the application anytime!
)

pause
