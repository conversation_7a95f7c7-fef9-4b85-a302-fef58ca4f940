#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced System Monitor for Cyber Shield Pro
Comprehensive system monitoring with admin privileges and real-time alerts
"""

import os
import sys
import time
import threading
import ctypes
import subprocess
import winreg
import wmi
import psutil
import platform
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from .performance_monitor import PerformanceMonitor
from .service_manager import ServiceManager
from .registry_manager import RegistryManager
from .startup_manager import StartupManager
from ..database.log_manager import LogManager
from ..utils.logger import Logger

class SystemHealth(Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class SystemAlert:
    """System alert information"""
    timestamp: datetime
    level: AlertLevel
    category: str
    title: str
    description: str
    details: Dict[str, Any]
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class SystemInfo:
    """Comprehensive system information"""
    hostname: str
    os_name: str
    os_version: str
    os_build: str
    architecture: str
    processor: str
    total_memory: int
    available_memory: int
    disk_usage: Dict[str, Dict[str, Any]]
    network_interfaces: List[Dict[str, Any]]
    uptime: timedelta
    boot_time: datetime
    logged_users: List[Dict[str, Any]]
    system_health: SystemHealth
    last_updated: datetime

class SystemMonitor:
    """Advanced System Monitoring and Management"""

    def __init__(self, log_manager: LogManager, logger: Logger):
        """Initialize system monitor"""
        self.log_manager = log_manager
        self.logger = logger

        # Sub-components
        self.performance_monitor = PerformanceMonitor(logger)
        self.service_manager = ServiceManager(logger)
        self.registry_manager = RegistryManager(logger)
        self.startup_manager = StartupManager(logger)

        # Monitoring state
        self.is_monitoring = False
        self.monitor_threads = []

        # System information
        self.system_info = None
        self.system_alerts = []

        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()

        # WMI connection for advanced Windows monitoring
        try:
            self.wmi_conn = wmi.WMI()
        except Exception as e:
            self.logger.warning(f"Failed to initialize WMI: {e}")
            self.wmi_conn = None

        # Monitoring thresholds
        self.thresholds = {
            'cpu_warning': 80.0,
            'cpu_critical': 95.0,
            'memory_warning': 80.0,
            'memory_critical': 95.0,
            'disk_warning': 85.0,
            'disk_critical': 95.0,
            'temperature_warning': 70.0,
            'temperature_critical': 85.0
        }

        # Callbacks
        self.callbacks = {}

        # Initialize system information
        self._initialize_system_info()

    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False

    def start_monitoring(self, callbacks: Dict[str, Callable] = None) -> bool:
        """Start comprehensive system monitoring"""
        try:
            if self.is_monitoring:
                return True

            self.callbacks = callbacks or {}

            # Start sub-component monitoring
            self.performance_monitor.start_monitoring()
            self.service_manager.start_monitoring()

            # Start main monitoring threads
            self._start_system_health_monitor()
            self._start_security_monitor()
            self._start_integrity_monitor()
            self._start_event_monitor()

            self.is_monitoring = True
            self.logger.info("System monitoring started")

            if self.callbacks.get('on_monitoring_started'):
                self.callbacks['on_monitoring_started']()

            return True

        except Exception as e:
            self.logger.error(f"Failed to start system monitoring: {e}")
            return False

    def stop_monitoring(self) -> bool:
        """Stop system monitoring"""
        try:
            self.is_monitoring = False

            # Stop sub-components
            self.performance_monitor.stop_monitoring()
            self.service_manager.stop_monitoring()

            # Wait for threads to finish
            for thread in self.monitor_threads:
                if thread.is_alive():
                    thread.join(timeout=5)

            self.monitor_threads.clear()
            self.logger.info("System monitoring stopped")

            if self.callbacks.get('on_monitoring_stopped'):
                self.callbacks['on_monitoring_stopped']()

            return True

        except Exception as e:
            self.logger.error(f"Failed to stop system monitoring: {e}")
            return False

    def _start_system_health_monitor(self):
        """Start system health monitoring thread"""
        def monitor_health():
            while self.is_monitoring:
                try:
                    self._check_system_health()
                    time.sleep(30)  # Check every 30 seconds
                except Exception as e:
                    self.logger.error(f"Error in system health monitoring: {e}")
                    time.sleep(60)

        thread = threading.Thread(target=monitor_health, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)

    def _start_security_monitor(self):
        """Start security monitoring thread"""
        def monitor_security():
            while self.is_monitoring:
                try:
                    self._check_security_status()
                    time.sleep(60)  # Check every minute
                except Exception as e:
                    self.logger.error(f"Error in security monitoring: {e}")
                    time.sleep(120)

        thread = threading.Thread(target=monitor_security, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)

    def _start_integrity_monitor(self):
        """Start system integrity monitoring thread"""
        def monitor_integrity():
            while self.is_monitoring:
                try:
                    self._check_system_integrity()
                    time.sleep(300)  # Check every 5 minutes
                except Exception as e:
                    self.logger.error(f"Error in integrity monitoring: {e}")
                    time.sleep(600)

        thread = threading.Thread(target=monitor_integrity, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)

    def _start_event_monitor(self):
        """Start Windows event log monitoring thread"""
        def monitor_events():
            while self.is_monitoring:
                try:
                    self._monitor_event_logs()
                    time.sleep(120)  # Check every 2 minutes
                except Exception as e:
                    self.logger.error(f"Error in event monitoring: {e}")
                    time.sleep(240)

        thread = threading.Thread(target=monitor_events, daemon=True)
        thread.start()
        self.monitor_threads.append(thread)

    def _initialize_system_info(self):
        """Initialize comprehensive system information"""
        try:
            # Get basic system info
            uname = platform.uname()
            boot_time = datetime.fromtimestamp(psutil.boot_time())

            # Get memory info
            memory = psutil.virtual_memory()

            # Get disk usage
            disk_usage = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_usage[partition.device] = {
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100,
                        'filesystem': partition.fstype
                    }
                except PermissionError:
                    continue

            # Get network interfaces
            network_interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = {'name': interface, 'addresses': []}
                for addr in addrs:
                    interface_info['addresses'].append({
                        'family': addr.family.name,
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
                network_interfaces.append(interface_info)

            # Get logged users
            logged_users = []
            for user in psutil.users():
                logged_users.append({
                    'name': user.name,
                    'terminal': user.terminal,
                    'host': user.host,
                    'started': datetime.fromtimestamp(user.started)
                })

            self.system_info = SystemInfo(
                hostname=uname.node,
                os_name=uname.system,
                os_version=uname.version,
                os_build=uname.release,
                architecture=uname.machine,
                processor=uname.processor or platform.processor(),
                total_memory=memory.total,
                available_memory=memory.available,
                disk_usage=disk_usage,
                network_interfaces=network_interfaces,
                uptime=datetime.now() - boot_time,
                boot_time=boot_time,
                logged_users=logged_users,
                system_health=SystemHealth.GOOD,
                last_updated=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"Error initializing system info: {e}")

    def _check_system_health(self):
        """Check overall system health"""
        try:
            health_score = 100
            issues = []

            # Check CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > self.thresholds['cpu_critical']:
                health_score -= 30
                issues.append(f"Critical CPU usage: {cpu_percent:.1f}%")
                self._create_alert(AlertLevel.CRITICAL, "System Performance",
                                 "Critical CPU Usage", f"CPU usage is at {cpu_percent:.1f}%")
            elif cpu_percent > self.thresholds['cpu_warning']:
                health_score -= 15
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
                self._create_alert(AlertLevel.WARNING, "System Performance",
                                 "High CPU Usage", f"CPU usage is at {cpu_percent:.1f}%")

            # Check memory usage
            memory = psutil.virtual_memory()
            if memory.percent > self.thresholds['memory_critical']:
                health_score -= 25
                issues.append(f"Critical memory usage: {memory.percent:.1f}%")
                self._create_alert(AlertLevel.CRITICAL, "System Performance",
                                 "Critical Memory Usage", f"Memory usage is at {memory.percent:.1f}%")
            elif memory.percent > self.thresholds['memory_warning']:
                health_score -= 10
                issues.append(f"High memory usage: {memory.percent:.1f}%")
                self._create_alert(AlertLevel.WARNING, "System Performance",
                                 "High Memory Usage", f"Memory usage is at {memory.percent:.1f}%")

            # Check disk usage
            for device, usage in self.system_info.disk_usage.items():
                if usage['percent'] > self.thresholds['disk_critical']:
                    health_score -= 20
                    issues.append(f"Critical disk usage on {device}: {usage['percent']:.1f}%")
                    self._create_alert(AlertLevel.CRITICAL, "Storage",
                                     "Critical Disk Usage", f"Disk {device} is {usage['percent']:.1f}% full")
                elif usage['percent'] > self.thresholds['disk_warning']:
                    health_score -= 10
                    issues.append(f"High disk usage on {device}: {usage['percent']:.1f}%")
                    self._create_alert(AlertLevel.WARNING, "Storage",
                                     "High Disk Usage", f"Disk {device} is {usage['percent']:.1f}% full")

            # Check system temperature (if available)
            try:
                temps = psutil.sensors_temperatures()
                if temps:
                    for name, entries in temps.items():
                        for entry in entries:
                            if entry.current > self.thresholds['temperature_critical']:
                                health_score -= 25
                                issues.append(f"Critical temperature: {entry.current}°C")
                                self._create_alert(AlertLevel.CRITICAL, "Hardware",
                                                 "Critical Temperature", f"{name} temperature is {entry.current}°C")
                            elif entry.current > self.thresholds['temperature_warning']:
                                health_score -= 10
                                issues.append(f"High temperature: {entry.current}°C")
                                self._create_alert(AlertLevel.WARNING, "Hardware",
                                                 "High Temperature", f"{name} temperature is {entry.current}°C")
            except:
                pass  # Temperature monitoring not available

            # Determine health status
            if health_score >= 90:
                health_status = SystemHealth.EXCELLENT
            elif health_score >= 75:
                health_status = SystemHealth.GOOD
            elif health_score >= 50:
                health_status = SystemHealth.WARNING
            elif health_score >= 25:
                health_status = SystemHealth.CRITICAL
            else:
                health_status = SystemHealth.EMERGENCY

            # Update system info
            if self.system_info:
                self.system_info.system_health = health_status
                self.system_info.last_updated = datetime.now()

            # Notify callbacks
            if self.callbacks.get('on_health_update'):
                self.callbacks['on_health_update'](health_status, health_score, issues)

        except Exception as e:
            self.logger.error(f"Error checking system health: {e}")

    def _check_security_status(self):
        """Check system security status"""
        try:
            security_issues = []

            # Check Windows Defender status
            defender_status = self._check_windows_defender()
            if not defender_status['enabled']:
                security_issues.append("Windows Defender is disabled")
                self._create_alert(AlertLevel.WARNING, "Security",
                                 "Antivirus Disabled", "Windows Defender is not active")

            # Check Windows Firewall status
            firewall_status = self._check_windows_firewall()
            if not firewall_status['enabled']:
                security_issues.append("Windows Firewall is disabled")
                self._create_alert(AlertLevel.WARNING, "Security",
                                 "Firewall Disabled", "Windows Firewall is not active")

            # Check for suspicious services
            suspicious_services = self.service_manager.get_suspicious_services()
            if suspicious_services:
                security_issues.append(f"Found {len(suspicious_services)} suspicious services")
                self._create_alert(AlertLevel.WARNING, "Security",
                                 "Suspicious Services", f"Detected {len(suspicious_services)} suspicious services")

            # Check startup programs
            suspicious_startup = self.startup_manager.get_suspicious_startup_programs()
            if suspicious_startup:
                security_issues.append(f"Found {len(suspicious_startup)} suspicious startup programs")
                self._create_alert(AlertLevel.WARNING, "Security",
                                 "Suspicious Startup Programs", f"Detected {len(suspicious_startup)} suspicious startup programs")

            # Check for unsigned executables in system directories
            unsigned_files = self._check_unsigned_system_files()
            if unsigned_files:
                security_issues.append(f"Found {len(unsigned_files)} unsigned system files")
                self._create_alert(AlertLevel.ERROR, "Security",
                                 "Unsigned System Files", f"Found {len(unsigned_files)} unsigned files in system directories")

            # Log security status
            if security_issues:
                self.log_manager.log_warning(1, 'security', 'Security issues detected',
                                           {'issues': security_issues})

        except Exception as e:
            self.logger.error(f"Error checking security status: {e}")

    def _check_system_integrity(self):
        """Check system file integrity"""
        try:
            if not self.has_admin_rights:
                return

            # Run System File Checker (SFC)
            try:
                result = subprocess.run(['sfc', '/verifyonly'],
                                      capture_output=True, text=True, timeout=300)

                if "found integrity violations" in result.stdout.lower():
                    self._create_alert(AlertLevel.ERROR, "System Integrity",
                                     "System File Corruption", "SFC detected corrupted system files")

                    # Optionally run repair
                    if self.callbacks.get('auto_repair_system_files'):
                        subprocess.run(['sfc', '/scannow'], timeout=1800)

            except subprocess.TimeoutExpired:
                self.logger.warning("SFC scan timed out")
            except Exception as e:
                self.logger.error(f"Error running SFC: {e}")

            # Check critical system files
            critical_files = [
                'C:\\Windows\\System32\\ntoskrnl.exe',
                'C:\\Windows\\System32\\kernel32.dll',
                'C:\\Windows\\System32\\user32.dll',
                'C:\\Windows\\System32\\ntdll.dll'
            ]

            for file_path in critical_files:
                if not os.path.exists(file_path):
                    self._create_alert(AlertLevel.CRITICAL, "System Integrity",
                                     "Missing Critical File", f"Critical system file missing: {file_path}")

        except Exception as e:
            self.logger.error(f"Error checking system integrity: {e}")

    def _monitor_event_logs(self):
        """Monitor Windows event logs for security events"""
        try:
            if not self.wmi_conn:
                return

            # Query recent security events
            query = """
            SELECT * FROM Win32_NTLogEvent
            WHERE LogFile = 'Security' AND TimeGenerated > '%s'
            """ % (datetime.now() - timedelta(minutes=5)).strftime('%Y%m%d%H%M%S.000000+000')

            events = self.wmi_conn.query(query)

            for event in events:
                event_id = event.EventCode

                # Check for critical security events
                if event_id in [4625, 4648, 4649]:  # Failed logon attempts
                    self._create_alert(AlertLevel.WARNING, "Security",
                                     "Failed Logon Attempt", f"Failed logon detected (Event ID: {event_id})")
                elif event_id in [4720, 4722, 4724]:  # Account management
                    self._create_alert(AlertLevel.INFO, "Security",
                                     "Account Management", f"Account activity detected (Event ID: {event_id})")
                elif event_id in [4688]:  # Process creation
                    # Could analyze for suspicious process creation
                    pass

        except Exception as e:
            self.logger.error(f"Error monitoring event logs: {e}")

    def _check_windows_defender(self) -> Dict[str, Any]:
        """Check Windows Defender status"""
        try:
            if not self.wmi_conn:
                return {'enabled': False, 'error': 'WMI not available'}

            # Query Windows Defender status
            query = "SELECT * FROM MSFT_MpComputerStatus"
            try:
                defender_status = self.wmi_conn.query(query, namespace="root/Microsoft/Windows/Defender")
                if defender_status:
                    status = defender_status[0]
                    return {
                        'enabled': status.AntivirusEnabled,
                        'real_time_protection': status.RealTimeProtectionEnabled,
                        'definitions_updated': status.AntivirusSignatureLastUpdated
                    }
            except:
                pass

            return {'enabled': False, 'error': 'Could not query Defender status'}

        except Exception as e:
            self.logger.error(f"Error checking Windows Defender: {e}")
            return {'enabled': False, 'error': str(e)}

    def _check_windows_firewall(self) -> Dict[str, Any]:
        """Check Windows Firewall status"""
        try:
            result = subprocess.run(['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                output = result.stdout
                profiles = {}

                lines = output.split('\n')
                current_profile = None

                for line in lines:
                    if 'Profile' in line:
                        current_profile = line.split()[0].lower()
                    elif 'State' in line and current_profile:
                        state = 'ON' in line.upper()
                        profiles[current_profile] = state

                return {
                    'enabled': any(profiles.values()),
                    'profiles': profiles
                }

            return {'enabled': False, 'error': 'Could not query firewall status'}

        except Exception as e:
            self.logger.error(f"Error checking Windows Firewall: {e}")
            return {'enabled': False, 'error': str(e)}

    def _check_unsigned_system_files(self) -> List[str]:
        """Check for unsigned files in system directories"""
        try:
            unsigned_files = []
            system_dirs = [
                'C:\\Windows\\System32',
                'C:\\Windows\\SysWOW64'
            ]

            for sys_dir in system_dirs:
                if os.path.exists(sys_dir):
                    for root, dirs, files in os.walk(sys_dir):
                        for file in files[:10]:  # Limit check to avoid performance issues
                            if file.endswith('.exe') or file.endswith('.dll'):
                                file_path = os.path.join(root, file)
                                if not self._is_file_signed(file_path):
                                    unsigned_files.append(file_path)

                        if len(unsigned_files) > 50:  # Limit results
                            break

            return unsigned_files

        except Exception as e:
            self.logger.error(f"Error checking unsigned files: {e}")
            return []

    def _is_file_signed(self, file_path: str) -> bool:
        """Check if file has valid digital signature"""
        try:
            # This would use Windows API to check digital signature
            # For now, return True to avoid false positives
            return True
        except Exception as e:
            self.logger.error(f"Error checking file signature: {e}")
            return True

    def _create_alert(self, level: AlertLevel, category: str, title: str, description: str, details: Dict[str, Any] = None):
        """Create system alert"""
        try:
            alert = SystemAlert(
                timestamp=datetime.now(),
                level=level,
                category=category,
                title=title,
                description=description,
                details=details or {}
            )

            self.system_alerts.append(alert)

            # Keep only last 100 alerts
            if len(self.system_alerts) > 100:
                self.system_alerts = self.system_alerts[-100:]

            # Log alert
            self.log_manager.log_warning(1, 'system', f'{category}: {title}',
                                       {'description': description, 'details': details})

            # Notify callbacks
            if self.callbacks.get('on_alert_created'):
                self.callbacks['on_alert_created'](alert)

            self.logger.warning(f"System alert: {category} - {title}: {description}")

        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")

    def get_system_info(self) -> Optional[Dict[str, Any]]:
        """Get current system information"""
        if self.system_info:
            return asdict(self.system_info)
        return None

    def get_system_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent system alerts"""
        recent_alerts = self.system_alerts[-limit:]
        return [asdict(alert) for alert in recent_alerts]

    def get_performance_data(self) -> Dict[str, Any]:
        """Get performance monitoring data"""
        return self.performance_monitor.get_performance_data()

    def get_service_status(self) -> Dict[str, Any]:
        """Get service status information"""
        return self.service_manager.get_service_status()

    def resolve_alert(self, alert_index: int) -> bool:
        """Mark alert as resolved"""
        try:
            if 0 <= alert_index < len(self.system_alerts):
                self.system_alerts[alert_index].resolved = True
                self.system_alerts[alert_index].resolution_time = datetime.now()
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False

    def set_threshold(self, threshold_name: str, value: float) -> bool:
        """Set monitoring threshold"""
        try:
            if threshold_name in self.thresholds:
                self.thresholds[threshold_name] = value
                self.logger.info(f"Threshold updated: {threshold_name} = {value}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error setting threshold: {e}")
            return False

    def get_system_statistics(self) -> Dict[str, Any]:
        """Get comprehensive system statistics"""
        try:
            stats = {
                'monitoring_active': self.is_monitoring,
                'admin_privileges': self.has_admin_rights,
                'total_alerts': len(self.system_alerts),
                'unresolved_alerts': len([a for a in self.system_alerts if not a.resolved]),
                'system_health': self.system_info.system_health.value if self.system_info else 'unknown',
                'uptime_hours': self.system_info.uptime.total_seconds() / 3600 if self.system_info else 0,
                'last_updated': self.system_info.last_updated.isoformat() if self.system_info else None
            }

            # Add performance stats
            perf_data = self.performance_monitor.get_current_stats()
            if perf_data:
                stats.update(perf_data)

            return stats

        except Exception as e:
            self.logger.error(f"Error getting system statistics: {e}")
            return {}