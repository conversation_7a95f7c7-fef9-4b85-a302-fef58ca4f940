#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Two-Factor Authentication for Cyber Shield Pro
Handles TOTP (Time-based One-Time Password) authentication
"""

import secrets
import base64
import hmac
import hashlib
import struct
import time
import qrcode
import io
from typing import Optional

class TwoFactorAuth:
    """Two-factor authentication manager"""
    
    def __init__(self):
        """Initialize 2FA manager"""
        self.issuer = "Cyber Shield Pro"
        self.digits = 6
        self.period = 30  # 30 seconds
    
    def generate_secret(self) -> str:
        """Generate a new secret key for 2FA"""
        return base64.b32encode(secrets.token_bytes(20)).decode('utf-8')
    
    def generate_qr_url(self, secret: str, username: str) -> str:
        """Generate QR code URL for 2FA setup"""
        return f"otpauth://totp/{self.issuer}:{username}?secret={secret}&issuer={self.issuer}&digits={self.digits}&period={self.period}"
    
    def generate_qr_code(self, secret: str, username: str) -> bytes:
        """Generate QR code image for 2FA setup"""
        try:
            url = self.generate_qr_url(secret, username)
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(url)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to bytes
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            return img_buffer.getvalue()
            
        except Exception as e:
            print(f"Error generating QR code: {e}")
            return b""
    
    def generate_token(self, secret: str, timestamp: Optional[int] = None) -> str:
        """Generate TOTP token"""
        try:
            if timestamp is None:
                timestamp = int(time.time())
            
            # Calculate time counter
            counter = timestamp // self.period
            
            # Decode secret
            key = base64.b32decode(secret.upper() + '=' * (8 - len(secret) % 8))
            
            # Generate HMAC
            counter_bytes = struct.pack('>Q', counter)
            hmac_digest = hmac.new(key, counter_bytes, hashlib.sha1).digest()
            
            # Dynamic truncation
            offset = hmac_digest[-1] & 0x0f
            truncated = struct.unpack('>I', hmac_digest[offset:offset + 4])[0]
            truncated &= 0x7fffffff
            
            # Generate token
            token = truncated % (10 ** self.digits)
            return f"{token:0{self.digits}d}"
            
        except Exception as e:
            print(f"Error generating token: {e}")
            return ""
    
    def verify_token(self, secret: str, token: str, window: int = 1) -> bool:
        """Verify TOTP token with time window tolerance"""
        try:
            if not secret or not token:
                return False
            
            current_time = int(time.time())
            
            # Check current time and adjacent time windows
            for i in range(-window, window + 1):
                timestamp = current_time + (i * self.period)
                expected_token = self.generate_token(secret, timestamp)
                
                if self._constant_time_compare(token, expected_token):
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error verifying token: {e}")
            return False
    
    def get_backup_codes(self, count: int = 10) -> list:
        """Generate backup codes for 2FA"""
        backup_codes = []
        for _ in range(count):
            code = secrets.token_hex(4).upper()
            # Format as XXXX-XXXX
            formatted_code = f"{code[:4]}-{code[4:]}"
            backup_codes.append(formatted_code)
        
        return backup_codes
    
    def verify_backup_code(self, stored_codes: list, provided_code: str) -> bool:
        """Verify backup code and remove it from the list"""
        try:
            # Normalize the provided code
            normalized_code = provided_code.upper().replace('-', '').replace(' ', '')
            
            for i, stored_code in enumerate(stored_codes):
                normalized_stored = stored_code.upper().replace('-', '').replace(' ', '')
                
                if self._constant_time_compare(normalized_code, normalized_stored):
                    # Remove used backup code
                    stored_codes.pop(i)
                    return True
            
            return False
            
        except Exception as e:
            print(f"Error verifying backup code: {e}")
            return False
    
    def _constant_time_compare(self, a: str, b: str) -> bool:
        """Constant time string comparison to prevent timing attacks"""
        if len(a) != len(b):
            return False
        
        result = 0
        for x, y in zip(a, b):
            result |= ord(x) ^ ord(y)
        
        return result == 0
    
    def get_time_remaining(self) -> int:
        """Get remaining time for current TOTP period"""
        return self.period - (int(time.time()) % self.period)
    
    def is_valid_secret(self, secret: str) -> bool:
        """Validate if secret is properly formatted"""
        try:
            # Check if it's valid base32
            base64.b32decode(secret.upper() + '=' * (8 - len(secret) % 8))
            return True
        except:
            return False
