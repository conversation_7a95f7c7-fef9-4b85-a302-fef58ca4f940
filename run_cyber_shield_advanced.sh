#!/bin/bash

# Cyber Shield Pro Advanced - Quick Start Script
# This script helps you run Cyber Shield Pro Advanced with proper setup

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_header() {
    echo
    echo "================================================================"
    echo -e "                ${PURPLE}🛡️  Cyber Shield Pro Advanced 🛡️${NC}"
    echo -e "                ${CYAN}Advanced Cybersecurity Protection Suite${NC}"
    echo "================================================================"
    echo
}

# Check if running as root (for Linux)
check_privileges() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ "$EUID" -ne 0 ]; then
            print_warning "Not running as root"
            print_warning "For full functionality, please run with sudo"
            echo
            read -p "Do you want to continue anyway? (y/n): " choice
            if [[ ! "$choice" =~ ^[Yy]$ ]]; then
                echo "Exiting..."
                exit 1
            fi
        else
            print_status "Running with root privileges"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_info "Running on macOS"
        print_warning "Some features may require administrator privileges"
    fi
}

# Check if Python is installed
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        print_status "Python 3 is installed"
        $PYTHON_CMD --version
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        python_version=$(python --version 2>&1)
        if [[ $python_version == *"Python 3"* ]]; then
            print_status "Python 3 is installed"
            echo $python_version
        else
            print_error "Python 3 is required but Python 2 is installed"
            print_info "Please install Python 3.8 or higher"
            exit 1
        fi
    else
        print_error "Python is not installed"
        print_info "Please install Python 3.8 or higher"
        exit 1
    fi
}

# Check if pip is installed
check_pip() {
    if command -v pip3 &> /dev/null; then
        PIP_CMD="pip3"
    elif command -v pip &> /dev/null; then
        PIP_CMD="pip"
    else
        print_error "pip is not installed"
        print_info "Please install pip for Python package management"
        exit 1
    fi
    print_status "pip is available"
}

# Install dependencies
install_dependencies() {
    print_info "Checking dependencies..."
    
    # Check if aiohttp is installed (as a test for dependencies)
    if $PIP_CMD show aiohttp &> /dev/null; then
        print_status "Dependencies are already installed"
    else
        echo
        print_info "Installing required dependencies..."
        print_warning "This may take a few minutes..."
        
        if $PIP_CMD install -r requirements_advanced.txt; then
            print_status "Dependencies installed successfully"
        else
            print_error "Failed to install dependencies"
            print_info "Please check your internet connection and try again"
            exit 1
        fi
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating necessary directories..."
    
    directories=("logs" "data" "temp" "reports" "quarantine" "config")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
        fi
    done
    
    print_status "Directories created"
}

# Check configuration
check_configuration() {
    print_info "Checking configuration..."
    
    if [ ! -f "config/cybershield_advanced.yaml" ]; then
        print_warning "Configuration file not found"
        print_info "Using default configuration..."
    else
        print_status "Configuration file found"
    fi
}

# Install system dependencies for Linux
install_system_deps_linux() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        print_info "Checking system dependencies for Linux..."
        
        # Check if libpcap is installed (required for packet capture)
        if ! ldconfig -p | grep -q libpcap; then
            print_warning "libpcap not found, attempting to install..."
            
            # Detect package manager and install
            if command -v apt-get &> /dev/null; then
                sudo apt-get update && sudo apt-get install -y libpcap-dev python3-dev
            elif command -v yum &> /dev/null; then
                sudo yum install -y libpcap-devel python3-devel
            elif command -v dnf &> /dev/null; then
                sudo dnf install -y libpcap-devel python3-devel
            elif command -v pacman &> /dev/null; then
                sudo pacman -S libpcap python
            else
                print_warning "Could not detect package manager"
                print_info "Please install libpcap-dev manually"
            fi
        fi
        
        # Check for notification dependencies
        if ! command -v notify-send &> /dev/null; then
            print_warning "notify-send not found (desktop notifications may not work)"
            print_info "Install libnotify-bin for desktop notifications"
        fi
    fi
}

# Main execution
main() {
    print_header
    
    check_privileges
    check_python
    check_pip
    install_system_deps_linux
    install_dependencies
    create_directories
    check_configuration
    
    echo
    print_info "Starting Cyber Shield Pro Advanced..."
    echo
    echo "================================================================"
    echo -e "                        ${YELLOW}IMPORTANT NOTES${NC}"
    echo "================================================================"
    echo
    echo -e "${BLUE}📊${NC} Dashboard will be available at: http://localhost:8080"
    echo -e "${BLUE}👤${NC} Default login credentials:"
    echo "    Username: admin"
    echo "    Password: CyberShield2025!"
    echo
    echo -e "${YELLOW}🛑${NC} To stop the application, press Ctrl+C"
    echo
    echo -e "${YELLOW}⚠️${NC}  First run may take longer as the system initializes"
    echo "================================================================"
    echo
    
    # Start the application
    if [ -f "config/cybershield_advanced.yaml" ]; then
        $PYTHON_CMD cyber_shield_advanced.py --config config/cybershield_advanced.yaml
    else
        $PYTHON_CMD cyber_shield_advanced.py
    fi
    
    echo
    print_info "Cyber Shield Pro Advanced has stopped"
    echo
}

# Make sure we're in the right directory
cd "$(dirname "$0")"

# Run main function
main "$@"
