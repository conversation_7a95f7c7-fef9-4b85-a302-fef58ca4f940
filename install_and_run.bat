@echo off
echo ========================================
echo Cyber Shield Pro - Installation Script
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python is already installed.
    goto :install_deps
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo Python is already installed (using py launcher).
    set PYTHON_CMD=py
    goto :install_deps
)

echo Python is not installed. Please install Python 3.8 or higher.
echo.
echo You can download Python from: https://www.python.org/downloads/
echo.
echo After installing Python, run this script again.
echo.
pause
exit /b 1

:install_deps
echo Installing required dependencies...
echo.

REM Set Python command
if not defined PYTHON_CMD set PYTHON_CMD=python

REM Install pip if not available
%PYTHON_CMD% -m pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing pip...
    %PYTHON_CMD% -m ensurepip --upgrade
)

REM Install required packages
echo Installing CustomTkinter...
%PYTHON_CMD% -m pip install customtkinter==5.2.0

echo Installing Pillow...
%PYTHON_CMD% -m pip install Pillow==10.0.0

echo Installing requests...
%PYTHON_CMD% -m pip install requests==2.31.0

echo Installing psutil...
%PYTHON_CMD% -m pip install psutil==5.9.5

echo Installing cryptography...
%PYTHON_CMD% -m pip install cryptography==41.0.3

echo Installing other dependencies...
%PYTHON_CMD% -m pip install geocoder==1.38.1
%PYTHON_CMD% -m pip install folium==0.14.0
%PYTHON_CMD% -m pip install reportlab==4.0.4
%PYTHON_CMD% -m pip install pycryptodome==3.18.0
%PYTHON_CMD% -m pip install qrcode==7.4.2
%PYTHON_CMD% -m pip install pyotp==2.9.0
%PYTHON_CMD% -m pip install bcrypt==4.0.1

echo.
echo Dependencies installed successfully!
echo.

:run_app
echo Starting Cyber Shield Pro...
echo.
%PYTHON_CMD% main.py

if %errorlevel% neq 0 (
    echo.
    echo An error occurred while running the application.
    echo Please check the error messages above.
    echo.
    pause
)

echo.
echo Application closed.
pause
