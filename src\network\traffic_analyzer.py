#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Traffic Analyzer for Cyber Shield Pro
Advanced network traffic analysis and monitoring
"""

import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import psutil
from ..utils.logger import Logger

@dataclass
class TrafficStats:
    """Network traffic statistics"""
    timestamp: datetime
    bytes_sent: int
    bytes_received: int
    packets_sent: int
    packets_received: int
    connections_active: int
    upload_speed: float  # bytes per second
    download_speed: float  # bytes per second

@dataclass
class BandwidthAlert:
    """Bandwidth usage alert"""
    timestamp: datetime
    alert_type: str  # 'high_usage', 'suspicious_spike', 'unusual_pattern'
    description: str
    current_usage: float
    threshold: float
    severity: str  # 'low', 'medium', 'high'

class TrafficAnalyzer:
    """Advanced network traffic analysis"""
    
    def __init__(self, logger: Logger = None):
        """Initialize traffic analyzer"""
        self.logger = logger
        
        # Traffic data storage
        self.traffic_history = deque(maxlen=3600)  # Last hour of data
        self.bandwidth_history = deque(maxlen=1440)  # Last 24 hours (minute intervals)
        
        # Analysis state
        self.is_analyzing = False
        self.analysis_thread = None
        
        # Thresholds and limits
        self.bandwidth_thresholds = {
            'high_usage': 10 * 1024 * 1024,  # 10 MB/s
            'very_high_usage': 50 * 1024 * 1024,  # 50 MB/s
            'suspicious_spike': 5.0,  # 5x normal usage
        }
        
        # Pattern detection
        self.baseline_usage = {'upload': 0, 'download': 0}
        self.usage_patterns = defaultdict(list)
        self.anomaly_threshold = 3.0  # Standard deviations
        
        # Alerts
        self.alerts = deque(maxlen=100)
        self.alert_callbacks = []
        
        # Statistics
        self.daily_stats = {}
        self.hourly_stats = defaultdict(list)
        
        # Last measurement for speed calculation
        self.last_measurement = None
        self.last_measurement_time = None
    
    def start_analysis(self) -> bool:
        """Start traffic analysis"""
        try:
            if self.is_analyzing:
                return True
            
            self.analysis_thread = threading.Thread(target=self._analysis_worker, daemon=True)
            self.analysis_thread.start()
            
            self.is_analyzing = True
            
            if self.logger:
                self.logger.info("Traffic analysis started")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to start traffic analysis: {e}")
            return False
    
    def stop_analysis(self):
        """Stop traffic analysis"""
        self.is_analyzing = False
        if self.analysis_thread:
            self.analysis_thread.join(timeout=5)
        
        if self.logger:
            self.logger.info("Traffic analysis stopped")
    
    def _analysis_worker(self):
        """Main analysis worker thread"""
        while self.is_analyzing:
            try:
                # Collect current traffic data
                current_stats = self._collect_traffic_stats()
                
                if current_stats:
                    # Add to history
                    self.traffic_history.append(current_stats)
                    
                    # Analyze patterns
                    self._analyze_patterns(current_stats)
                    
                    # Check for anomalies
                    self._check_anomalies(current_stats)
                    
                    # Update statistics
                    self._update_statistics(current_stats)
                
                time.sleep(1)  # Collect data every second
                
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Error in traffic analysis: {e}")
                time.sleep(5)
    
    def _collect_traffic_stats(self) -> Optional[TrafficStats]:
        """Collect current network traffic statistics"""
        try:
            # Get network I/O counters
            net_io = psutil.net_io_counters()
            
            # Get active connections count
            connections = psutil.net_connections()
            active_connections = len([c for c in connections if c.status == 'ESTABLISHED'])
            
            current_time = datetime.now()
            
            # Calculate speeds if we have previous measurement
            upload_speed = 0.0
            download_speed = 0.0
            
            if self.last_measurement and self.last_measurement_time:
                time_delta = (current_time - self.last_measurement_time).total_seconds()
                
                if time_delta > 0:
                    bytes_sent_delta = net_io.bytes_sent - self.last_measurement.bytes_sent
                    bytes_recv_delta = net_io.bytes_recv - self.last_measurement.bytes_recv
                    
                    upload_speed = bytes_sent_delta / time_delta
                    download_speed = bytes_recv_delta / time_delta
            
            # Create traffic stats
            stats = TrafficStats(
                timestamp=current_time,
                bytes_sent=net_io.bytes_sent,
                bytes_received=net_io.bytes_recv,
                packets_sent=net_io.packets_sent,
                packets_received=net_io.packets_recv,
                connections_active=active_connections,
                upload_speed=upload_speed,
                download_speed=download_speed
            )
            
            # Update last measurement
            self.last_measurement = stats
            self.last_measurement_time = current_time
            
            return stats
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error collecting traffic stats: {e}")
            return None
    
    def _analyze_patterns(self, stats: TrafficStats):
        """Analyze traffic patterns"""
        try:
            current_hour = stats.timestamp.hour
            
            # Store hourly patterns
            self.hourly_stats[current_hour].append({
                'upload': stats.upload_speed,
                'download': stats.download_speed,
                'connections': stats.connections_active
            })
            
            # Keep only last 30 days of hourly data
            if len(self.hourly_stats[current_hour]) > 30:
                self.hourly_stats[current_hour] = self.hourly_stats[current_hour][-30:]
            
            # Update baseline usage
            self._update_baseline(stats)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error analyzing patterns: {e}")
    
    def _update_baseline(self, stats: TrafficStats):
        """Update baseline usage patterns"""
        try:
            # Simple moving average for baseline
            alpha = 0.1  # Smoothing factor
            
            self.baseline_usage['upload'] = (
                alpha * stats.upload_speed + 
                (1 - alpha) * self.baseline_usage['upload']
            )
            
            self.baseline_usage['download'] = (
                alpha * stats.download_speed + 
                (1 - alpha) * self.baseline_usage['download']
            )
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating baseline: {e}")
    
    def _check_anomalies(self, stats: TrafficStats):
        """Check for traffic anomalies"""
        try:
            # Check for high bandwidth usage
            total_usage = stats.upload_speed + stats.download_speed
            
            if total_usage > self.bandwidth_thresholds['very_high_usage']:
                self._create_alert(
                    'high_usage',
                    f'Very high bandwidth usage: {self._format_bytes(total_usage)}/s',
                    total_usage,
                    self.bandwidth_thresholds['very_high_usage'],
                    'high'
                )
            elif total_usage > self.bandwidth_thresholds['high_usage']:
                self._create_alert(
                    'high_usage',
                    f'High bandwidth usage: {self._format_bytes(total_usage)}/s',
                    total_usage,
                    self.bandwidth_thresholds['high_usage'],
                    'medium'
                )
            
            # Check for suspicious spikes
            baseline_total = self.baseline_usage['upload'] + self.baseline_usage['download']
            if baseline_total > 0:
                spike_ratio = total_usage / baseline_total
                
                if spike_ratio > self.bandwidth_thresholds['suspicious_spike']:
                    self._create_alert(
                        'suspicious_spike',
                        f'Suspicious traffic spike: {spike_ratio:.1f}x normal usage',
                        total_usage,
                        baseline_total * self.bandwidth_thresholds['suspicious_spike'],
                        'medium'
                    )
            
            # Check for unusual connection count
            if stats.connections_active > 100:
                self._create_alert(
                    'high_connections',
                    f'High number of active connections: {stats.connections_active}',
                    stats.connections_active,
                    100,
                    'medium'
                )
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error checking anomalies: {e}")
    
    def _create_alert(self, alert_type: str, description: str, current_usage: float, 
                     threshold: float, severity: str):
        """Create bandwidth alert"""
        try:
            alert = BandwidthAlert(
                timestamp=datetime.now(),
                alert_type=alert_type,
                description=description,
                current_usage=current_usage,
                threshold=threshold,
                severity=severity
            )
            
            self.alerts.append(alert)
            
            # Call alert callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    if self.logger:
                        self.logger.error(f"Error in alert callback: {e}")
            
            if self.logger:
                self.logger.warning(f"Traffic alert: {description}")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error creating alert: {e}")
    
    def _update_statistics(self, stats: TrafficStats):
        """Update daily and overall statistics"""
        try:
            date_key = stats.timestamp.date().isoformat()
            
            if date_key not in self.daily_stats:
                self.daily_stats[date_key] = {
                    'total_upload': 0,
                    'total_download': 0,
                    'peak_upload': 0,
                    'peak_download': 0,
                    'avg_connections': 0,
                    'connection_samples': 0
                }
            
            daily = self.daily_stats[date_key]
            
            # Update totals (approximate based on speed)
            daily['total_upload'] += stats.upload_speed
            daily['total_download'] += stats.download_speed
            
            # Update peaks
            daily['peak_upload'] = max(daily['peak_upload'], stats.upload_speed)
            daily['peak_download'] = max(daily['peak_download'], stats.download_speed)
            
            # Update average connections
            daily['avg_connections'] = (
                (daily['avg_connections'] * daily['connection_samples'] + stats.connections_active) /
                (daily['connection_samples'] + 1)
            )
            daily['connection_samples'] += 1
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating statistics: {e}")
    
    def analyze_bandwidth(self, upload_speed: float, download_speed: float):
        """Analyze bandwidth usage (external interface)"""
        try:
            # Add to bandwidth history for minute-level tracking
            current_time = datetime.now()
            
            # Only add if it's a new minute
            if (not self.bandwidth_history or 
                current_time.minute != self.bandwidth_history[-1]['timestamp'].minute):
                
                self.bandwidth_history.append({
                    'timestamp': current_time,
                    'upload': upload_speed,
                    'download': download_speed,
                    'total': upload_speed + download_speed
                })
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error analyzing bandwidth: {e}")
    
    def get_current_stats(self) -> Optional[Dict[str, Any]]:
        """Get current traffic statistics"""
        try:
            if not self.traffic_history:
                return None
            
            latest = self.traffic_history[-1]
            
            return {
                'timestamp': latest.timestamp.isoformat(),
                'upload_speed': latest.upload_speed,
                'download_speed': latest.download_speed,
                'total_speed': latest.upload_speed + latest.download_speed,
                'active_connections': latest.connections_active,
                'upload_speed_formatted': self._format_bytes(latest.upload_speed) + '/s',
                'download_speed_formatted': self._format_bytes(latest.download_speed) + '/s',
                'total_speed_formatted': self._format_bytes(latest.upload_speed + latest.download_speed) + '/s'
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting current stats: {e}")
            return None
    
    def get_historical_data(self, hours: int = 1) -> List[Dict[str, Any]]:
        """Get historical traffic data"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            filtered_data = [
                asdict(stats) for stats in self.traffic_history
                if stats.timestamp > cutoff_time
            ]
            
            return filtered_data
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting historical data: {e}")
            return []
    
    def get_daily_statistics(self, days: int = 7) -> Dict[str, Any]:
        """Get daily statistics for specified number of days"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).date()
            
            filtered_stats = {
                date: stats for date, stats in self.daily_stats.items()
                if datetime.fromisoformat(date).date() >= cutoff_date
            }
            
            return filtered_stats
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting daily statistics: {e}")
            return {}
    
    def get_hourly_patterns(self) -> Dict[int, Dict[str, float]]:
        """Get hourly usage patterns"""
        try:
            patterns = {}
            
            for hour, data_list in self.hourly_stats.items():
                if data_list:
                    avg_upload = sum(d['upload'] for d in data_list) / len(data_list)
                    avg_download = sum(d['download'] for d in data_list) / len(data_list)
                    avg_connections = sum(d['connections'] for d in data_list) / len(data_list)
                    
                    patterns[hour] = {
                        'avg_upload': avg_upload,
                        'avg_download': avg_download,
                        'avg_connections': avg_connections,
                        'total_avg': avg_upload + avg_download
                    }
            
            return patterns
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting hourly patterns: {e}")
            return {}
    
    def get_recent_alerts(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent bandwidth alerts"""
        try:
            recent_alerts = list(self.alerts)[-limit:]
            return [asdict(alert) for alert in recent_alerts]
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting recent alerts: {e}")
            return []
    
    def add_alert_callback(self, callback: callable):
        """Add callback for bandwidth alerts"""
        self.alert_callbacks.append(callback)
    
    def remove_alert_callback(self, callback: callable):
        """Remove alert callback"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def set_bandwidth_threshold(self, threshold_type: str, value: float):
        """Set bandwidth threshold"""
        if threshold_type in self.bandwidth_thresholds:
            self.bandwidth_thresholds[threshold_type] = value
    
    def _format_bytes(self, bytes_value: float) -> str:
        """Format bytes value to human readable string"""
        try:
            for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
                if bytes_value < 1024.0:
                    return f"{bytes_value:.1f} {unit}"
                bytes_value /= 1024.0
            return f"{bytes_value:.1f} PB"
            
        except:
            return "0 B"
    
    def clear_history(self):
        """Clear traffic history"""
        self.traffic_history.clear()
        self.bandwidth_history.clear()
        self.alerts.clear()
    
    def export_data(self) -> Dict[str, Any]:
        """Export traffic analysis data"""
        try:
            return {
                'traffic_history': [asdict(stats) for stats in self.traffic_history],
                'bandwidth_history': list(self.bandwidth_history),
                'daily_stats': self.daily_stats,
                'hourly_patterns': self.get_hourly_patterns(),
                'recent_alerts': self.get_recent_alerts(50),
                'baseline_usage': self.baseline_usage,
                'thresholds': self.bandwidth_thresholds
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error exporting data: {e}")
            return {}
