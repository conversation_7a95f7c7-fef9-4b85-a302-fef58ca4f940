#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chart Generator for Cyber Shield Pro
Advanced chart generation for reports and dashboards
"""

import os
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Wedge
import seaborn as sns
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd

class ChartGenerator:
    """Advanced Chart Generation System"""
    
    def __init__(self):
        """Initialize chart generator"""
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Default colors
        self.colors = {
            'primary': '#3498db',
            'secondary': '#2ecc71',
            'warning': '#f39c12',
            'danger': '#e74c3c',
            'info': '#17a2b8',
            'success': '#28a745',
            'dark': '#343a40',
            'light': '#f8f9fa'
        }
        
        # Chart settings
        self.figure_size = (10, 6)
        self.dpi = 300
        self.output_dir = "temp/charts"
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
    
    def create_pie_chart(self, data: Dict[str, Any], title: str, 
                        colors: List[str] = None, output_path: str = None) -> Dict[str, Any]:
        """Create a pie chart"""
        try:
            fig, ax = plt.subplots(figsize=(8, 8))
            
            # Prepare data
            labels = list(data.keys())
            sizes = list(data.values())
            
            # Use custom colors or default
            if colors:
                chart_colors = colors[:len(labels)]
            else:
                chart_colors = [self.colors['primary'], self.colors['secondary'], 
                              self.colors['warning'], self.colors['danger'],
                              self.colors['info'], self.colors['success']][:len(labels)]
            
            # Create pie chart
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=chart_colors,
                                            autopct='%1.1f%%', startangle=90,
                                            explode=[0.05] * len(labels))
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # Make percentage text bold
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(10)
            
            # Equal aspect ratio ensures that pie is drawn as a circle
            ax.axis('equal')
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"pie_chart_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'pie',
                'title': title,
                'path': output_path,
                'data': data
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating pie chart: {e}")
    
    def create_bar_chart(self, data: Dict[str, Any], title: str, 
                        xlabel: str = "", ylabel: str = "", 
                        horizontal: bool = False, output_path: str = None) -> Dict[str, Any]:
        """Create a bar chart"""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)
            
            # Prepare data
            labels = list(data.keys())
            values = list(data.values())
            
            # Create bar chart
            if horizontal:
                bars = ax.barh(labels, values, color=self.colors['primary'], alpha=0.8)
                ax.set_xlabel(ylabel or 'Values')
                ax.set_ylabel(xlabel or 'Categories')
            else:
                bars = ax.bar(labels, values, color=self.colors['primary'], alpha=0.8)
                ax.set_xlabel(xlabel or 'Categories')
                ax.set_ylabel(ylabel or 'Values')
            
            # Add value labels on bars
            for bar in bars:
                if horizontal:
                    width = bar.get_width()
                    ax.text(width, bar.get_y() + bar.get_height()/2, 
                           f'{width}', ha='left', va='center', fontweight='bold')
                else:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2, height,
                           f'{height}', ha='center', va='bottom', fontweight='bold')
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)
            
            # Rotate x-axis labels if needed
            if not horizontal and len(max(labels, key=len)) > 8:
                plt.xticks(rotation=45, ha='right')
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"bar_chart_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'bar',
                'title': title,
                'path': output_path,
                'data': data
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating bar chart: {e}")
    
    def create_line_chart(self, data: Dict[str, List], title: str,
                         xlabel: str = "", ylabel: str = "",
                         output_path: str = None) -> Dict[str, Any]:
        """Create a line chart"""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)
            
            # Plot multiple lines if data contains multiple series
            color_cycle = [self.colors['primary'], self.colors['secondary'],
                          self.colors['warning'], self.colors['danger']]
            
            for i, (label, values) in enumerate(data.items()):
                color = color_cycle[i % len(color_cycle)]
                ax.plot(range(len(values)), values, label=label, 
                       color=color, linewidth=2, marker='o', markersize=4)
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel(xlabel or 'Time')
            ax.set_ylabel(ylabel or 'Values')
            ax.grid(True, alpha=0.3)
            
            # Add legend if multiple series
            if len(data) > 1:
                ax.legend(loc='best')
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"line_chart_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'line',
                'title': title,
                'path': output_path,
                'data': data
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating line chart: {e}")
    
    def create_heatmap(self, data: List[List], title: str,
                      xlabels: List[str] = None, ylabels: List[str] = None,
                      output_path: str = None) -> Dict[str, Any]:
        """Create a heatmap"""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)
            
            # Create heatmap
            im = ax.imshow(data, cmap='YlOrRd', aspect='auto')
            
            # Set labels
            if xlabels:
                ax.set_xticks(range(len(xlabels)))
                ax.set_xticklabels(xlabels)
            
            if ylabels:
                ax.set_yticks(range(len(ylabels)))
                ax.set_yticklabels(ylabels)
            
            # Add colorbar
            plt.colorbar(im, ax=ax)
            
            # Add text annotations
            for i in range(len(data)):
                for j in range(len(data[i])):
                    text = ax.text(j, i, data[i][j], ha="center", va="center",
                                 color="black" if data[i][j] < np.max(data)/2 else "white",
                                 fontweight='bold')
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"heatmap_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'heatmap',
                'title': title,
                'path': output_path,
                'data': data
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating heatmap: {e}")
    
    def create_scatter_plot(self, x_data: List, y_data: List, title: str,
                           xlabel: str = "", ylabel: str = "",
                           output_path: str = None) -> Dict[str, Any]:
        """Create a scatter plot"""
        try:
            fig, ax = plt.subplots(figsize=self.figure_size)
            
            # Create scatter plot
            ax.scatter(x_data, y_data, color=self.colors['primary'], 
                      alpha=0.7, s=50, edgecolors='white', linewidth=1)
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel(xlabel or 'X Values')
            ax.set_ylabel(ylabel or 'Y Values')
            ax.grid(True, alpha=0.3)
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"scatter_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'scatter',
                'title': title,
                'path': output_path,
                'data': {'x': x_data, 'y': y_data}
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating scatter plot: {e}")
    
    def create_gauge_chart(self, value: float, max_value: float, title: str,
                          thresholds: Dict[str, float] = None,
                          output_path: str = None) -> Dict[str, Any]:
        """Create a gauge chart"""
        try:
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # Default thresholds
            if not thresholds:
                thresholds = {
                    'good': max_value * 0.6,
                    'warning': max_value * 0.8,
                    'critical': max_value
                }
            
            # Create gauge
            theta1, theta2 = 0, 180
            
            # Background arc
            arc = patches.Arc((0.5, 0.5), 0.8, 0.8, angle=0, theta1=theta1, theta2=theta2,
                            linewidth=20, color='lightgray')
            ax.add_patch(arc)
            
            # Value arc
            value_angle = (value / max_value) * 180
            value_arc = patches.Arc((0.5, 0.5), 0.8, 0.8, angle=0, theta1=theta1, theta2=value_angle,
                                  linewidth=20, color=self._get_gauge_color(value, thresholds))
            ax.add_patch(value_arc)
            
            # Add value text
            ax.text(0.5, 0.3, f'{value:.1f}', ha='center', va='center',
                   fontsize=24, fontweight='bold')
            ax.text(0.5, 0.2, f'/ {max_value}', ha='center', va='center',
                   fontsize=14, color='gray')
            
            # Styling
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.set_aspect('equal')
            ax.axis('off')
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"gauge_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'gauge',
                'title': title,
                'path': output_path,
                'data': {'value': value, 'max_value': max_value}
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating gauge chart: {e}")
    
    def create_timeline_chart(self, events: List[Dict[str, Any]], title: str,
                             output_path: str = None) -> Dict[str, Any]:
        """Create a timeline chart"""
        try:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            # Sort events by timestamp
            sorted_events = sorted(events, key=lambda x: x['timestamp'])
            
            # Prepare data
            timestamps = [event['timestamp'] for event in sorted_events]
            labels = [event['label'] for event in sorted_events]
            colors_list = [self._get_event_color(event.get('severity', 'info')) 
                          for event in sorted_events]
            
            # Create timeline
            y_positions = range(len(timestamps))
            
            # Plot events
            ax.scatter(timestamps, y_positions, c=colors_list, s=100, alpha=0.8, zorder=3)
            
            # Add event labels
            for i, (timestamp, label) in enumerate(zip(timestamps, labels)):
                ax.annotate(label, (timestamp, i), xytext=(10, 0), 
                           textcoords='offset points', va='center',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
            
            # Styling
            ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
            ax.set_xlabel('Time')
            ax.set_ylabel('Events')
            ax.grid(True, alpha=0.3)
            
            # Format x-axis for dates
            fig.autofmt_xdate()
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"timeline_{timestamp}.png")
            
            plt.tight_layout()
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'timeline',
                'title': title,
                'path': output_path,
                'data': events
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating timeline chart: {e}")
    
    def _get_gauge_color(self, value: float, thresholds: Dict[str, float]) -> str:
        """Get color for gauge based on value and thresholds"""
        if value <= thresholds.get('good', 0):
            return self.colors['success']
        elif value <= thresholds.get('warning', 0):
            return self.colors['warning']
        else:
            return self.colors['danger']
    
    def _get_event_color(self, severity: str) -> str:
        """Get color for event based on severity"""
        color_map = {
            'info': self.colors['info'],
            'warning': self.colors['warning'],
            'error': self.colors['danger'],
            'critical': self.colors['danger'],
            'success': self.colors['success']
        }
        return color_map.get(severity, self.colors['primary'])
    
    def create_dashboard_summary(self, data: Dict[str, Any], output_path: str = None) -> Dict[str, Any]:
        """Create a dashboard summary with multiple charts"""
        try:
            fig = plt.figure(figsize=(16, 12))
            
            # Create subplots
            gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
            
            # Chart 1: Threat severity pie chart
            if 'threat_severity' in data:
                ax1 = fig.add_subplot(gs[0, 0])
                threat_data = data['threat_severity']
                ax1.pie(threat_data.values(), labels=threat_data.keys(), autopct='%1.1f%%')
                ax1.set_title('Threats by Severity')
            
            # Chart 2: System performance gauge
            if 'system_performance' in data:
                ax2 = fig.add_subplot(gs[0, 1])
                perf_value = data['system_performance']
                # Simple gauge representation
                ax2.bar(['CPU', 'Memory', 'Disk'], [perf_value.get('cpu', 0), 
                       perf_value.get('memory', 0), perf_value.get('disk', 0)])
                ax2.set_title('System Performance')
                ax2.set_ylabel('Usage %')
            
            # Chart 3: Network activity
            if 'network_activity' in data:
                ax3 = fig.add_subplot(gs[0, 2])
                net_data = data['network_activity']
                ax3.plot(net_data.get('timestamps', []), net_data.get('traffic', []))
                ax3.set_title('Network Traffic')
                ax3.set_ylabel('Traffic (MB/s)')
            
            # Chart 4: Top threats
            if 'top_threats' in data:
                ax4 = fig.add_subplot(gs[1, :])
                threat_names = list(data['top_threats'].keys())
                threat_counts = list(data['top_threats'].values())
                ax4.barh(threat_names, threat_counts)
                ax4.set_title('Top Threats')
                ax4.set_xlabel('Count')
            
            # Chart 5: Security events timeline
            if 'security_events' in data:
                ax5 = fig.add_subplot(gs[2, :])
                events = data['security_events']
                if events:
                    timestamps = [event['timestamp'] for event in events]
                    severities = [event.get('severity', 'info') for event in events]
                    severity_colors = [self._get_event_color(sev) for sev in severities]
                    ax5.scatter(timestamps, range(len(timestamps)), c=severity_colors)
                    ax5.set_title('Security Events Timeline')
                    ax5.set_ylabel('Event Index')
            
            plt.suptitle('Cyber Shield Pro - Security Dashboard', fontsize=20, fontweight='bold')
            
            # Save chart
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = os.path.join(self.output_dir, f"dashboard_{timestamp}.png")
            
            plt.savefig(output_path, dpi=self.dpi, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            plt.close()
            
            return {
                'type': 'dashboard',
                'title': 'Security Dashboard',
                'path': output_path,
                'data': data
            }
            
        except Exception as e:
            plt.close()
            raise Exception(f"Error creating dashboard summary: {e}")
    
    def cleanup_old_charts(self, days_old: int = 7):
        """Clean up old chart files"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_old)
            
            for filename in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, filename)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        
        except Exception as e:
            print(f"Error cleaning up old charts: {e}")
    
    def get_chart_info(self, chart_path: str) -> Dict[str, Any]:
        """Get information about a chart file"""
        try:
            if os.path.exists(chart_path):
                stat = os.stat(chart_path)
                return {
                    'path': chart_path,
                    'size': stat.st_size,
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'modified': datetime.fromtimestamp(stat.st_mtime)
                }
            return None
        except Exception as e:
            return None
