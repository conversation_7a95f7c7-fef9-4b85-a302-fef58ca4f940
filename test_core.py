#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Core Components of Cyber Shield Pro
Tests the core functionality without GUI
"""

import sys
import os
import time

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all core modules can be imported"""
    print("Testing imports...")
    
    try:
        from src.utils.logger import Logger
        print("✓ Logger imported successfully")
    except Exception as e:
        print(f"✗ Failed to import Logger: {e}")
        return False
    
    try:
        from src.utils.config_manager import ConfigManager
        print("✓ ConfigManager imported successfully")
    except Exception as e:
        print(f"✗ Failed to import ConfigManager: {e}")
        return False
    
    try:
        from src.database.db_manager import DatabaseManager
        print("✓ DatabaseManager imported successfully")
    except Exception as e:
        print(f"✗ Failed to import DatabaseManager: {e}")
        return False
    
    try:
        from src.database.user_manager import UserManager
        print("✓ UserManager imported successfully")
    except Exception as e:
        print(f"✗ Failed to import UserManager: {e}")
        return False
    
    try:
        from src.auth.auth_manager import AuthManager
        print("✓ AuthManager imported successfully")
    except Exception as e:
        print(f"✗ Failed to import AuthManager: {e}")
        return False
    
    try:
        from src.security.scanner_engine import ScannerEngine
        print("✓ ScannerEngine imported successfully")
    except Exception as e:
        print(f"✗ Failed to import ScannerEngine: {e}")
        return False
    
    return True

def test_database():
    """Test database functionality"""
    print("\nTesting database...")
    
    try:
        from src.database.db_manager import DatabaseManager
        from src.database.user_manager import UserManager
        
        # Initialize database
        db_manager = DatabaseManager("test_database.db")
        if not db_manager.initialize_database():
            print("✗ Failed to initialize database")
            return False
        
        print("✓ Database initialized successfully")
        
        # Test user manager
        user_manager = UserManager(db_manager)
        
        # Create test user
        user_id = user_manager.create_user("testuser", "<EMAIL>", "testpass123")
        if user_id:
            print("✓ Test user created successfully")
        else:
            print("✗ Failed to create test user")
            return False
        
        # Test authentication
        user = user_manager.authenticate_user("testuser", "testpass123")
        if user:
            print("✓ User authentication successful")
        else:
            print("✗ User authentication failed")
            return False
        
        # Cleanup
        db_manager.close()
        try:
            os.remove("test_database.db")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def test_security():
    """Test security components"""
    print("\nTesting security components...")
    
    try:
        from src.security.threat_detector import ThreatDetector
        from src.security.file_scanner import FileScanner
        
        # Initialize components
        threat_detector = ThreatDetector()
        file_scanner = FileScanner(threat_detector)
        
        print("✓ Security components initialized")
        
        # Test threat detector
        test_file = __file__  # Use this script as test file
        result = threat_detector.analyze_file(test_file)
        
        if 'is_threat' in result:
            print("✓ Threat detector working")
        else:
            print("✗ Threat detector not working properly")
            return False
        
        # Test file scanner
        scan_result = file_scanner.scan_file(test_file)
        
        if 'file_path' in scan_result:
            print("✓ File scanner working")
        else:
            print("✗ File scanner not working properly")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Security test failed: {e}")
        return False

def test_config():
    """Test configuration management"""
    print("\nTesting configuration...")
    
    try:
        from src.utils.config_manager import ConfigManager
        
        # Initialize config manager
        config_manager = ConfigManager("test_config.json")
        
        # Test setting and getting values
        config_manager.set("test.value", "hello")
        value = config_manager.get("test.value")
        
        if value == "hello":
            print("✓ Configuration management working")
        else:
            print("✗ Configuration management failed")
            return False
        
        # Cleanup
        try:
            os.remove("test_config.json")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging functionality"""
    print("\nTesting logging...")
    
    try:
        from src.utils.logger import Logger
        
        # Initialize logger
        logger = Logger("TestLogger")
        
        # Test logging
        logger.info("Test info message")
        logger.warning("Test warning message")
        logger.error("Test error message")
        
        print("✓ Logging working")
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("Cyber Shield Pro - Core Components Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Database Test", test_database),
        ("Security Test", test_security),
        ("Configuration Test", test_config),
        ("Logging Test", test_logging),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Core components are working correctly.")
        print("\nYou can now run the main application:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\nCommon issues:")
        print("1. Missing Python packages - run: pip install -r requirements.txt")
        print("2. Python version too old - requires Python 3.8+")
        print("3. Permission issues - try running as administrator")
    
    print("=" * 60)
    
    return passed == total

if __name__ == "__main__":
    success = main()
    
    # Keep window open on Windows
    if os.name == 'nt':
        input("\nPress Enter to exit...")
    
    sys.exit(0 if success else 1)
