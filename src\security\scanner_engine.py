#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scanner Engine for Cyber Shield Pro
Main scanning engine that coordinates different types of scans
"""

import os
import time
import threading
from typing import List, Dict, Any, Callable, Optional
from pathlib import Path
from .threat_detector import ThreatDetector
from .file_scanner import FileScanner
from .process_monitor import ProcessMonitor
from ..database.threat_manager import ThreatManager
from ..database.log_manager import LogManager
from ..utils.logger import Logger

class ScannerEngine:
    """Main scanning engine"""
    
    def __init__(self, threat_manager: ThreatManager, log_manager: LogManager, logger: Logger):
        """Initialize scanner engine"""
        self.threat_manager = threat_manager
        self.log_manager = log_manager
        self.logger = logger
        
        # Initialize components
        self.threat_detector = ThreatDetector()
        self.file_scanner = FileScanner(self.threat_detector)
        self.process_monitor = ProcessMonitor(self.threat_detector)
        
        # Scan state
        self.is_scanning = False
        self.scan_thread = None
        self.scan_progress = 0
        self.scan_results = {}
        self.scan_callbacks = {}
        
        # Scan statistics
        self.files_scanned = 0
        self.threats_found = 0
        self.scan_start_time = None
        self.scan_duration = 0
        
        # Default scan paths
        self.default_scan_paths = [
            os.path.expanduser("~"),  # User home directory
            "C:\\Program Files",
            "C:\\Program Files (x86)",
            "C:\\Windows\\System32",
            "C:\\Windows\\Temp",
            os.path.expanduser("~/Downloads"),
            os.path.expanduser("~/Desktop"),
            os.path.expanduser("~/Documents"),
        ]
        
        # File extensions to scan
        self.scan_extensions = {
            '.exe', '.dll', '.bat', '.cmd', '.com', '.scr', '.pif',
            '.vbs', '.js', '.jar', '.zip', '.rar', '.7z',
            '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp',
            '.mp3', '.mp4', '.avi', '.mkv', '.mov'
        }
    
    def start_quick_scan(self, user_id: int, progress_callback: Callable = None, 
                        completion_callback: Callable = None) -> bool:
        """Start a quick scan"""
        if self.is_scanning:
            return False
        
        # Quick scan paths (high-risk areas)
        quick_scan_paths = [
            os.path.expanduser("~/Downloads"),
            os.path.expanduser("~/Desktop"),
            "C:\\Windows\\Temp",
            os.path.expanduser("~/AppData/Local/Temp"),
        ]
        
        return self._start_scan(
            scan_type="quick",
            user_id=user_id,
            scan_paths=quick_scan_paths,
            progress_callback=progress_callback,
            completion_callback=completion_callback
        )
    
    def start_full_scan(self, user_id: int, progress_callback: Callable = None, 
                       completion_callback: Callable = None) -> bool:
        """Start a full system scan"""
        if self.is_scanning:
            return False
        
        return self._start_scan(
            scan_type="full",
            user_id=user_id,
            scan_paths=self.default_scan_paths,
            progress_callback=progress_callback,
            completion_callback=completion_callback
        )
    
    def start_custom_scan(self, user_id: int, scan_paths: List[str], 
                         progress_callback: Callable = None, 
                         completion_callback: Callable = None) -> bool:
        """Start a custom scan with specified paths"""
        if self.is_scanning:
            return False
        
        return self._start_scan(
            scan_type="custom",
            user_id=user_id,
            scan_paths=scan_paths,
            progress_callback=progress_callback,
            completion_callback=completion_callback
        )
    
    def _start_scan(self, scan_type: str, user_id: int, scan_paths: List[str],
                   progress_callback: Callable = None, 
                   completion_callback: Callable = None) -> bool:
        """Internal method to start scanning"""
        try:
            # Set scan state
            self.is_scanning = True
            self.scan_progress = 0
            self.files_scanned = 0
            self.threats_found = 0
            self.scan_start_time = time.time()
            self.scan_results = {
                'scan_type': scan_type,
                'user_id': user_id,
                'threats': [],
                'errors': [],
                'skipped_files': []
            }
            
            # Store callbacks
            self.scan_callbacks = {
                'progress': progress_callback,
                'completion': completion_callback
            }
            
            # Log scan start
            self.log_manager.log_info(
                user_id, 'security', f'Started {scan_type} scan',
                {'scan_paths': scan_paths}
            )
            
            # Start scan thread
            self.scan_thread = threading.Thread(
                target=self._scan_worker,
                args=(scan_type, user_id, scan_paths),
                daemon=True
            )
            self.scan_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting scan: {e}")
            self.is_scanning = False
            return False
    
    def _scan_worker(self, scan_type: str, user_id: int, scan_paths: List[str]):
        """Worker thread for scanning"""
        try:
            # Get all files to scan
            files_to_scan = []
            for path in scan_paths:
                if os.path.exists(path):
                    files_to_scan.extend(self._get_files_to_scan(path))
            
            total_files = len(files_to_scan)
            self.logger.info(f"Scanning {total_files} files")
            
            # Scan files
            for i, file_path in enumerate(files_to_scan):
                if not self.is_scanning:  # Check if scan was cancelled
                    break
                
                try:
                    # Update progress
                    self.scan_progress = int((i / total_files) * 100) if total_files > 0 else 100
                    self.files_scanned = i + 1
                    
                    # Call progress callback
                    if self.scan_callbacks.get('progress'):
                        self.scan_callbacks['progress'](
                            self.scan_progress, 
                            self.files_scanned, 
                            self.threats_found,
                            file_path
                        )
                    
                    # Scan file
                    scan_result = self.file_scanner.scan_file(file_path)
                    
                    if scan_result['is_threat']:
                        # Threat found
                        threat_info = {
                            'file_path': file_path,
                            'threat_type': scan_result['threat_type'],
                            'threat_name': scan_result['threat_name'],
                            'severity': scan_result['severity'],
                            'description': scan_result['description']
                        }
                        
                        self.scan_results['threats'].append(threat_info)
                        self.threats_found += 1
                        
                        # Add to database
                        self.threat_manager.add_threat(
                            user_id=user_id,
                            threat_type=scan_result['threat_type'],
                            threat_name=scan_result['threat_name'],
                            file_path=file_path,
                            severity=scan_result['severity']
                        )
                        
                        self.logger.warning(f"Threat detected: {file_path}")
                
                except Exception as e:
                    self.scan_results['errors'].append({
                        'file_path': file_path,
                        'error': str(e)
                    })
                    self.logger.error(f"Error scanning file {file_path}: {e}")
            
            # Scan complete
            self.scan_duration = time.time() - self.scan_start_time
            self.scan_progress = 100
            
            # Log scan completion
            self.log_manager.log_info(
                user_id, 'security', f'Completed {scan_type} scan',
                {
                    'files_scanned': self.files_scanned,
                    'threats_found': self.threats_found,
                    'scan_duration': self.scan_duration
                }
            )
            
            # Call completion callback
            if self.scan_callbacks.get('completion'):
                self.scan_callbacks['completion'](self.scan_results)
            
        except Exception as e:
            self.logger.error(f"Error in scan worker: {e}")
            self.scan_results['errors'].append({
                'general_error': str(e)
            })
        
        finally:
            self.is_scanning = False
    
    def _get_files_to_scan(self, root_path: str) -> List[str]:
        """Get list of files to scan from a directory"""
        files_to_scan = []
        
        try:
            if os.path.isfile(root_path):
                # Single file
                if self._should_scan_file(root_path):
                    files_to_scan.append(root_path)
            else:
                # Directory
                for root, dirs, files in os.walk(root_path):
                    # Skip system directories
                    dirs[:] = [d for d in dirs if not self._is_system_directory(os.path.join(root, d))]
                    
                    for file in files:
                        file_path = os.path.join(root, file)
                        if self._should_scan_file(file_path):
                            files_to_scan.append(file_path)
                        
                        # Limit number of files for performance
                        if len(files_to_scan) > 100000:  # 100k files max
                            break
                    
                    if len(files_to_scan) > 100000:
                        break
        
        except Exception as e:
            self.logger.error(f"Error getting files to scan from {root_path}: {e}")
        
        return files_to_scan
    
    def _should_scan_file(self, file_path: str) -> bool:
        """Check if file should be scanned"""
        try:
            # Check file extension
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in self.scan_extensions:
                return False
            
            # Check file size (skip very large files)
            if os.path.getsize(file_path) > 100 * 1024 * 1024:  # 100MB
                return False
            
            # Check if file is accessible
            if not os.access(file_path, os.R_OK):
                return False
            
            return True
            
        except Exception:
            return False
    
    def _is_system_directory(self, dir_path: str) -> bool:
        """Check if directory is a system directory to skip"""
        system_dirs = {
            'System Volume Information',
            '$Recycle.Bin',
            'Recovery',
            'Windows\\WinSxS',
            'Windows\\System32\\DriverStore',
            'ProgramData\\Microsoft\\Windows\\WER',
        }
        
        dir_name = os.path.basename(dir_path)
        return dir_name in system_dirs or dir_name.startswith('.')
    
    def stop_scan(self) -> bool:
        """Stop current scan"""
        if not self.is_scanning:
            return False
        
        self.is_scanning = False
        
        if self.scan_thread and self.scan_thread.is_alive():
            self.scan_thread.join(timeout=5)
        
        self.logger.info("Scan stopped by user")
        return True
    
    def get_scan_status(self) -> Dict[str, Any]:
        """Get current scan status"""
        return {
            'is_scanning': self.is_scanning,
            'progress': self.scan_progress,
            'files_scanned': self.files_scanned,
            'threats_found': self.threats_found,
            'scan_duration': time.time() - self.scan_start_time if self.scan_start_time else 0
        }
    
    def get_scan_results(self) -> Dict[str, Any]:
        """Get scan results"""
        return self.scan_results.copy() if self.scan_results else {}
    
    def quarantine_threat(self, threat_id: int, file_path: str) -> bool:
        """Quarantine a detected threat"""
        try:
            # Create quarantine directory
            quarantine_dir = Path("temp/quarantine")
            quarantine_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate quarantine filename
            import uuid
            quarantine_filename = f"{uuid.uuid4().hex}_{Path(file_path).name}"
            quarantine_path = quarantine_dir / quarantine_filename
            
            # Move file to quarantine
            import shutil
            shutil.move(file_path, quarantine_path)
            
            # Update threat status in database
            self.threat_manager.quarantine_threat(threat_id, str(quarantine_path))
            
            self.logger.info(f"Threat quarantined: {file_path} -> {quarantine_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error quarantining threat: {e}")
            return False
    
    def delete_threat(self, threat_id: int, file_path: str) -> bool:
        """Delete a detected threat"""
        try:
            # Delete file
            os.remove(file_path)
            
            # Update threat status in database
            self.threat_manager.delete_threat(threat_id)
            
            self.logger.info(f"Threat deleted: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting threat: {e}")
            return False
