#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Login Window for Cyber Shield Pro
Handles user authentication and registration
"""

import tkinter as tk
import customtkinter as ctk
from tkinter import messagebox
import threading
from typing import Callable, Dict, Any
from .cyber_theme import CyberTheme
from ..auth.auth_manager import AuthManager
from ..database.user_manager import UserManager
from ..database.log_manager import LogManager
from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..database.db_manager import DatabaseManager

class LoginWindow:
    """Login and registration window"""
    
    def __init__(self, parent: ctk.CTk, theme: CyberTheme, app_name: str, version: str, copyright: str,
                 on_login_success: Callable, on_register_success: Callable,
                 logger: Logger, config_manager: ConfigManager, db_manager: DatabaseManager):
        """Initialize login window"""
        self.parent = parent
        self.theme = theme
        self.app_name = app_name
        self.version = version
        self.copyright = copyright
        self.on_login_success = on_login_success
        self.on_register_success = on_register_success
        self.logger = logger
        self.config_manager = config_manager
        self.db_manager = db_manager
        
        # Initialize managers
        self.user_manager = UserManager(db_manager)
        self.log_manager = LogManager(db_manager)
        self.auth_manager = AuthManager(self.user_manager, self.log_manager)
        
        # UI state
        self.current_mode = "login"  # login, register, forgot_password
        self.remember_me_var = tk.BooleanVar()
        
        # Create UI
        self._create_ui()
    
    def _create_ui(self):
        """Create the login window UI"""
        # Main container
        self.main_frame = ctk.CTkFrame(
            self.parent,
            **self.theme.get_frame_style('primary')
        )
        self.main_frame.pack(fill="both", expand=True)
        
        # Create background effect
        self._create_background()
        
        # Create login form
        self._create_login_form()
        
        # Create footer
        self._create_footer()
    
    def _create_background(self):
        """Create cyber-themed background"""
        # Background frame
        self.bg_frame = ctk.CTkFrame(
            self.main_frame,
            **self.theme.get_frame_style('primary')
        )
        self.bg_frame.place(x=0, y=0, relwidth=1, relheight=1)
        
        # Matrix effect canvas (simplified)
        self.matrix_canvas = tk.Canvas(
            self.bg_frame,
            bg=self.theme.colors['bg_primary'],
            highlightthickness=0
        )
        self.matrix_canvas.place(x=0, y=0, relwidth=1, relheight=1)
        
        # Start matrix animation
        self._start_matrix_animation()
    
    def _create_login_form(self):
        """Create the login form"""
        # Form container
        self.form_frame = ctk.CTkFrame(
            self.main_frame,
            width=400,
            height=500,
            **self.theme.get_frame_style('accent')
        )
        self.form_frame.place(relx=0.5, rely=0.5, anchor="center")
        
        # Title
        self.title_label = ctk.CTkLabel(
            self.form_frame,
            text=self.app_name,
            **self.theme.get_label_style('title')
        )
        self.title_label.pack(pady=(30, 10))
        
        # Subtitle
        self.subtitle_label = ctk.CTkLabel(
            self.form_frame,
            text="Advanced Cybersecurity Protection",
            **self.theme.get_label_style('secondary')
        )
        self.subtitle_label.pack(pady=(0, 30))
        
        # Login form
        self._create_login_fields()
        
        # Buttons
        self._create_buttons()
        
        # Mode switcher
        self._create_mode_switcher()
    
    def _create_login_fields(self):
        """Create login input fields"""
        # Username field
        self.username_label = ctk.CTkLabel(
            self.form_frame,
            text="اسم المستخدم / Username:",
            **self.theme.get_label_style('primary')
        )
        self.username_label.pack(pady=(10, 5), padx=20, anchor="w")
        
        self.username_entry = ctk.CTkEntry(
            self.form_frame,
            width=350,
            height=40,
            placeholder_text="أدخل اسم المستخدم",
            **self.theme.get_entry_style()
        )
        self.username_entry.pack(pady=(0, 15), padx=20)
        
        # Password field
        self.password_label = ctk.CTkLabel(
            self.form_frame,
            text="كلمة المرور / Password:",
            **self.theme.get_label_style('primary')
        )
        self.password_label.pack(pady=(0, 5), padx=20, anchor="w")
        
        self.password_entry = ctk.CTkEntry(
            self.form_frame,
            width=350,
            height=40,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            **self.theme.get_entry_style()
        )
        self.password_entry.pack(pady=(0, 15), padx=20)
        
        # Additional fields for registration (initially hidden)
        self._create_registration_fields()
        
        # Remember me checkbox
        self.remember_frame = ctk.CTkFrame(
            self.form_frame,
            fg_color="transparent"
        )
        self.remember_frame.pack(pady=(0, 20), padx=20, fill="x")
        
        self.remember_checkbox = ctk.CTkCheckBox(
            self.remember_frame,
            text="تذكرني / Remember Me",
            variable=self.remember_me_var,
            **self.theme.get_label_style('secondary')
        )
        self.remember_checkbox.pack(side="left")
    
    def _create_registration_fields(self):
        """Create additional fields for registration"""
        # Email field (for registration)
        self.email_label = ctk.CTkLabel(
            self.form_frame,
            text="البريد الإلكتروني / Email:",
            **self.theme.get_label_style('primary')
        )
        
        self.email_entry = ctk.CTkEntry(
            self.form_frame,
            width=350,
            height=40,
            placeholder_text="أدخل البريد الإلكتروني",
            **self.theme.get_entry_style()
        )
        
        # Confirm password field (for registration)
        self.confirm_password_label = ctk.CTkLabel(
            self.form_frame,
            text="تأكيد كلمة المرور / Confirm Password:",
            **self.theme.get_label_style('primary')
        )
        
        self.confirm_password_entry = ctk.CTkEntry(
            self.form_frame,
            width=350,
            height=40,
            placeholder_text="أعد إدخال كلمة المرور",
            show="*",
            **self.theme.get_entry_style()
        )
        
        # Initially hide registration fields
        self._hide_registration_fields()
    
    def _create_buttons(self):
        """Create action buttons"""
        # Main action button
        self.action_button = ctk.CTkButton(
            self.form_frame,
            text="تسجيل الدخول / Login",
            width=350,
            height=45,
            command=self._handle_action,
            **self.theme.get_button_style('primary')
        )
        self.action_button.pack(pady=(10, 15), padx=20)
        
        # Secondary button (for mode switching)
        self.secondary_button = ctk.CTkButton(
            self.form_frame,
            text="إنشاء حساب جديد / Register",
            width=350,
            height=35,
            command=self._switch_to_register,
            **self.theme.get_button_style('secondary')
        )
        self.secondary_button.pack(pady=(0, 10), padx=20)
    
    def _create_mode_switcher(self):
        """Create mode switching links"""
        self.mode_frame = ctk.CTkFrame(
            self.form_frame,
            fg_color="transparent"
        )
        self.mode_frame.pack(pady=(10, 20), padx=20)
        
        # Forgot password link
        self.forgot_password_button = ctk.CTkButton(
            self.mode_frame,
            text="نسيت كلمة المرور؟ / Forgot Password?",
            width=200,
            height=25,
            command=self._switch_to_forgot_password,
            fg_color="transparent",
            text_color=self.theme.colors['text_accent'],
            hover_color=self.theme.colors['bg_tertiary']
        )
        self.forgot_password_button.pack()
    
    def _create_footer(self):
        """Create footer with copyright"""
        self.footer_frame = ctk.CTkFrame(
            self.main_frame,
            height=60,
            **self.theme.get_frame_style('secondary')
        )
        self.footer_frame.pack(side="bottom", fill="x")
        
        # Copyright label
        self.copyright_label = ctk.CTkLabel(
            self.footer_frame,
            text=f"{self.copyright} | Version {self.version}",
            **self.theme.get_label_style('secondary')
        )
        self.copyright_label.pack(expand=True)
    
    def _start_matrix_animation(self):
        """Start matrix background animation"""
        def animate():
            # Simple matrix effect - falling characters
            try:
                self.matrix_canvas.delete("matrix")
                width = self.matrix_canvas.winfo_width()
                height = self.matrix_canvas.winfo_height()
                
                if width > 1 and height > 1:
                    import random
                    chars = "01"
                    
                    for _ in range(20):
                        x = random.randint(0, width)
                        y = random.randint(0, height)
                        char = random.choice(chars)
                        
                        self.matrix_canvas.create_text(
                            x, y, text=char,
                            fill=self.theme.colors['matrix_green'],
                            font=self.theme.fonts['matrix'],
                            tags="matrix"
                        )
                
                # Schedule next animation frame
                self.parent.after(100, animate)
                
            except:
                pass
        
        # Start animation
        self.parent.after(1000, animate)
    
    def _handle_action(self):
        """Handle main action button click"""
        if self.current_mode == "login":
            self._handle_login()
        elif self.current_mode == "register":
            self._handle_register()
        elif self.current_mode == "forgot_password":
            self._handle_forgot_password()
    
    def _handle_login(self):
        """Handle login attempt"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get()
        
        if not username or not password:
            messagebox.showerror("خطأ / Error", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # Disable button during login
        self.action_button.configure(state="disabled", text="جاري تسجيل الدخول...")
        
        def login_thread():
            try:
                success, message, user_data = self.auth_manager.login_user(username, password)
                
                # Update UI in main thread
                self.parent.after(0, lambda: self._handle_login_result(success, message, user_data))
                
            except Exception as e:
                self.parent.after(0, lambda: self._handle_login_result(False, "خطأ في النظام", {}))
        
        # Start login in separate thread
        threading.Thread(target=login_thread, daemon=True).start()
    
    def _handle_login_result(self, success: bool, message: str, user_data: Dict[str, Any]):
        """Handle login result"""
        # Re-enable button
        self.action_button.configure(state="normal", text="تسجيل الدخول / Login")
        
        if success:
            # Call success callback
            self.on_login_success(user_data)
        else:
            messagebox.showerror("فشل تسجيل الدخول / Login Failed", message)
    
    def _handle_register(self):
        """Handle registration attempt"""
        username = self.username_entry.get().strip()
        email = self.email_entry.get().strip()
        password = self.password_entry.get()
        confirm_password = self.confirm_password_entry.get()
        
        if not all([username, email, password, confirm_password]):
            messagebox.showerror("خطأ / Error", "يرجى ملء جميع الحقول")
            return
        
        # Disable button during registration
        self.action_button.configure(state="disabled", text="جاري إنشاء الحساب...")
        
        def register_thread():
            try:
                success, message = self.auth_manager.register_user(username, email, password, confirm_password)
                
                # Update UI in main thread
                self.parent.after(0, lambda: self._handle_register_result(success, message, username))
                
            except Exception as e:
                self.parent.after(0, lambda: self._handle_register_result(False, "خطأ في النظام", username))
        
        # Start registration in separate thread
        threading.Thread(target=register_thread, daemon=True).start()
    
    def _handle_register_result(self, success: bool, message: str, username: str):
        """Handle registration result"""
        # Re-enable button
        self.action_button.configure(state="normal", text="إنشاء حساب / Register")
        
        if success:
            messagebox.showinfo("نجح التسجيل / Registration Successful", message)
            # Switch back to login mode
            self._switch_to_login()
        else:
            messagebox.showerror("فشل التسجيل / Registration Failed", message)
    
    def _handle_forgot_password(self):
        """Handle forgot password"""
        # This would implement password recovery
        messagebox.showinfo("استرجاع كلمة المرور", "سيتم إرسال رابط استرجاع كلمة المرور إلى بريدك الإلكتروني")
        self._switch_to_login()
    
    def _switch_to_login(self):
        """Switch to login mode"""
        self.current_mode = "login"
        self._hide_registration_fields()
        self.action_button.configure(text="تسجيل الدخول / Login")
        self.secondary_button.configure(text="إنشاء حساب جديد / Register", command=self._switch_to_register)
    
    def _switch_to_register(self):
        """Switch to registration mode"""
        self.current_mode = "register"
        self._show_registration_fields()
        self.action_button.configure(text="إنشاء حساب / Register")
        self.secondary_button.configure(text="العودة لتسجيل الدخول / Back to Login", command=self._switch_to_login)
    
    def _switch_to_forgot_password(self):
        """Switch to forgot password mode"""
        self.current_mode = "forgot_password"
        self._hide_registration_fields()
        self.action_button.configure(text="استرجاع كلمة المرور / Reset Password")
        self.secondary_button.configure(text="العودة لتسجيل الدخول / Back to Login", command=self._switch_to_login)
    
    def _show_registration_fields(self):
        """Show registration-specific fields"""
        # Insert email field after username
        self.email_label.pack(pady=(10, 5), padx=20, anchor="w", after=self.username_entry)
        self.email_entry.pack(pady=(0, 15), padx=20, after=self.email_label)
        
        # Insert confirm password after password
        self.confirm_password_label.pack(pady=(0, 5), padx=20, anchor="w", after=self.password_entry)
        self.confirm_password_entry.pack(pady=(0, 15), padx=20, after=self.confirm_password_label)
    
    def _hide_registration_fields(self):
        """Hide registration-specific fields"""
        self.email_label.pack_forget()
        self.email_entry.pack_forget()
        self.confirm_password_label.pack_forget()
        self.confirm_password_entry.pack_forget()
