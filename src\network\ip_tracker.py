#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IP Tracker for Cyber Shield Pro
Advanced IP tracking with geolocation and threat intelligence
"""

import requests
import json
import time
import threading
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import ipaddress
from ..utils.logger import Logger

@dataclass
class IPInfo:
    """IP address information"""
    ip: str
    country: Optional[str] = None
    country_code: Optional[str] = None
    region: Optional[str] = None
    city: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    timezone: Optional[str] = None
    isp: Optional[str] = None
    organization: Optional[str] = None
    as_number: Optional[str] = None
    as_name: Optional[str] = None
    is_proxy: bool = False
    is_vpn: bool = False
    is_tor: bool = False
    is_malicious: bool = False
    threat_level: str = "low"
    first_seen: Optional[datetime] = None
    last_seen: Optional[datetime] = None
    connection_count: int = 0

class IPTracker:
    """Advanced IP tracking and analysis"""
    
    def __init__(self, logger: Logger = None):
        """Initialize IP tracker"""
        self.logger = logger
        
        # IP database
        self.ip_database = {}
        self.ip_cache = {}
        
        # API configurations
        self.api_keys = {
            'ipapi': None,  # Get from https://ipapi.co/
            'ipgeolocation': None,  # Get from https://ipgeolocation.io/
            'virustotal': None,  # Get from https://www.virustotal.com/
        }
        
        # Rate limiting
        self.api_calls = {}
        self.rate_limits = {
            'ipapi': {'calls': 0, 'reset_time': time.time(), 'limit': 1000},
            'ipgeolocation': {'calls': 0, 'reset_time': time.time(), 'limit': 1000},
            'virustotal': {'calls': 0, 'reset_time': time.time(), 'limit': 500},
        }
        
        # Cache settings
        self.cache_duration = 3600  # 1 hour
        self.max_cache_size = 10000
        
        # Threat intelligence
        self.malicious_ips = set()
        self.proxy_ips = set()
        self.vpn_ips = set()
        self.tor_ips = set()
        
        # Load local databases
        self._load_local_databases()
    
    def track_ip(self, ip_address: str, connection_info: Dict[str, Any] = None) -> IPInfo:
        """Track IP address and gather information"""
        try:
            # Validate IP address
            ipaddress.ip_address(ip_address)
            
            # Check if IP is already tracked
            if ip_address in self.ip_database:
                ip_info = self.ip_database[ip_address]
                ip_info.last_seen = datetime.now()
                ip_info.connection_count += 1
            else:
                # Create new IP info
                ip_info = IPInfo(
                    ip=ip_address,
                    first_seen=datetime.now(),
                    last_seen=datetime.now(),
                    connection_count=1
                )
                
                # Gather information about the IP
                self._gather_ip_info(ip_info)
                
                # Store in database
                self.ip_database[ip_address] = ip_info
            
            # Update connection information
            if connection_info:
                self._update_connection_info(ip_info, connection_info)
            
            return ip_info
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error tracking IP {ip_address}: {e}")
            return IPInfo(ip=ip_address)
    
    def _gather_ip_info(self, ip_info: IPInfo):
        """Gather comprehensive information about IP"""
        try:
            # Check cache first
            cached_info = self._get_cached_info(ip_info.ip)
            if cached_info:
                self._update_ip_info_from_cache(ip_info, cached_info)
                return
            
            # Check if it's a private IP
            if self._is_private_ip(ip_info.ip):
                ip_info.country = "Private Network"
                ip_info.organization = "Local Network"
                return
            
            # Gather geolocation information
            self._get_geolocation_info(ip_info)
            
            # Check threat intelligence
            self._check_threat_intelligence(ip_info)
            
            # Check for proxy/VPN/Tor
            self._check_proxy_vpn_tor(ip_info)
            
            # Cache the information
            self._cache_ip_info(ip_info)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error gathering IP info for {ip_info.ip}: {e}")
    
    def _get_geolocation_info(self, ip_info: IPInfo):
        """Get geolocation information for IP"""
        try:
            # Try free API first
            url = f"http://ip-api.com/json/{ip_info.ip}"
            
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status') == 'success':
                    ip_info.country = data.get('country')
                    ip_info.country_code = data.get('countryCode')
                    ip_info.region = data.get('regionName')
                    ip_info.city = data.get('city')
                    ip_info.latitude = data.get('lat')
                    ip_info.longitude = data.get('lon')
                    ip_info.timezone = data.get('timezone')
                    ip_info.isp = data.get('isp')
                    ip_info.organization = data.get('org')
                    ip_info.as_number = data.get('as')
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error getting geolocation for {ip_info.ip}: {e}")
    
    def _check_threat_intelligence(self, ip_info: IPInfo):
        """Check IP against threat intelligence"""
        try:
            # Check local malicious IP database
            if ip_info.ip in self.malicious_ips:
                ip_info.is_malicious = True
                ip_info.threat_level = "high"
            
            # Check against known threat lists
            if self._check_malicious_ip_lists(ip_info.ip):
                ip_info.is_malicious = True
                ip_info.threat_level = "high"
                self.malicious_ips.add(ip_info.ip)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error checking threat intelligence for {ip_info.ip}: {e}")
    
    def _check_proxy_vpn_tor(self, ip_info: IPInfo):
        """Check if IP is proxy/VPN/Tor"""
        try:
            # Check local databases
            if ip_info.ip in self.proxy_ips:
                ip_info.is_proxy = True
            
            if ip_info.ip in self.vpn_ips:
                ip_info.is_vpn = True
            
            if ip_info.ip in self.tor_ips:
                ip_info.is_tor = True
            
            # Additional checks based on organization/ISP
            if ip_info.organization:
                org_lower = ip_info.organization.lower()
                if any(keyword in org_lower for keyword in ['vpn', 'proxy', 'tor', 'anonymous']):
                    ip_info.is_vpn = True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error checking proxy/VPN/Tor for {ip_info.ip}: {e}")
    
    def _check_malicious_ip_lists(self, ip_address: str) -> bool:
        """Check IP against malicious IP lists"""
        try:
            # This would check against various threat intelligence feeds
            # For demo purposes, return False
            return False
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error checking malicious IP lists: {e}")
            return False
    
    def _is_private_ip(self, ip_address: str) -> bool:
        """Check if IP is private/local"""
        try:
            ip = ipaddress.ip_address(ip_address)
            return ip.is_private or ip.is_loopback or ip.is_link_local
        except:
            return False
    
    def _get_cached_info(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Get cached IP information"""
        if ip_address in self.ip_cache:
            cache_entry = self.ip_cache[ip_address]
            if time.time() - cache_entry['timestamp'] < self.cache_duration:
                return cache_entry['data']
            else:
                del self.ip_cache[ip_address]
        return None
    
    def _cache_ip_info(self, ip_info: IPInfo):
        """Cache IP information"""
        try:
            # Clean cache if too large
            if len(self.ip_cache) >= self.max_cache_size:
                # Remove oldest entries
                sorted_cache = sorted(self.ip_cache.items(), key=lambda x: x[1]['timestamp'])
                for i in range(len(sorted_cache) // 2):
                    del self.ip_cache[sorted_cache[i][0]]
            
            self.ip_cache[ip_info.ip] = {
                'timestamp': time.time(),
                'data': asdict(ip_info)
            }
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error caching IP info: {e}")
    
    def _update_ip_info_from_cache(self, ip_info: IPInfo, cached_data: Dict[str, Any]):
        """Update IP info from cached data"""
        try:
            for key, value in cached_data.items():
                if hasattr(ip_info, key) and value is not None:
                    setattr(ip_info, key, value)
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating from cache: {e}")
    
    def _update_connection_info(self, ip_info: IPInfo, connection_info: Dict[str, Any]):
        """Update IP info with connection details"""
        try:
            # This would store connection-specific information
            pass
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating connection info: {e}")
    
    def _load_local_databases(self):
        """Load local threat intelligence databases"""
        try:
            # Load malicious IPs
            self._load_malicious_ips()
            
            # Load proxy/VPN/Tor lists
            self._load_proxy_lists()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error loading local databases: {e}")
    
    def _load_malicious_ips(self):
        """Load malicious IP database"""
        try:
            # This would load from a file or database
            # For demo, adding some test IPs
            test_malicious = {
                "0.0.0.0",
                "*************"  # Test IP
            }
            self.malicious_ips.update(test_malicious)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error loading malicious IPs: {e}")
    
    def _load_proxy_lists(self):
        """Load proxy/VPN/Tor lists"""
        try:
            # This would load from files or APIs
            pass
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error loading proxy lists: {e}")
    
    def _check_rate_limit(self, api_name: str) -> bool:
        """Check if API rate limit allows request"""
        try:
            rate_info = self.rate_limits.get(api_name, {})
            current_time = time.time()
            
            # Reset counter if hour has passed
            if current_time - rate_info.get('reset_time', 0) > 3600:
                rate_info['calls'] = 0
                rate_info['reset_time'] = current_time
            
            # Check if under limit
            return rate_info.get('calls', 0) < rate_info.get('limit', 1000)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error checking rate limit: {e}")
            return False
    
    def _update_rate_limit(self, api_name: str):
        """Update rate limit counter"""
        try:
            if api_name in self.rate_limits:
                self.rate_limits[api_name]['calls'] += 1
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error updating rate limit: {e}")
    
    def get_ip_info(self, ip_address: str) -> Optional[IPInfo]:
        """Get stored information about IP"""
        return self.ip_database.get(ip_address)
    
    def get_all_tracked_ips(self) -> List[IPInfo]:
        """Get all tracked IPs"""
        return list(self.ip_database.values())
    
    def get_malicious_ips(self) -> List[IPInfo]:
        """Get all malicious IPs"""
        return [ip_info for ip_info in self.ip_database.values() if ip_info.is_malicious]
    
    def get_country_statistics(self) -> Dict[str, int]:
        """Get statistics by country"""
        country_stats = {}
        for ip_info in self.ip_database.values():
            country = ip_info.country or "Unknown"
            country_stats[country] = country_stats.get(country, 0) + 1
        return country_stats
    
    def add_malicious_ip(self, ip_address: str):
        """Add IP to malicious list"""
        try:
            self.malicious_ips.add(ip_address)
            if ip_address in self.ip_database:
                self.ip_database[ip_address].is_malicious = True
                self.ip_database[ip_address].threat_level = "high"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error adding malicious IP: {e}")
    
    def remove_malicious_ip(self, ip_address: str):
        """Remove IP from malicious list"""
        try:
            self.malicious_ips.discard(ip_address)
            if ip_address in self.ip_database:
                self.ip_database[ip_address].is_malicious = False
                self.ip_database[ip_address].threat_level = "low"
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error removing malicious IP: {e}")
    
    def clear_cache(self):
        """Clear IP cache"""
        self.ip_cache.clear()
    
    def export_data(self) -> Dict[str, Any]:
        """Export tracked IP data"""
        return {
            'tracked_ips': [asdict(ip_info) for ip_info in self.ip_database.values()],
            'malicious_ips': list(self.malicious_ips),
            'proxy_ips': list(self.proxy_ips),
            'vpn_ips': list(self.vpn_ips),
            'tor_ips': list(self.tor_ips)
        }
