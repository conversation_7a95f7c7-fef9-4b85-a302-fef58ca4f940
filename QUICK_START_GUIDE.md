# 🚀 دليل البدء السريع - Cyber Shield Pro

## 📋 المتطلبات الأساسية

### 1. تثبيت Python
**يجب تثبيت Python 3.8 أو أحدث**

#### تحميل Python:
1. اذه<PERSON> إلى: https://www.python.org/downloads/
2. انقر على "Download Python 3.11.x"
3. شغل ملف التثبيت

#### أثناء التثبيت:
- ✅ **مهم جداً**: تأكد من تفعيل "Add Python to PATH"
- اختر "Install Now"
- انتظر حتى اكتمال التثبيت

#### التحقق من التثبيت:
```cmd
python --version
```
يجب أن ترى: `Python 3.11.x`

---

## 🎯 طرق تشغيل البرنامج

### الطريقة الأولى: التشغيل التلقائي (الأسهل)
1. انقر نقراً مزدوجاً على `START_CYBER_SHIELD.bat`
2. سيقوم البرنامج بتثبيت المكتبات المطلوبة تلقائياً
3. سيبدأ تشغيل البرنامج

### الطريقة الثانية: PowerShell (للمستخدمين المتقدمين)
1. انقر بالزر الأيمن على `setup_and_run.ps1`
2. اختر "Run with PowerShell"
3. أو شغل PowerShell كمسؤول واكتب:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
.\setup_and_run.ps1
```

### الطريقة الثالثة: يدوياً
```cmd
# تثبيت المكتبات
pip install -r requirements.txt

# تشغيل البرنامج
python main.py
```

---

## 🔐 معلومات تسجيل الدخول

### حساب المسؤول الافتراضي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: غير كلمة المرور فوراً بعد أول تسجيل دخول!

### إنشاء حساب جديد:
1. في شاشة تسجيل الدخول، انقر "إنشاء حساب جديد"
2. أدخل البيانات المطلوبة
3. اختر كلمة مرور قوية (8 أحرف على الأقل + أرقام + رموز)

---

## 🛠️ استكشاف الأخطاء

### خطأ: "python is not recognized"
**الحل**:
1. تأكد من تثبيت Python بشكل صحيح
2. أعد تشغيل الكمبيوتر
3. أضف Python إلى PATH يدوياً:
   - ابحث عن "Environment Variables"
   - أضف مسار Python (مثل: `C:\Python311\`)

### خطأ: "No module named 'customtkinter'"
**الحل**:
```cmd
pip install customtkinter
```

### خطأ: "Permission denied"
**الحل**:
1. شغل Command Prompt كمسؤول
2. أو استخدم: `pip install --user package_name`

### البرنامج لا يفتح
**الحل**:
1. تحقق من برنامج مكافحة الفيروسات (قد يحجب البرنامج)
2. شغل البرنامج كمسؤول
3. تحقق من ملفات السجلات في مجلد `logs/`

---

## 📁 هيكل المشروع

```
Cyber Shield Pro/
├── main.py                 # الملف الرئيسي
├── requirements.txt        # المكتبات المطلوبة
├── START_CYBER_SHIELD.bat  # ملف التشغيل السريع
├── setup_and_run.ps1      # ملف PowerShell للتثبيت
├── test_core.py           # اختبار المكونات الأساسية
├── src/                   # الكود المصدري
│   ├── ui/               # واجهة المستخدم
│   ├── auth/             # نظام المصادقة
│   ├── security/         # محرك الأمان
│   ├── database/         # قاعدة البيانات
│   ├── network/          # مراقبة الشبكة
│   └── utils/            # أدوات مساعدة
├── config/               # ملفات التكوين
├── database/             # قاعدة البيانات
├── logs/                 # ملفات السجلات
├── temp/                 # ملفات مؤقتة
└── assets/               # الموارد (صور، أيقونات)
```

---

## 🎮 الاستخدام الأساسي

### 1. تسجيل الدخول
- أدخل اسم المستخدم وكلمة المرور
- انقر "تسجيل الدخول"

### 2. لوحة التحكم الرئيسية
- **فحص سريع**: فحص المجلدات عالية الخطورة
- **فحص شامل**: فحص كامل للنظام
- **الحماية المباشرة**: مراقبة مستمرة
- **جدار الحماية**: حماية الشبكة

### 3. إدارة التهديدات
- عرض التهديدات المكتشفة
- خيارات: عزل، حذف، تجاهل
- عرض تفاصيل التهديد

### 4. السجلات والتقارير
- عرض سجل النشاط
- إنشاء تقارير PDF
- إحصائيات الأمان

---

## 🔧 الإعدادات المتقدمة

### تغيير اللغة:
1. اذهب إلى الإعدادات
2. اختر اللغة (عربي/إنجليزي)
3. أعد تشغيل البرنامج

### تفعيل التحقق الثنائي:
1. اذهب إلى إعدادات الحساب
2. انقر "تفعيل التحقق الثنائي"
3. امسح رمز QR بتطبيق المصادقة

### وضع الألعاب:
- يعطل الإشعارات أثناء الألعاب
- يقلل استخدام الموارد

---

## 📞 الدعم والمساعدة

### معلومات الاتصال:
- **البريد الإلكتروني**: <EMAIL>
- **واتساب**: +970599123456
- **الموقع**: https://cybershield.ps

### الإبلاغ عن الأخطاء:
1. اذهب إلى قائمة "المساعدة"
2. انقر "الإبلاغ عن خطأ"
3. أرفق ملفات السجلات من مجلد `logs/`

---

## 🔄 التحديثات

### التحقق من التحديثات:
- يتم التحقق تلقائياً كل 24 ساعة
- أو انقر "تحديث" في لوحة التحكم

### تحديث قاعدة بيانات التهديدات:
- يتم التحديث تلقائياً كل 4 ساعات
- يتطلب اتصال بالإنترنت

---

## ⚡ نصائح للأداء الأمثل

1. **شغل البرنامج كمسؤول** للحصول على أفضل حماية
2. **أضف استثناءات في برنامج مكافحة الفيروسات** لتجنب التداخل
3. **أعد تشغيل النظام** بعد التثبيت الأول
4. **حافظ على تحديث البرنامج** للحصول على أحدث الحماية
5. **استخدم كلمات مرور قوية** لحسابات المستخدمين

---

**Cyber Shield Pro © 2025**  
**تم التطوير في فلسطين 🇵🇸**  
**جميع الحقوق محفوظة**
