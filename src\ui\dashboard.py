#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dashboard for Cyber Shield Pro
Main application dashboard with security overview and controls
"""

import tkinter as tk
import customtkinter as ctk
from tkinter import messagebox
import threading
from typing import Callable, Dict, Any
from .cyber_theme import CyberTheme
from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from ..database.db_manager import DatabaseManager

class Dashboard:
    """Main application dashboard"""
    
    def __init__(self, parent: ctk.CTk, theme: CyberTheme, current_user: Dict[str, Any],
                 app_name: str, version: str, on_logout: Callable,
                 logger: Logger, config_manager: Config<PERSON>anager, db_manager: DatabaseManager):
        """Initialize dashboard"""
        self.parent = parent
        self.theme = theme
        self.current_user = current_user
        self.app_name = app_name
        self.version = version
        self.on_logout = on_logout
        self.logger = logger
        self.config_manager = config_manager
        self.db_manager = db_manager
        
        # Dashboard state
        self.protection_status = "protected"  # protected, at_risk, scanning
        self.last_scan = "Never"
        self.threats_found = 0
        
        # Create UI
        self._create_ui()
        
        # Start status updates
        self._start_status_updates()
    
    def _create_ui(self):
        """Create the dashboard UI"""
        # Main container
        self.main_frame = ctk.CTkFrame(
            self.parent,
            **self.theme.get_frame_style('primary')
        )
        self.main_frame.pack(fill="both", expand=True)
        
        # Create header
        self._create_header()
        
        # Create sidebar
        self._create_sidebar()
        
        # Create main content area
        self._create_main_content()
        
        # Create footer
        self._create_footer()
    
    def _create_header(self):
        """Create header with title and user info"""
        self.header_frame = ctk.CTkFrame(
            self.main_frame,
            height=80,
            **self.theme.get_frame_style('secondary')
        )
        self.header_frame.pack(fill="x", padx=10, pady=(10, 0))
        self.header_frame.pack_propagate(False)
        
        # Left side - App title
        self.title_frame = ctk.CTkFrame(
            self.header_frame,
            fg_color="transparent"
        )
        self.title_frame.pack(side="left", fill="y", padx=20)
        
        self.app_title = ctk.CTkLabel(
            self.title_frame,
            text=self.app_name,
            **self.theme.get_label_style('title')
        )
        self.app_title.pack(anchor="w")
        
        self.app_subtitle = ctk.CTkLabel(
            self.title_frame,
            text="درع الحماية السيبرانية المحترف",
            **self.theme.get_label_style('secondary')
        )
        self.app_subtitle.pack(anchor="w")
        
        # Right side - User info and controls
        self.user_frame = ctk.CTkFrame(
            self.header_frame,
            fg_color="transparent"
        )
        self.user_frame.pack(side="right", fill="y", padx=20)
        
        # User welcome
        self.welcome_label = ctk.CTkLabel(
            self.user_frame,
            text=f"مرحباً، {self.current_user['username']}",
            **self.theme.get_label_style('primary')
        )
        self.welcome_label.pack(anchor="e", pady=(10, 0))
        
        # Control buttons
        self.controls_frame = ctk.CTkFrame(
            self.user_frame,
            fg_color="transparent"
        )
        self.controls_frame.pack(anchor="e", pady=(5, 0))
        
        # Settings button
        self.settings_btn = ctk.CTkButton(
            self.controls_frame,
            text="⚙️ الإعدادات",
            width=100,
            height=30,
            command=self._show_settings,
            **self.theme.get_button_style('secondary')
        )
        self.settings_btn.pack(side="right", padx=(5, 0))
        
        # Logout button
        self.logout_btn = ctk.CTkButton(
            self.controls_frame,
            text="🚪 خروج",
            width=80,
            height=30,
            command=self._handle_logout,
            **self.theme.get_button_style('danger')
        )
        self.logout_btn.pack(side="right")
    
    def _create_sidebar(self):
        """Create navigation sidebar"""
        self.sidebar_frame = ctk.CTkFrame(
            self.main_frame,
            width=250,
            **self.theme.get_frame_style('secondary')
        )
        self.sidebar_frame.pack(side="left", fill="y", padx=(10, 5), pady=10)
        self.sidebar_frame.pack_propagate(False)
        
        # Navigation title
        self.nav_title = ctk.CTkLabel(
            self.sidebar_frame,
            text="🛡️ القائمة الرئيسية",
            **self.theme.get_label_style('heading')
        )
        self.nav_title.pack(pady=(20, 30))
        
        # Navigation buttons
        nav_items = [
            ("🏠 لوحة التحكم", self._show_dashboard),
            ("🔍 فحص الجهاز", self._show_scan),
            ("🛡️ الحماية المباشرة", self._show_realtime),
            ("🔥 جدار الحماية", self._show_firewall),
            ("🌐 مراقب الشبكة", self._show_network),
            ("📍 تتبع IP", self._show_ip_tracker),
            ("📊 السجلات", self._show_logs),
            ("📈 التقارير", self._show_reports),
        ]
        
        if self.current_user.get('is_admin'):
            nav_items.append(("👨‍💼 لوحة المسؤول", self._show_admin))
        
        nav_items.extend([
            ("❓ المساعدة", self._show_help),
            ("ℹ️ حول البرنامج", self._show_about),
        ])
        
        self.nav_buttons = []
        for text, command in nav_items:
            btn = ctk.CTkButton(
                self.sidebar_frame,
                text=text,
                width=220,
                height=40,
                command=command,
                anchor="w",
                **self.theme.get_button_style('secondary')
            )
            btn.pack(pady=5, padx=15)
            self.nav_buttons.append(btn)
        
        # Highlight first button (Dashboard)
        if self.nav_buttons:
            self.nav_buttons[0].configure(**self.theme.get_button_style('primary'))
    
    def _create_main_content(self):
        """Create main content area"""
        self.content_frame = ctk.CTkFrame(
            self.main_frame,
            **self.theme.get_frame_style('primary')
        )
        self.content_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)
        
        # Create dashboard content by default
        self._create_dashboard_content()
    
    def _create_dashboard_content(self):
        """Create dashboard overview content"""
        # Clear content
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        # Status overview
        self._create_status_overview()
        
        # Quick actions
        self._create_quick_actions()
        
        # Recent activity
        self._create_recent_activity()
    
    def _create_status_overview(self):
        """Create system status overview"""
        status_frame = ctk.CTkFrame(
            self.content_frame,
            **self.theme.get_frame_style('secondary')
        )
        status_frame.pack(fill="x", padx=20, pady=(20, 10))
        
        # Status title
        status_title = ctk.CTkLabel(
            status_frame,
            text="🛡️ حالة الحماية",
            **self.theme.get_label_style('heading')
        )
        status_title.pack(pady=(15, 10))
        
        # Status indicators
        indicators_frame = ctk.CTkFrame(
            status_frame,
            fg_color="transparent"
        )
        indicators_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Protection status
        protection_frame = ctk.CTkFrame(indicators_frame, **self.theme.get_frame_style('accent'))
        protection_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))
        
        protection_icon = ctk.CTkLabel(
            protection_frame,
            text="🛡️",
            font=("Arial", 32)
        )
        protection_icon.pack(pady=(10, 5))
        
        self.protection_label = ctk.CTkLabel(
            protection_frame,
            text="محمي",
            **self.theme.get_label_style('success')
        )
        self.protection_label.pack()
        
        protection_desc = ctk.CTkLabel(
            protection_frame,
            text="الحماية المباشرة مفعلة",
            **self.theme.get_label_style('secondary')
        )
        protection_desc.pack(pady=(0, 10))
        
        # Scan status
        scan_frame = ctk.CTkFrame(indicators_frame, **self.theme.get_frame_style('accent'))
        scan_frame.pack(side="right", fill="both", expand=True, padx=(10, 0))
        
        scan_icon = ctk.CTkLabel(
            scan_frame,
            text="🔍",
            font=("Arial", 32)
        )
        scan_icon.pack(pady=(10, 5))
        
        self.scan_label = ctk.CTkLabel(
            scan_frame,
            text="آخر فحص",
            **self.theme.get_label_style('primary')
        )
        self.scan_label.pack()
        
        self.last_scan_label = ctk.CTkLabel(
            scan_frame,
            text=self.last_scan,
            **self.theme.get_label_style('secondary')
        )
        self.last_scan_label.pack(pady=(0, 10))
    
    def _create_quick_actions(self):
        """Create quick action buttons"""
        actions_frame = ctk.CTkFrame(
            self.content_frame,
            **self.theme.get_frame_style('secondary')
        )
        actions_frame.pack(fill="x", padx=20, pady=10)
        
        # Actions title
        actions_title = ctk.CTkLabel(
            actions_frame,
            text="⚡ إجراءات سريعة",
            **self.theme.get_label_style('heading')
        )
        actions_title.pack(pady=(15, 10))
        
        # Action buttons
        buttons_frame = ctk.CTkFrame(
            actions_frame,
            fg_color="transparent"
        )
        buttons_frame.pack(fill="x", padx=20, pady=(0, 15))
        
        # Quick scan button
        quick_scan_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 فحص سريع",
            width=150,
            height=50,
            command=self._start_quick_scan,
            **self.theme.get_button_style('primary')
        )
        quick_scan_btn.pack(side="left", padx=(0, 10))
        
        # Full scan button
        full_scan_btn = ctk.CTkButton(
            buttons_frame,
            text="🔍 فحص شامل",
            width=150,
            height=50,
            command=self._start_full_scan,
            **self.theme.get_button_style('secondary')
        )
        full_scan_btn.pack(side="left", padx=10)
        
        # Update button
        update_btn = ctk.CTkButton(
            buttons_frame,
            text="🔄 تحديث",
            width=150,
            height=50,
            command=self._check_updates,
            **self.theme.get_button_style('warning')
        )
        update_btn.pack(side="right")
    
    def _create_recent_activity(self):
        """Create recent activity section"""
        activity_frame = ctk.CTkFrame(
            self.content_frame,
            **self.theme.get_frame_style('secondary')
        )
        activity_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # Activity title
        activity_title = ctk.CTkLabel(
            activity_frame,
            text="📋 النشاط الأخير",
            **self.theme.get_label_style('heading')
        )
        activity_title.pack(pady=(15, 10))
        
        # Activity list (scrollable)
        activity_scroll = ctk.CTkScrollableFrame(
            activity_frame,
            **self.theme.get_frame_style('primary')
        )
        activity_scroll.pack(fill="both", expand=True, padx=20, pady=(0, 15))
        
        # Sample activity items
        activities = [
            ("🛡️", "تم تفعيل الحماية المباشرة", "منذ دقيقتين"),
            ("🔍", "اكتمل الفحص السريع - لم يتم العثور على تهديدات", "منذ 15 دقيقة"),
            ("🌐", "تم حظر محاولة اتصال مشبوهة", "منذ ساعة"),
            ("🔄", "تم تحديث قاعدة بيانات التهديدات", "منذ 3 ساعات"),
            ("👤", f"تم تسجيل دخول المستخدم {self.current_user['username']}", "اليوم"),
        ]
        
        for icon, message, time in activities:
            item_frame = ctk.CTkFrame(
                activity_scroll,
                **self.theme.get_frame_style('accent')
            )
            item_frame.pack(fill="x", pady=5)
            
            # Icon
            icon_label = ctk.CTkLabel(
                item_frame,
                text=icon,
                font=("Arial", 16)
            )
            icon_label.pack(side="left", padx=(15, 10), pady=10)
            
            # Message
            message_label = ctk.CTkLabel(
                item_frame,
                text=message,
                **self.theme.get_label_style('primary')
            )
            message_label.pack(side="left", pady=10)
            
            # Time
            time_label = ctk.CTkLabel(
                item_frame,
                text=time,
                **self.theme.get_label_style('secondary')
            )
            time_label.pack(side="right", padx=(10, 15), pady=10)
    
    def _create_footer(self):
        """Create footer with status information"""
        self.footer_frame = ctk.CTkFrame(
            self.main_frame,
            height=40,
            **self.theme.get_frame_style('secondary')
        )
        self.footer_frame.pack(side="bottom", fill="x", padx=10, pady=(0, 10))
        self.footer_frame.pack_propagate(False)
        
        # Status indicators
        self.status_label = ctk.CTkLabel(
            self.footer_frame,
            text="🟢 متصل | 🛡️ محمي | 🔄 محدث",
            **self.theme.get_label_style('success')
        )
        self.status_label.pack(side="left", padx=20, pady=10)
        
        # Version info
        version_label = ctk.CTkLabel(
            self.footer_frame,
            text=f"الإصدار {self.version}",
            **self.theme.get_label_style('secondary')
        )
        version_label.pack(side="right", padx=20, pady=10)
    
    def _start_status_updates(self):
        """Start periodic status updates"""
        def update_status():
            try:
                # Update protection status
                # This would check actual protection status
                pass
            except Exception as e:
                self.logger.error(f"Error updating status: {e}")
            
            # Schedule next update
            self.parent.after(5000, update_status)  # Update every 5 seconds
        
        # Start updates
        self.parent.after(1000, update_status)
    
    # Navigation handlers
    def _show_dashboard(self):
        """Show dashboard content"""
        self._reset_nav_buttons()
        self.nav_buttons[0].configure(**self.theme.get_button_style('primary'))
        self._create_dashboard_content()
    
    def _show_scan(self):
        """Show scan interface"""
        self._reset_nav_buttons()
        self.nav_buttons[1].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة الفحص قيد التطوير")
    
    def _show_realtime(self):
        """Show real-time protection"""
        self._reset_nav_buttons()
        self.nav_buttons[2].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة الحماية المباشرة قيد التطوير")
    
    def _show_firewall(self):
        """Show firewall interface"""
        self._reset_nav_buttons()
        self.nav_buttons[3].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة جدار الحماية قيد التطوير")
    
    def _show_network(self):
        """Show network monitor"""
        self._reset_nav_buttons()
        self.nav_buttons[4].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة مراقب الشبكة قيد التطوير")
    
    def _show_ip_tracker(self):
        """Show IP tracker"""
        self._reset_nav_buttons()
        self.nav_buttons[5].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة تتبع IP قيد التطوير")
    
    def _show_logs(self):
        """Show logs interface"""
        self._reset_nav_buttons()
        self.nav_buttons[6].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة السجلات قيد التطوير")
    
    def _show_reports(self):
        """Show reports interface"""
        self._reset_nav_buttons()
        self.nav_buttons[7].configure(**self.theme.get_button_style('primary'))
        messagebox.showinfo("قريباً", "واجهة التقارير قيد التطوير")
    
    def _show_admin(self):
        """Show admin panel"""
        if self.current_user.get('is_admin'):
            messagebox.showinfo("قريباً", "لوحة المسؤول قيد التطوير")
    
    def _show_help(self):
        """Show help interface"""
        messagebox.showinfo("المساعدة", "مرحباً بك في درع الحماية السيبرانية المحترف!\n\nللمساعدة والدعم:\n📧 <EMAIL>\n📱 +970599123456")
    
    def _show_about(self):
        """Show about dialog"""
        about_text = f"""
{self.app_name} v{self.version}

درع الحماية السيبرانية المحترف
Advanced Cybersecurity Protection Suite

© 2025 - تم التطوير في فلسطين 🇵🇸
جميع الحقوق محفوظة

للدعم الفني:
📧 <EMAIL>
🌐 https://cybershield.ps
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def _show_settings(self):
        """Show settings dialog"""
        messagebox.showinfo("قريباً", "واجهة الإعدادات قيد التطوير")
    
    def _reset_nav_buttons(self):
        """Reset all navigation buttons to default style"""
        for btn in self.nav_buttons:
            btn.configure(**self.theme.get_button_style('secondary'))
    
    # Action handlers
    def _start_quick_scan(self):
        """Start quick scan"""
        messagebox.showinfo("فحص سريع", "بدء الفحص السريع...")
        # This would start actual scanning
    
    def _start_full_scan(self):
        """Start full scan"""
        messagebox.showinfo("فحص شامل", "بدء الفحص الشامل...")
        # This would start actual scanning
    
    def _check_updates(self):
        """Check for updates"""
        messagebox.showinfo("التحديثات", "فحص التحديثات...")
        # This would check for actual updates
    
    def _handle_logout(self):
        """Handle logout"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج من البرنامج؟")
        if result:
            self.on_logout()
