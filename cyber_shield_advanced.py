#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cyber Shield Pro - Advanced Cybersecurity Suite
Main application entry point with comprehensive security monitoring
"""

import asyncio
import signal
import sys
import os
import argparse
import json
from pathlib import Path
from datetime import datetime
import threading
import time

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Core imports
from src.utils.logger import Logger
from src.utils.config_manager import ConfigManager
from src.database.database_manager import DatabaseManager
from src.database.log_manager import LogManager
from src.database.threat_manager import ThreatManager

# Security modules
from src.security.threat_detector import ThreatDetector
from src.security.malware_scanner import MalwareScanner
from src.security.firewall_manager import FirewallManager
from src.security.intrusion_detector import IntrusionDetector

# Network modules
from src.network.network_monitor import NetworkMonitor
from src.network.packet_analyzer import PacketAnalyzer
from src.network.connection_tracker import ConnectionTracker

# System modules
from src.system.process_monitor import ProcessMonitor
from src.system.performance_monitor import PerformanceMonitor
from src.system.service_manager import ServiceManager
from src.system.registry_manager import RegistryManager
from src.system.startup_manager import StartupManager

# Reports and dashboard
from src.reports.report_generator import ReportGenerator
from src.dashboard.admin_dashboard import AdminDashboard
from src.notifications.notification_manager import NotificationManager

class CyberShieldAdvanced:
    """Advanced Cyber Shield Pro Application"""
    
    def __init__(self):
        """Initialize Cyber Shield Pro"""
        self.version = "1.0.0"
        self.name = "Cyber Shield Pro Advanced"
        
        # Application state
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Core components
        self.logger = None
        self.config_manager = None
        self.database_manager = None
        self.log_manager = None
        self.threat_manager = None
        
        # Security components
        self.threat_detector = None
        self.malware_scanner = None
        self.firewall_manager = None
        self.intrusion_detector = None
        
        # Network components
        self.network_monitor = None
        self.packet_analyzer = None
        self.connection_tracker = None
        
        # System components
        self.process_monitor = None
        self.performance_monitor = None
        self.service_manager = None
        self.registry_manager = None
        self.startup_manager = None
        
        # Dashboard and notifications
        self.admin_dashboard = None
        self.notification_manager = None
        self.report_generator = None
        
        # Background tasks
        self.background_tasks = []
    
    async def initialize(self, config_path: str = None):
        """Initialize all components"""
        try:
            print(f"🛡️  Initializing {self.name} v{self.version}")
            
            # Initialize core components
            await self._initialize_core_components(config_path)
            
            # Initialize security components
            await self._initialize_security_components()
            
            # Initialize network components
            await self._initialize_network_components()
            
            # Initialize system components
            await self._initialize_system_components()
            
            # Initialize dashboard and notifications
            await self._initialize_dashboard_components()
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            self.logger.info(f"{self.name} v{self.version} initialized successfully")
            print(f"✅ {self.name} initialized successfully")
            
        except Exception as e:
            print(f"❌ Failed to initialize {self.name}: {e}")
            raise
    
    async def _initialize_core_components(self, config_path: str = None):
        """Initialize core components"""
        # Configuration manager
        self.config_manager = ConfigManager()
        if config_path and os.path.exists(config_path):
            self.config_manager.load_config(config_path)
        
        # Logger
        self.logger = Logger(
            log_level=self.config_manager.get('logging.level', 'INFO'),
            log_file=self.config_manager.get('logging.file', 'logs/cybershield_advanced.log'),
            max_file_size=self.config_manager.get('logging.max_file_size', 10485760),
            backup_count=self.config_manager.get('logging.backup_count', 5)
        )
        
        # Database manager
        db_config = self.config_manager.get('database', {})
        self.database_manager = DatabaseManager(
            db_path=db_config.get('path', 'data/cybershield_advanced.db'),
            logger=self.logger
        )
        await self.database_manager.initialize()
        
        # Log manager
        self.log_manager = LogManager(self.database_manager, self.logger)
        
        # Threat manager
        self.threat_manager = ThreatManager(self.database_manager, self.logger)
    
    async def _initialize_security_components(self):
        """Initialize security components"""
        # Threat detector
        self.threat_detector = ThreatDetector(
            self.threat_manager, 
            self.log_manager, 
            self.logger
        )
        
        # Malware scanner
        self.malware_scanner = MalwareScanner(
            self.threat_manager,
            self.logger
        )
        
        # Firewall manager
        self.firewall_manager = FirewallManager(self.logger)
        
        # Intrusion detector
        self.intrusion_detector = IntrusionDetector(
            self.threat_manager,
            self.logger
        )
    
    async def _initialize_network_components(self):
        """Initialize network components"""
        # Network monitor
        self.network_monitor = NetworkMonitor(self.logger)
        
        # Packet analyzer
        self.packet_analyzer = PacketAnalyzer(
            self.threat_manager,
            self.logger
        )
        
        # Connection tracker
        self.connection_tracker = ConnectionTracker(self.logger)
    
    async def _initialize_system_components(self):
        """Initialize system components"""
        # Process monitor
        self.process_monitor = ProcessMonitor(self.logger)
        
        # Performance monitor
        self.performance_monitor = PerformanceMonitor(self.logger)
        
        # Service manager
        self.service_manager = ServiceManager(self.logger)
        
        # Registry manager
        self.registry_manager = RegistryManager(self.logger)
        
        # Startup manager
        self.startup_manager = StartupManager(self.logger)
    
    async def _initialize_dashboard_components(self):
        """Initialize dashboard and notification components"""
        # Notification manager
        self.notification_manager = NotificationManager(self.logger)
        
        # Report generator
        self.report_generator = ReportGenerator(
            self.log_manager,
            self.threat_manager,
            self.logger
        )
        
        # Admin dashboard
        self.admin_dashboard = AdminDashboard(
            self.log_manager,
            self.threat_manager,
            self.report_generator,
            self.logger
        )
        
        # Register system components with dashboard
        self.admin_dashboard.register_system_component('threat_detector', self.threat_detector)
        self.admin_dashboard.register_system_component('network_monitor', self.network_monitor)
        self.admin_dashboard.register_system_component('performance_monitor', self.performance_monitor)
    
    async def start(self):
        """Start all components"""
        try:
            if self.is_running:
                self.logger.warning("Cyber Shield Pro Advanced is already running")
                return
            
            self.is_running = True
            self.logger.info("Starting Cyber Shield Pro Advanced components...")
            
            # Start core components
            await self._start_core_components()
            
            # Start security components
            await self._start_security_components()
            
            # Start network components
            await self._start_network_components()
            
            # Start system components
            await self._start_system_components()
            
            # Start dashboard and notifications
            await self._start_dashboard_components()
            
            # Start background monitoring
            await self._start_background_monitoring()
            
            self.logger.info("All Cyber Shield Pro Advanced components started successfully")
            print(f"🚀 {self.name} is now running and protecting your system")
            
            # Display status
            await self._display_status()
            
        except Exception as e:
            self.logger.error(f"Error starting Cyber Shield Pro Advanced: {e}")
            await self.stop()
            raise
    
    async def _start_core_components(self):
        """Start core components"""
        # Log startup
        await self.log_manager.log_info(
            1, 'system', 'Cyber Shield Pro Advanced starting up',
            {'version': self.version, 'timestamp': datetime.now().isoformat()}
        )
    
    async def _start_security_components(self):
        """Start security components"""
        # Start threat detector
        if self.threat_detector:
            self.threat_detector.start_monitoring()
        
        # Start malware scanner
        if self.malware_scanner:
            self.malware_scanner.start_monitoring()
        
        # Start intrusion detector
        if self.intrusion_detector:
            self.intrusion_detector.start_monitoring()
        
        # Initialize firewall
        if self.firewall_manager:
            self.firewall_manager.initialize_firewall()
    
    async def _start_network_components(self):
        """Start network components"""
        # Start network monitor
        if self.network_monitor:
            self.network_monitor.start_monitoring()
        
        # Start packet analyzer
        if self.packet_analyzer:
            self.packet_analyzer.start_analysis()
        
        # Start connection tracker
        if self.connection_tracker:
            self.connection_tracker.start_tracking()
    
    async def _start_system_components(self):
        """Start system components"""
        # Start process monitor
        if self.process_monitor:
            self.process_monitor.start_monitoring()
        
        # Start performance monitor
        if self.performance_monitor:
            self.performance_monitor.start_monitoring()
        
        # Start service manager
        if self.service_manager:
            self.service_manager.start_monitoring()
        
        # Start registry manager
        if self.registry_manager:
            self.registry_manager.start_monitoring()
    
    async def _start_dashboard_components(self):
        """Start dashboard and notification components"""
        # Start notification manager
        if self.notification_manager:
            await self.notification_manager.start()
        
        # Start admin dashboard in background
        if self.admin_dashboard:
            dashboard_task = asyncio.create_task(self._run_dashboard())
            self.background_tasks.append(dashboard_task)
    
    async def _run_dashboard(self):
        """Run admin dashboard in background"""
        try:
            dashboard_config = self.config_manager.get('dashboard', {})
            host = dashboard_config.get('host', '127.0.0.1')
            port = dashboard_config.get('port', 8080)
            
            # Run dashboard in thread to avoid blocking
            def run_dashboard():
                self.admin_dashboard.start_dashboard(host=host, port=port)
            
            dashboard_thread = threading.Thread(target=run_dashboard, daemon=True)
            dashboard_thread.start()
            
            self.logger.info(f"Admin dashboard started on http://{host}:{port}")
            
        except Exception as e:
            self.logger.error(f"Error starting admin dashboard: {e}")
    
    async def _start_background_monitoring(self):
        """Start background monitoring tasks"""
        # System health monitoring
        health_task = asyncio.create_task(self._system_health_monitor())
        self.background_tasks.append(health_task)
        
        # Threat correlation
        correlation_task = asyncio.create_task(self._threat_correlation_monitor())
        self.background_tasks.append(correlation_task)
    
    async def _system_health_monitor(self):
        """Monitor overall system health"""
        while self.is_running:
            try:
                # Check component health
                health_status = await self._check_component_health()
                
                # Log health status
                await self.log_manager.log_info(
                    1, 'system', 'System health check',
                    health_status
                )
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in system health monitor: {e}")
                await asyncio.sleep(60)
    
    async def _threat_correlation_monitor(self):
        """Monitor and correlate threats"""
        while self.is_running:
            try:
                # Get recent threats
                recent_threats = await self.threat_manager.get_recent_threats(100)
                
                # Simple threat analysis
                if len(recent_threats) > 10:
                    self.logger.info(f"Analyzed {len(recent_threats)} recent threats")
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                self.logger.error(f"Error in threat correlation monitor: {e}")
                await asyncio.sleep(300)
    
    async def _check_component_health(self) -> dict:
        """Check health of all components"""
        health_status = {
            'timestamp': datetime.now().isoformat(),
            'components': {},
            'critical_issues': 0,
            'warnings': 0,
            'overall_status': 'healthy'
        }
        
        try:
            # Check each component
            components = {
                'threat_detector': self.threat_detector,
                'malware_scanner': self.malware_scanner,
                'network_monitor': self.network_monitor,
                'process_monitor': self.process_monitor,
                'performance_monitor': self.performance_monitor
            }
            
            for name, component in components.items():
                if component:
                    status = {
                        'status': 'running' if getattr(component, 'is_monitoring', True) else 'stopped',
                        'last_activity': getattr(component, 'last_activity', datetime.now()).isoformat()
                    }
                    
                    health_status['components'][name] = status
            
        except Exception as e:
            self.logger.error(f"Error checking component health: {e}")
            health_status['overall_status'] = 'error'
            health_status['error'] = str(e)
        
        return health_status
    
    async def _display_status(self):
        """Display current system status"""
        try:
            print("\n" + "="*60)
            print(f"🛡️  {self.name} v{self.version} - System Status")
            print("="*60)
            
            # Component status
            components = [
                ("Threat Detector", self.threat_detector),
                ("Malware Scanner", self.malware_scanner),
                ("Network Monitor", self.network_monitor),
                ("Process Monitor", self.process_monitor),
                ("Performance Monitor", self.performance_monitor),
                ("Admin Dashboard", self.admin_dashboard),
                ("Notification Manager", self.notification_manager)
            ]
            
            for name, component in components:
                status = "🟢 Running" if component and getattr(component, 'is_running', True) else "🔴 Stopped"
                print(f"{name:20} : {status}")
            
            print("="*60)
            
            # Dashboard URL
            if self.admin_dashboard:
                dashboard_url = self.admin_dashboard.get_dashboard_url()
                print(f"📊 Admin Dashboard: {dashboard_url}")
            
            print("="*60)
            print("Press Ctrl+C to stop Cyber Shield Pro Advanced")
            print("="*60)
            
        except Exception as e:
            self.logger.error(f"Error displaying status: {e}")
    
    async def stop(self):
        """Stop all components"""
        try:
            if not self.is_running:
                return
            
            self.logger.info("Stopping Cyber Shield Pro Advanced...")
            print("\n🛑 Stopping Cyber Shield Pro Advanced...")
            
            self.is_running = False
            self.shutdown_event.set()
            
            # Stop background tasks
            for task in self.background_tasks:
                task.cancel()
            
            # Stop components
            await self._stop_components()
            
            # Wait for background tasks to complete
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            
            self.logger.info("Cyber Shield Pro Advanced stopped successfully")
            print("✅ Cyber Shield Pro Advanced stopped successfully")
            
        except Exception as e:
            self.logger.error(f"Error stopping Cyber Shield Pro Advanced: {e}")
            print(f"❌ Error stopping Cyber Shield Pro Advanced: {e}")
    
    async def _stop_components(self):
        """Stop all components"""
        try:
            # Stop dashboard and notifications
            if self.notification_manager:
                await self.notification_manager.stop()
            
            if self.admin_dashboard:
                self.admin_dashboard.stop_dashboard()
            
            # Stop system components
            if self.process_monitor:
                self.process_monitor.stop_monitoring()
            
            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
            
            if self.service_manager:
                self.service_manager.stop_monitoring()
            
            if self.registry_manager:
                self.registry_manager.stop_monitoring()
            
            # Stop network components
            if self.network_monitor:
                self.network_monitor.stop_monitoring()
            
            if self.packet_analyzer:
                self.packet_analyzer.stop_analysis()
            
            if self.connection_tracker:
                self.connection_tracker.stop_tracking()
            
            # Stop security components
            if self.threat_detector:
                self.threat_detector.stop_monitoring()
            
            if self.malware_scanner:
                self.malware_scanner.stop_monitoring()
            
            if self.intrusion_detector:
                self.intrusion_detector.stop_monitoring()
            
            # Close database
            if self.database_manager:
                await self.database_manager.close()
            
        except Exception as e:
            self.logger.error(f"Error stopping components: {e}")
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            print(f"\n🛑 Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def run_forever(self):
        """Run until shutdown signal"""
        try:
            await self.shutdown_event.wait()
        except KeyboardInterrupt:
            await self.stop()


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Cyber Shield Pro Advanced - Comprehensive Cybersecurity Suite')
    parser.add_argument('--config', '-c', help='Configuration file path')
    parser.add_argument('--version', '-v', action='version', version='Cyber Shield Pro Advanced 1.0.0')
    parser.add_argument('--daemon', '-d', action='store_true', help='Run as daemon')
    
    args = parser.parse_args()
    
    # Create application
    app = CyberShieldAdvanced()
    
    try:
        # Initialize
        await app.initialize(args.config)
        
        # Start
        await app.start()
        
        # Run forever
        await app.run_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 Keyboard interrupt received")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
    finally:
        await app.stop()


if __name__ == "__main__":
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('data', exist_ok=True)
    os.makedirs('temp', exist_ok=True)
    os.makedirs('reports', exist_ok=True)
    
    # Run application
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Application error: {e}")
        sys.exit(1)
