#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Performance Monitor for Cyber Shield Pro
Real-time system performance monitoring and analysis
"""

import time
import threading
import psutil
import platform
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from collections import deque
from ..utils.logger import Logger

@dataclass
class PerformanceSnapshot:
    """Performance data snapshot"""
    timestamp: datetime
    cpu_percent: float
    cpu_count: int
    cpu_freq: Optional[Dict[str, float]]
    memory_total: int
    memory_available: int
    memory_percent: float
    memory_used: int
    swap_total: int
    swap_used: int
    swap_percent: float
    disk_io_read: int
    disk_io_write: int
    network_io_sent: int
    network_io_recv: int
    processes_count: int
    threads_count: int
    handles_count: int
    boot_time: datetime
    uptime_seconds: float

class PerformanceMonitor:
    """Real-time performance monitoring"""
    
    def __init__(self, logger: Logger):
        """Initialize performance monitor"""
        self.logger = logger
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Performance data storage
        self.performance_history = deque(maxlen=3600)  # Last hour
        self.minute_averages = deque(maxlen=1440)  # Last 24 hours
        
        # Baseline performance
        self.baseline_cpu = 0.0
        self.baseline_memory = 0.0
        self.baseline_disk_io = 0.0
        self.baseline_network_io = 0.0
        
        # Performance alerts
        self.performance_alerts = []
        self.alert_thresholds = {
            'cpu_high': 80.0,
            'cpu_critical': 95.0,
            'memory_high': 80.0,
            'memory_critical': 95.0,
            'disk_io_high': 100 * 1024 * 1024,  # 100 MB/s
            'network_io_high': 50 * 1024 * 1024  # 50 MB/s
        }
        
        # Last measurements for calculating rates
        self.last_disk_io = None
        self.last_network_io = None
        self.last_measurement_time = None
    
    def start_monitoring(self) -> bool:
        """Start performance monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            
            self.is_monitoring = True
            self.logger.info("Performance monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start performance monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Performance monitoring stopped")
    
    def _monitor_worker(self):
        """Main monitoring worker thread"""
        while self.is_monitoring:
            try:
                # Collect performance snapshot
                snapshot = self._collect_performance_data()
                
                if snapshot:
                    # Add to history
                    self.performance_history.append(snapshot)
                    
                    # Update baselines
                    self._update_baselines(snapshot)
                    
                    # Check for performance issues
                    self._check_performance_alerts(snapshot)
                    
                    # Update minute averages
                    self._update_minute_averages()
                
                time.sleep(1)  # Collect data every second
                
            except Exception as e:
                self.logger.error(f"Error in performance monitoring: {e}")
                time.sleep(5)
    
    def _collect_performance_data(self) -> Optional[PerformanceSnapshot]:
        """Collect current performance data"""
        try:
            current_time = datetime.now()
            
            # CPU information
            cpu_percent = psutil.cpu_percent(interval=None)
            cpu_count = psutil.cpu_count()
            
            try:
                cpu_freq = psutil.cpu_freq()
                cpu_freq_dict = {
                    'current': cpu_freq.current,
                    'min': cpu_freq.min,
                    'max': cpu_freq.max
                } if cpu_freq else None
            except:
                cpu_freq_dict = None
            
            # Memory information
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_rate = 0
            disk_write_rate = 0
            
            if self.last_disk_io and self.last_measurement_time:
                time_delta = (current_time - self.last_measurement_time).total_seconds()
                if time_delta > 0:
                    disk_read_rate = (disk_io.read_bytes - self.last_disk_io.read_bytes) / time_delta
                    disk_write_rate = (disk_io.write_bytes - self.last_disk_io.write_bytes) / time_delta
            
            self.last_disk_io = disk_io
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_rate = 0
            network_recv_rate = 0
            
            if self.last_network_io and self.last_measurement_time:
                time_delta = (current_time - self.last_measurement_time).total_seconds()
                if time_delta > 0:
                    network_sent_rate = (network_io.bytes_sent - self.last_network_io.bytes_sent) / time_delta
                    network_recv_rate = (network_io.bytes_recv - self.last_network_io.bytes_recv) / time_delta
            
            self.last_network_io = network_io
            self.last_measurement_time = current_time
            
            # Process information
            processes = list(psutil.process_iter(['pid']))
            processes_count = len(processes)
            
            # Count threads and handles
            threads_count = 0
            handles_count = 0
            
            for proc in processes[:100]:  # Limit to avoid performance issues
                try:
                    proc_info = proc.as_dict(['num_threads', 'num_handles'])
                    threads_count += proc_info.get('num_threads', 0)
                    handles_count += proc_info.get('num_handles', 0)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Boot time and uptime
            boot_time = datetime.fromtimestamp(psutil.boot_time())
            uptime_seconds = (current_time - boot_time).total_seconds()
            
            return PerformanceSnapshot(
                timestamp=current_time,
                cpu_percent=cpu_percent,
                cpu_count=cpu_count,
                cpu_freq=cpu_freq_dict,
                memory_total=memory.total,
                memory_available=memory.available,
                memory_percent=memory.percent,
                memory_used=memory.used,
                swap_total=swap.total,
                swap_used=swap.used,
                swap_percent=swap.percent,
                disk_io_read=int(disk_read_rate),
                disk_io_write=int(disk_write_rate),
                network_io_sent=int(network_sent_rate),
                network_io_recv=int(network_recv_rate),
                processes_count=processes_count,
                threads_count=threads_count,
                handles_count=handles_count,
                boot_time=boot_time,
                uptime_seconds=uptime_seconds
            )
            
        except Exception as e:
            self.logger.error(f"Error collecting performance data: {e}")
            return None
    
    def _update_baselines(self, snapshot: PerformanceSnapshot):
        """Update performance baselines"""
        try:
            # Simple exponential moving average
            alpha = 0.1
            
            self.baseline_cpu = alpha * snapshot.cpu_percent + (1 - alpha) * self.baseline_cpu
            self.baseline_memory = alpha * snapshot.memory_percent + (1 - alpha) * self.baseline_memory
            
            total_disk_io = snapshot.disk_io_read + snapshot.disk_io_write
            self.baseline_disk_io = alpha * total_disk_io + (1 - alpha) * self.baseline_disk_io
            
            total_network_io = snapshot.network_io_sent + snapshot.network_io_recv
            self.baseline_network_io = alpha * total_network_io + (1 - alpha) * self.baseline_network_io
            
        except Exception as e:
            self.logger.error(f"Error updating baselines: {e}")
    
    def _check_performance_alerts(self, snapshot: PerformanceSnapshot):
        """Check for performance alerts"""
        try:
            alerts = []
            
            # CPU alerts
            if snapshot.cpu_percent > self.alert_thresholds['cpu_critical']:
                alerts.append({
                    'type': 'cpu_critical',
                    'message': f'Critical CPU usage: {snapshot.cpu_percent:.1f}%',
                    'value': snapshot.cpu_percent,
                    'threshold': self.alert_thresholds['cpu_critical']
                })
            elif snapshot.cpu_percent > self.alert_thresholds['cpu_high']:
                alerts.append({
                    'type': 'cpu_high',
                    'message': f'High CPU usage: {snapshot.cpu_percent:.1f}%',
                    'value': snapshot.cpu_percent,
                    'threshold': self.alert_thresholds['cpu_high']
                })
            
            # Memory alerts
            if snapshot.memory_percent > self.alert_thresholds['memory_critical']:
                alerts.append({
                    'type': 'memory_critical',
                    'message': f'Critical memory usage: {snapshot.memory_percent:.1f}%',
                    'value': snapshot.memory_percent,
                    'threshold': self.alert_thresholds['memory_critical']
                })
            elif snapshot.memory_percent > self.alert_thresholds['memory_high']:
                alerts.append({
                    'type': 'memory_high',
                    'message': f'High memory usage: {snapshot.memory_percent:.1f}%',
                    'value': snapshot.memory_percent,
                    'threshold': self.alert_thresholds['memory_high']
                })
            
            # Disk I/O alerts
            total_disk_io = snapshot.disk_io_read + snapshot.disk_io_write
            if total_disk_io > self.alert_thresholds['disk_io_high']:
                alerts.append({
                    'type': 'disk_io_high',
                    'message': f'High disk I/O: {self._format_bytes(total_disk_io)}/s',
                    'value': total_disk_io,
                    'threshold': self.alert_thresholds['disk_io_high']
                })
            
            # Network I/O alerts
            total_network_io = snapshot.network_io_sent + snapshot.network_io_recv
            if total_network_io > self.alert_thresholds['network_io_high']:
                alerts.append({
                    'type': 'network_io_high',
                    'message': f'High network I/O: {self._format_bytes(total_network_io)}/s',
                    'value': total_network_io,
                    'threshold': self.alert_thresholds['network_io_high']
                })
            
            # Add alerts to history
            for alert in alerts:
                alert['timestamp'] = snapshot.timestamp
                self.performance_alerts.append(alert)
                self.logger.warning(f"Performance alert: {alert['message']}")
            
            # Keep only recent alerts
            if len(self.performance_alerts) > 100:
                self.performance_alerts = self.performance_alerts[-100:]
            
        except Exception as e:
            self.logger.error(f"Error checking performance alerts: {e}")
    
    def _update_minute_averages(self):
        """Update minute-level averages"""
        try:
            current_minute = datetime.now().replace(second=0, microsecond=0)
            
            # Get data from last minute
            minute_data = [
                snapshot for snapshot in self.performance_history
                if snapshot.timestamp >= current_minute - timedelta(minutes=1)
                and snapshot.timestamp < current_minute
            ]
            
            if minute_data and (not self.minute_averages or 
                              self.minute_averages[-1]['timestamp'] != current_minute):
                
                # Calculate averages
                avg_cpu = sum(s.cpu_percent for s in minute_data) / len(minute_data)
                avg_memory = sum(s.memory_percent for s in minute_data) / len(minute_data)
                avg_disk_read = sum(s.disk_io_read for s in minute_data) / len(minute_data)
                avg_disk_write = sum(s.disk_io_write for s in minute_data) / len(minute_data)
                avg_network_sent = sum(s.network_io_sent for s in minute_data) / len(minute_data)
                avg_network_recv = sum(s.network_io_recv for s in minute_data) / len(minute_data)
                
                minute_avg = {
                    'timestamp': current_minute,
                    'cpu_percent': avg_cpu,
                    'memory_percent': avg_memory,
                    'disk_read': avg_disk_read,
                    'disk_write': avg_disk_write,
                    'network_sent': avg_network_sent,
                    'network_recv': avg_network_recv
                }
                
                self.minute_averages.append(minute_avg)
            
        except Exception as e:
            self.logger.error(f"Error updating minute averages: {e}")
    
    def _format_bytes(self, bytes_value: float) -> str:
        """Format bytes to human readable string"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} PB"
    
    def get_current_stats(self) -> Optional[Dict[str, Any]]:
        """Get current performance statistics"""
        try:
            if not self.performance_history:
                return None
            
            latest = self.performance_history[-1]
            
            return {
                'timestamp': latest.timestamp.isoformat(),
                'cpu_percent': latest.cpu_percent,
                'memory_percent': latest.memory_percent,
                'memory_used_gb': latest.memory_used / (1024**3),
                'memory_total_gb': latest.memory_total / (1024**3),
                'disk_read_rate': self._format_bytes(latest.disk_io_read) + '/s',
                'disk_write_rate': self._format_bytes(latest.disk_io_write) + '/s',
                'network_sent_rate': self._format_bytes(latest.network_io_sent) + '/s',
                'network_recv_rate': self._format_bytes(latest.network_io_recv) + '/s',
                'processes_count': latest.processes_count,
                'uptime_hours': latest.uptime_seconds / 3600
            }
            
        except Exception as e:
            self.logger.error(f"Error getting current stats: {e}")
            return None
    
    def get_performance_data(self) -> Dict[str, Any]:
        """Get comprehensive performance data"""
        try:
            current_stats = self.get_current_stats()
            
            # Get historical data
            historical_data = [asdict(snapshot) for snapshot in list(self.performance_history)[-60:]]  # Last minute
            
            # Get minute averages
            minute_data = list(self.minute_averages)[-60:]  # Last hour
            
            # Get recent alerts
            recent_alerts = self.performance_alerts[-10:]
            
            return {
                'current': current_stats,
                'historical': historical_data,
                'minute_averages': minute_data,
                'recent_alerts': recent_alerts,
                'baselines': {
                    'cpu': self.baseline_cpu,
                    'memory': self.baseline_memory,
                    'disk_io': self.baseline_disk_io,
                    'network_io': self.baseline_network_io
                },
                'thresholds': self.alert_thresholds
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance data: {e}")
            return {}
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get static system information"""
        try:
            uname = platform.uname()
            
            # CPU info
            cpu_count_logical = psutil.cpu_count(logical=True)
            cpu_count_physical = psutil.cpu_count(logical=False)
            
            try:
                cpu_freq = psutil.cpu_freq()
                cpu_freq_info = {
                    'current': cpu_freq.current,
                    'min': cpu_freq.min,
                    'max': cpu_freq.max
                } if cpu_freq else None
            except:
                cpu_freq_info = None
            
            # Memory info
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # Disk info
            disk_partitions = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_partitions.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    })
                except PermissionError:
                    continue
            
            # Network interfaces
            network_interfaces = []
            for interface, addrs in psutil.net_if_addrs().items():
                interface_info = {'name': interface, 'addresses': []}
                for addr in addrs:
                    interface_info['addresses'].append({
                        'family': addr.family.name,
                        'address': addr.address
                    })
                network_interfaces.append(interface_info)
            
            return {
                'system': {
                    'hostname': uname.node,
                    'os': uname.system,
                    'version': uname.version,
                    'release': uname.release,
                    'architecture': uname.machine,
                    'processor': uname.processor or platform.processor()
                },
                'cpu': {
                    'logical_cores': cpu_count_logical,
                    'physical_cores': cpu_count_physical,
                    'frequency': cpu_freq_info
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'swap_total': swap.total
                },
                'disks': disk_partitions,
                'network': network_interfaces,
                'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {}
    
    def set_alert_threshold(self, threshold_name: str, value: float) -> bool:
        """Set performance alert threshold"""
        try:
            if threshold_name in self.alert_thresholds:
                self.alert_thresholds[threshold_name] = value
                self.logger.info(f"Performance threshold updated: {threshold_name} = {value}")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Error setting threshold: {e}")
            return False
    
    def clear_alerts(self):
        """Clear performance alerts"""
        self.performance_alerts.clear()
    
    def export_performance_data(self) -> Dict[str, Any]:
        """Export performance data for analysis"""
        try:
            return {
                'performance_history': [asdict(snapshot) for snapshot in self.performance_history],
                'minute_averages': list(self.minute_averages),
                'alerts': self.performance_alerts,
                'baselines': {
                    'cpu': self.baseline_cpu,
                    'memory': self.baseline_memory,
                    'disk_io': self.baseline_disk_io,
                    'network_io': self.baseline_network_io
                },
                'thresholds': self.alert_thresholds,
                'export_time': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")
            return {}
