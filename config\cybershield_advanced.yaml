# Cyber Shield Pro Advanced Configuration
# Advanced cybersecurity suite configuration file

# Application Information
application:
  name: "Cyber Shield Pro Advanced"
  version: "1.0.0"
  description: "Advanced Cybersecurity Protection Suite"
  author: "Cyber Shield Team"

# Logging Configuration
logging:
  level: INFO  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  file: logs/cybershield_advanced.log
  max_file_size: 10485760  # 10MB
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  console_output: true
  
  # Separate log files for different components
  security_log: logs/cybershield_security.log
  network_log: logs/cybershield_network.log
  system_log: logs/cybershield_system.log
  error_log: logs/cybershield_errors.log

# Database Configuration
database:
  type: sqlite  # sqlite, postgresql, mysql
  path: data/cybershield_advanced.db
  backup_enabled: true
  backup_interval: 3600  # 1 hour in seconds
  backup_retention: 30  # days
  encryption_enabled: true
  
  # For PostgreSQL/MySQL (if used)
  host: localhost
  port: 5432
  username: cybershield
  password: secure_password
  database_name: cybershield_advanced

# Dashboard Configuration
dashboard:
  enabled: true
  host: 127.0.0.1
  port: 8080
  ssl_enabled: false
  ssl_cert_path: certs/dashboard.crt
  ssl_key_path: certs/dashboard.key
  session_timeout: 3600  # 1 hour
  max_sessions: 10
  auto_refresh_interval: 5  # seconds
  
  # Authentication
  authentication:
    enabled: true
    method: local  # local, ldap, oauth
    password_policy:
      min_length: 8
      require_uppercase: true
      require_lowercase: true
      require_numbers: true
      require_special: true
  
  # Theme and UI
  theme:
    name: cyber_dark
    language: en  # en, ar, es, fr, de
    timezone: UTC

# Security Configuration
security:
  # Threat Detection
  threat_detection:
    enabled: true
    sensitivity: medium  # low, medium, high, paranoid
    real_time_scanning: true
    scan_interval: 60  # seconds
    quarantine_enabled: true
    quarantine_path: quarantine/
    
    # Detection methods
    signature_based: true
    behavioral_analysis: true
    heuristic_analysis: true
    machine_learning: false  # Requires additional setup
    
    # File types to monitor
    monitored_extensions:
      - .exe
      - .dll
      - .bat
      - .cmd
      - .ps1
      - .vbs
      - .js
      - .jar
      - .scr
      - .com
    
    # Excluded paths
    excluded_paths:
      - C:\Windows\System32\
      - C:\Program Files\
      - C:\Program Files (x86)\
  
  # Malware Scanner
  malware_scanner:
    enabled: true
    scan_interval: 3600  # 1 hour
    full_scan_interval: 86400  # 24 hours
    scan_on_access: true
    scan_archives: true
    scan_email: true
    max_file_size: 104857600  # 100MB
    
    # Scan locations
    scan_paths:
      - C:\Users\<USER>\Temp\
      - C:\Windows\Temp\
      - "%USERPROFILE%\Downloads\"
      - "%USERPROFILE%\Desktop\"
    
    # Actions on detection
    on_detection:
      action: quarantine  # quarantine, delete, alert
      backup_before_action: true
      notify_user: true
  
  # Firewall Management
  firewall:
    enabled: true
    default_policy: block  # allow, block
    log_blocked: true
    log_allowed: false
    
    # Predefined rules
    rules:
      - name: "Allow HTTP"
        action: allow
        protocol: tcp
        port: 80
        direction: outbound
      
      - name: "Allow HTTPS"
        action: allow
        protocol: tcp
        port: 443
        direction: outbound
      
      - name: "Block Suspicious Ports"
        action: block
        protocol: tcp
        ports: [1337, 31337, 12345]
        direction: inbound
  
  # Intrusion Detection
  intrusion_detection:
    enabled: true
    sensitivity: medium
    monitor_network: true
    monitor_system: true
    
    # Detection rules
    rules:
      - name: "Port Scan Detection"
        type: network
        pattern: "multiple_connections"
        threshold: 10
        timeframe: 60
      
      - name: "Brute Force Detection"
        type: authentication
        pattern: "failed_logins"
        threshold: 5
        timeframe: 300

# Network Configuration
network:
  monitoring_enabled: true
  packet_analysis: true
  connection_tracking: true
  bandwidth_monitoring: true
  
  # Network interfaces to monitor
  interfaces:
    - all  # Monitor all interfaces
  
  # Packet capture
  packet_capture:
    enabled: true
    buffer_size: 1048576  # 1MB
    max_packets: 10000
    capture_filter: ""  # BPF filter
  
  # Connection tracking
  connection_tracking:
    track_all: true
    log_connections: true
    geo_lookup: true
    dns_resolution: true
  
  # Bandwidth monitoring
  bandwidth:
    alert_threshold: 80  # percentage
    monitor_interval: 30  # seconds
    log_usage: true

# System Monitoring Configuration
system:
  # Process Monitoring
  process_monitoring:
    enabled: true
    monitor_new_processes: true
    monitor_process_changes: true
    whitelist_system_processes: true
    
    # Suspicious process detection
    suspicious_patterns:
      - "powershell -enc"
      - "cmd /c echo"
      - "rundll32 javascript:"
      - "regsvr32 /s /u /i:"
  
  # Performance Monitoring
  performance_monitoring:
    enabled: true
    monitor_interval: 30  # seconds
    
    # Thresholds for alerts
    thresholds:
      cpu_usage: 90  # percentage
      memory_usage: 90  # percentage
      disk_usage: 90  # percentage
      disk_io: 1000  # MB/s
      network_io: 100  # MB/s
  
  # Service Monitoring
  service_monitoring:
    enabled: true
    monitor_critical_services: true
    
    # Critical services to monitor
    critical_services:
      - "Windows Security Center"
      - "Windows Defender Antivirus Service"
      - "Windows Firewall"
      - "Windows Update"
  
  # Registry Monitoring
  registry_monitoring:
    enabled: true
    monitor_startup_keys: true
    monitor_security_keys: true
    
    # Registry keys to monitor
    monitored_keys:
      - "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"
      - "HKCU\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run"
      - "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies"
  
  # Startup Program Management
  startup_management:
    enabled: true
    auto_analyze: true
    block_suspicious: false  # Set to true for automatic blocking

# Notification Configuration
notifications:
  # Email Notifications
  email:
    enabled: true
    smtp_server: smtp.gmail.com
    smtp_port: 587
    use_tls: true
    username: <EMAIL>
    password: your_app_password
    from_name: "Cyber Shield Pro"
    from_email: <EMAIL>
    
    # Recipients
    recipients:
      admin: <EMAIL>
      security: <EMAIL>
      emergency: <EMAIL>
  
  # SMS Notifications
  sms:
    enabled: false
    provider: twilio  # twilio, aws_sns
    account_sid: your_account_sid
    auth_token: your_auth_token
    from_number: "+**********"
    
    # Recipients
    recipients:
      admin: "+**********"
      security: "+**********"
  
  # Desktop Notifications
  desktop:
    enabled: true
    show_critical: true
    show_high: true
    show_medium: false
    show_low: false
    notification_timeout: 10  # seconds
  
  # Webhook Notifications
  webhook:
    enabled: false
    endpoints:
      slack:
        url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
        format: slack
        enabled: false
      
      discord:
        url: "https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK"
        format: discord
        enabled: false
      
      teams:
        url: "https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK"
        format: teams
        enabled: false
  
  # Notification Rules
  rules:
    critical_security:
      priority: critical
      categories: [security, threat, malware]
      channels: [email, sms, desktop]
      escalation_delay: 300  # 5 minutes
      max_escalations: 3
    
    system_performance:
      priority: medium
      categories: [performance, system]
      channels: [email, dashboard]
      quiet_hours:
        start: "22:00"
        end: "06:00"

# Reporting Configuration
reporting:
  enabled: true
  output_directory: reports/
  
  # Report types
  types:
    security_summary:
      enabled: true
      schedule: daily
      format: [pdf, html]
    
    threat_analysis:
      enabled: true
      schedule: weekly
      format: [pdf]
    
    system_performance:
      enabled: true
      schedule: weekly
      format: [pdf, csv]
    
    network_activity:
      enabled: true
      schedule: daily
      format: [html, json]
  
  # Report settings
  settings:
    include_charts: true
    include_raw_data: false
    compress_reports: true
    retention_days: 90

# Advanced Features
advanced:
  # Machine Learning
  machine_learning:
    enabled: false
    model_path: models/
    training_enabled: false
    update_models: false
  
  # API Configuration
  api:
    enabled: true
    host: 127.0.0.1
    port: 8081
    authentication_required: true
    rate_limiting: true
    max_requests_per_minute: 100
  
  # Integration
  integration:
    siem_export: false
    syslog_export: false
    json_export: true
    
    # External tools
    external_tools:
      virustotal_api: ""
      shodan_api: ""
      abuse_ipdb_api: ""

# Performance Tuning
performance:
  # Resource limits
  max_cpu_usage: 25  # percentage
  max_memory_usage: 512  # MB
  max_disk_io: 100  # MB/s
  
  # Threading
  max_worker_threads: 10
  background_task_interval: 60  # seconds
  
  # Caching
  enable_caching: true
  cache_size: 1000  # number of items
  cache_ttl: 3600  # seconds

# Maintenance
maintenance:
  # Automatic cleanup
  auto_cleanup: true
  cleanup_interval: 86400  # 24 hours
  
  # Log rotation
  log_rotation: true
  max_log_age: 30  # days
  
  # Database maintenance
  database_vacuum: true
  vacuum_interval: 604800  # 7 days
  
  # Update checking
  check_updates: true
  update_interval: 86400  # 24 hours
