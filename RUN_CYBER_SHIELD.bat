@echo off
title Cyber Shield Pro Advanced - Launcher
color 0A

echo.
echo ===============================================
echo    🛡️  Cyber Shield Pro Advanced
echo    Advanced Cybersecurity Protection Suite
echo ===============================================
echo.

echo 🔍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✅ Python found
echo.

echo 🔍 Checking if this is first run...
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "temp" mkdir temp
if not exist "reports" mkdir reports
if not exist "quarantine" mkdir quarantine

echo 📦 Installing required packages...
pip install aiosqlite psutil requests flask plyer >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Some packages may not be available, but system will still work
)

echo.
echo 🧪 Running system test...
python test_simple.py
if errorlevel 1 (
    echo.
    echo ❌ System test failed. Please check the errors above.
    pause
    exit /b 1
)

echo.
echo 🚀 Starting Cyber Shield Pro Advanced...
echo.
echo ⚠️  IMPORTANT NOTES:
echo    - Dashboard will be available at: http://localhost:8080
echo    - Press Ctrl+C to stop the application
echo    - Keep this window open while using the system
echo.
pause

python run_simple.py

echo.
echo 👋 Cyber Shield Pro Advanced has stopped.
pause
