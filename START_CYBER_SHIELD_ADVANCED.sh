#!/bin/bash

# Cyber Shield Pro Advanced - Ultimate Quick Start
# This script does everything needed to get you running

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_header() {
    echo
    echo "================================================================"
    echo -e "                ${PURPLE}🛡️  Cyber Shield Pro Advanced 🛡️${NC}"
    echo -e "                    ${CYAN}Ultimate Quick Start Launcher${NC}"
    echo "================================================================"
    echo
}

# Check if Python is installed
check_python() {
    print_info "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
        print_status "Python 3 is installed"
        $PYTHON_CMD --version
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PIP_CMD="pip"
        python_version=$(python --version 2>&1)
        if [[ $python_version == *"Python 3"* ]]; then
            print_status "Python 3 is installed"
            echo $python_version
        else
            print_error "Python 3 is required but Python 2 is installed"
            install_python
            return
        fi
    else
        print_error "Python is not installed"
        install_python
        return
    fi
    
    # Check if pip is available
    if ! command -v $PIP_CMD &> /dev/null; then
        print_error "pip is not installed"
        install_pip
    else
        print_status "pip is available"
    fi
}

# Install Python
install_python() {
    print_info "Installing Python 3..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Detect Linux distribution
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y python3 python3-pip python3-venv python3-dev
        elif command -v yum &> /dev/null; then
            sudo yum install -y python3 python3-pip python3-devel
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y python3 python3-pip python3-devel
        elif command -v pacman &> /dev/null; then
            sudo pacman -S python python-pip
        else
            print_error "Could not detect package manager"
            print_info "Please install Python 3.8+ manually"
            exit 1
        fi
        
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
        
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            brew install python3
            PYTHON_CMD="python3"
            PIP_CMD="pip3"
        else
            print_error "Homebrew not found"
            print_info "Please install Python 3.8+ manually from https://python.org"
            print_info "Or install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
    fi
    
    if command -v $PYTHON_CMD &> /dev/null; then
        print_status "Python 3 installed successfully"
        $PYTHON_CMD --version
    else
        print_error "Failed to install Python 3"
        exit 1
    fi
}

# Check privileges
check_privileges() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ "$EUID" -ne 0 ]; then
            print_warning "Not running as root"
            print_warning "For best performance and full functionality, consider running with sudo"
            echo
            read -p "Continue anyway? (y/n): " choice
            if [[ ! "$choice" =~ ^[Yy]$ ]]; then
                echo "Exiting..."
                exit 1
            fi
        else
            print_status "Running with root privileges"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        print_info "Running on macOS"
        print_warning "Some features may require administrator privileges"
    fi
}

# Setup project structure
setup_project() {
    print_info "Setting up project structure..."
    
    if $PYTHON_CMD setup_project.py; then
        print_status "Project structure setup complete"
    else
        print_error "Project setup failed"
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_info "Installing/updating dependencies..."
    print_warning "This may take several minutes..."
    
    # Upgrade pip first
    $PIP_CMD install --upgrade pip
    
    # Install dependencies
    if $PIP_CMD install -r requirements_advanced.txt; then
        print_status "Dependencies installed successfully"
    else
        print_warning "Some dependencies failed to install"
        print_info "Trying to install essential dependencies only..."
        
        # Install essential dependencies
        $PIP_CMD install aiohttp aiosqlite psutil flask fastapi uvicorn
        
        if [ $? -eq 0 ]; then
            print_status "Essential dependencies installed"
            print_warning "Some optional features may not be available"
        else
            print_error "Failed to install essential dependencies"
            exit 1
        fi
    fi
}

# Run tests
run_tests() {
    print_info "Running system tests..."
    
    if $PYTHON_CMD test_cyber_shield_advanced.py; then
        print_status "All tests passed"
    else
        print_warning "Some tests failed, but continuing anyway..."
        print_info "The application might work with limited functionality"
        sleep 3
    fi
}

# Start application
start_application() {
    print_info "Starting Cyber Shield Pro Advanced..."
    echo
    echo "================================================================"
    echo -e "                        ${YELLOW}IMPORTANT NOTES${NC}"
    echo "================================================================"
    echo
    echo -e "${BLUE}📊${NC} Dashboard will be available at: http://localhost:8080"
    echo -e "${BLUE}👤${NC} Default login credentials:"
    echo "    Username: admin"
    echo "    Password: CyberShield2025!"
    echo
    echo -e "${RED}🔒${NC} SECURITY REMINDER: Change the default password immediately!"
    echo
    echo -e "${YELLOW}🛑${NC} To stop the application, press Ctrl+C in the application window"
    echo
    echo -e "${YELLOW}⚠️${NC}  First run may take longer as the system initializes"
    echo "================================================================"
    echo
    
    # Start the application
    $PYTHON_CMD cyber_shield_advanced.py
    
    echo
    print_info "Cyber Shield Pro Advanced has stopped"
    echo
    echo "Thank you for using Cyber Shield Pro Advanced!"
    echo "For support, check the documentation or visit our GitHub page."
    echo
}

# Main execution
main() {
    print_header
    
    check_privileges
    check_python
    setup_project
    install_dependencies
    run_tests
    start_application
}

# Make sure we're in the right directory
cd "$(dirname "$0")"

# Run main function
main "$@"
