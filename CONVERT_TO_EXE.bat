@echo off
title تحويل Cyber Shield Pro إلى EXE
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██           CYBER SHIELD PRO - EXE CONVERTER                 ██
echo ██              تحويل البرنامج إلى ملف تنفيذي                ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.

echo [1] تحويل باستخدام Batch Script (سريع)
echo [2] تحويل باستخدام PowerShell (متقدم)
echo [3] تحويل باستخدام Python Script (مفصل)
echo [4] عرض متطلبات التحويل
echo [5] خروج
echo.

set /p choice="اختر الطريقة (1-5): "

if "%choice%"=="1" goto :batch_build
if "%choice%"=="2" goto :powershell_build
if "%choice%"=="3" goto :python_build
if "%choice%"=="4" goto :show_requirements
if "%choice%"=="5" goto :exit
goto :invalid_choice

:batch_build
echo.
echo [INFO] بدء التحويل باستخدام Batch Script...
echo.
call BUILD_EXE.bat
goto :end

:powershell_build
echo.
echo [INFO] بدء التحويل باستخدام PowerShell...
echo.
powershell -ExecutionPolicy Bypass -File "Build-EXE.ps1"
goto :end

:python_build
echo.
echo [INFO] بدء التحويل باستخدام Python Script...
echo.
python build_executable.py
if %errorlevel% neq 0 (
    py build_executable.py
)
goto :end

:show_requirements
echo.
echo ═══════════════════════════════════════════════════════════════
echo                        متطلبات التحويل
echo ═══════════════════════════════════════════════════════════════
echo.
echo ✅ المتطلبات الأساسية:
echo    • Python 3.8 أو أحدث
echo    • PyInstaller (سيتم تثبيته تلقائياً)
echo    • جميع مكتبات Python المطلوبة
echo.
echo 📁 الملفات المطلوبة:
echo    • main.py (الملف الرئيسي)
echo    • src/ (مجلد الكود المصدري)
echo    • config/ (ملفات التكوين)
echo    • assets/ (الموارد - اختياري)
echo.
echo 💾 المساحة المطلوبة:
echo    • ~500 MB مساحة فارغة للبناء
echo    • الملف النهائي: ~50-100 MB
echo.
echo ⏱️ الوقت المتوقع:
echo    • 3-10 دقائق حسب سرعة الجهاز
echo.
echo 🎯 النتيجة النهائية:
echo    • CyberShieldPro.exe (ملف تنفيذي مستقل)
echo    • CyberShieldPro_Distribution/ (حزمة التوزيع)
echo.
pause
goto :start

:invalid_choice
echo.
echo [ERROR] اختيار غير صحيح! يرجى اختيار رقم من 1 إلى 5.
echo.
pause
goto :start

:start
cls
goto :main

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo                    تم الانتهاء من العملية
echo ═══════════════════════════════════════════════════════════════
echo.

if exist "dist\CyberShieldPro.exe" (
    echo ✅ تم إنشاء الملف التنفيذي بنجاح!
    echo.
    echo 📁 الملفات الجاهزة:
    echo    • dist\CyberShieldPro.exe
    echo    • CyberShieldPro_Distribution\ (إذا تم إنشاؤها)
    echo.
    echo 🚀 للتشغيل:
    echo    انقر نقراً مزدوجاً على CyberShieldPro.exe
    echo.
    echo 🔐 معلومات الدخول الافتراضية:
    echo    اسم المستخدم: admin
    echo    كلمة المرور: admin123
    echo.
    
    set /p open_folder="هل تريد فتح مجلد الملفات؟ (y/n): "
    if /i "%open_folder%"=="y" (
        if exist "CyberShieldPro_Distribution" (
            explorer "CyberShieldPro_Distribution"
        ) else (
            explorer "dist"
        )
    )
) else (
    echo ❌ لم يتم إنشاء الملف التنفيذي!
    echo.
    echo 🔧 حلول مقترحة:
    echo    1. تأكد من تثبيت Python بشكل صحيح
    echo    2. تأكد من وجود جميع الملفات المطلوبة
    echo    3. شغل الأمر كمسؤول
    echo    4. تحقق من مساحة القرص الصلب
    echo.
)

:exit
echo.
echo شكراً لاستخدام Cyber Shield Pro! 🛡️
echo تم التطوير في فلسطين 🇵🇸
echo.
pause
