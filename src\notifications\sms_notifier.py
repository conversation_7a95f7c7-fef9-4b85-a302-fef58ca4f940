#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SMS Notifier for Cyber Shield Pro
SMS notifications for critical security alerts
"""

import requests
from typing import Dict, List, Any
from datetime import datetime
from ..utils.logger import Logger

class SMSNotifier:
    """SMS Notification System"""
    
    def __init__(self, logger: Logger):
        """Initialize SMS notifier"""
        self.logger = logger
        
        # SMS service configuration (using Twilio as example)
        self.sms_config = {
            'service': 'twilio',  # or 'aws_sns', 'nexmo', etc.
            'account_sid': 'your_twilio_account_sid',
            'auth_token': 'your_twilio_auth_token',
            'from_number': '+**********',
            'api_url': 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json'
        }
        
        # SMS statistics
        self.stats = {
            'sent': 0,
            'failed': 0,
            'last_sent': None,
            'last_error': None
        }
        
        # Message templates
        self.templates = {
            'critical': "🚨 CRITICAL ALERT: {title} - {message} - Cyber Shield Pro",
            'high': "⚠️ HIGH ALERT: {title} - {message} - Cyber Shield Pro",
            'medium': "📢 ALERT: {title} - {message} - Cyber Shield Pro",
            'low': "ℹ️ INFO: {title} - {message} - Cyber Shield Pro"
        }
    
    async def send_notification(self, notification) -> bool:
        """Send SMS notification"""
        try:
            # Format message
            message_text = self._format_message(notification)
            
            # Send SMS to each recipient
            success_count = 0
            for recipient in notification.recipients:
                if self._is_phone_number(recipient):
                    if await self._send_sms(recipient, message_text):
                        success_count += 1
            
            success = success_count > 0
            
            if success:
                self.stats['sent'] += success_count
                self.stats['last_sent'] = datetime.now()
                self.logger.info(f"SMS notification sent to {success_count} recipients: {notification.title}")
            else:
                self.stats['failed'] += 1
                self.logger.error(f"Failed to send SMS notification: {notification.title}")
            
            return success
            
        except Exception as e:
            self.stats['failed'] += 1
            self.stats['last_error'] = str(e)
            self.logger.error(f"Error sending SMS notification: {e}")
            return False
    
    def _format_message(self, notification) -> str:
        """Format SMS message"""
        try:
            # Get template based on priority
            template = self.templates.get(
                notification.priority.value, 
                self.templates['medium']
            )
            
            # Truncate message for SMS length limits
            title = notification.title[:50] + "..." if len(notification.title) > 50 else notification.title
            message = notification.message[:100] + "..." if len(notification.message) > 100 else notification.message
            
            # Format message
            formatted_message = template.format(
                title=title,
                message=message,
                priority=notification.priority.value.upper(),
                timestamp=notification.timestamp.strftime('%H:%M')
            )
            
            # Ensure message is within SMS limits (160 characters)
            if len(formatted_message) > 160:
                formatted_message = formatted_message[:157] + "..."
            
            return formatted_message
            
        except Exception as e:
            self.logger.error(f"Error formatting SMS message: {e}")
            return f"Cyber Shield Pro Alert: {notification.title}"
    
    async def _send_sms(self, phone_number: str, message: str) -> bool:
        """Send SMS via configured service"""
        try:
            if self.sms_config['service'] == 'twilio':
                return await self._send_via_twilio(phone_number, message)
            elif self.sms_config['service'] == 'aws_sns':
                return await self._send_via_aws_sns(phone_number, message)
            else:
                self.logger.error(f"Unsupported SMS service: {self.sms_config['service']}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending SMS to {phone_number}: {e}")
            return False
    
    async def _send_via_twilio(self, phone_number: str, message: str) -> bool:
        """Send SMS via Twilio"""
        try:
            url = self.sms_config['api_url'].format(
                account_sid=self.sms_config['account_sid']
            )
            
            data = {
                'From': self.sms_config['from_number'],
                'To': phone_number,
                'Body': message
            }
            
            auth = (
                self.sms_config['account_sid'],
                self.sms_config['auth_token']
            )
            
            response = requests.post(url, data=data, auth=auth, timeout=10)
            
            if response.status_code == 201:
                self.logger.info(f"SMS sent successfully to {phone_number}")
                return True
            else:
                self.logger.error(f"Twilio API error: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending SMS via Twilio: {e}")
            return False
    
    async def _send_via_aws_sns(self, phone_number: str, message: str) -> bool:
        """Send SMS via AWS SNS"""
        try:
            # This would use boto3 to send SMS via AWS SNS
            # For now, just simulate
            self.logger.info(f"Would send SMS via AWS SNS to {phone_number}: {message}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending SMS via AWS SNS: {e}")
            return False
    
    def _is_phone_number(self, recipient: str) -> bool:
        """Check if recipient is a phone number"""
        try:
            # Simple phone number validation
            # Remove common formatting characters
            cleaned = recipient.replace('+', '').replace('-', '').replace(' ', '').replace('(', '').replace(')', '')
            
            # Check if it's all digits and reasonable length
            return cleaned.isdigit() and 10 <= len(cleaned) <= 15
            
        except Exception as e:
            self.logger.error(f"Error validating phone number: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get SMS notification statistics"""
        return self.stats.copy()
    
    def test_connection(self) -> bool:
        """Test SMS service connection"""
        try:
            if self.sms_config['service'] == 'twilio':
                # Test Twilio connection
                url = f"https://api.twilio.com/2010-04-01/Accounts/{self.sms_config['account_sid']}.json"
                auth = (self.sms_config['account_sid'], self.sms_config['auth_token'])
                
                response = requests.get(url, auth=auth, timeout=10)
                
                if response.status_code == 200:
                    self.logger.info("SMS service connection test successful")
                    return True
                else:
                    self.logger.error(f"SMS service connection test failed: {response.status_code}")
                    return False
            else:
                self.logger.warning("SMS service connection test not implemented for this provider")
                return True
                
        except Exception as e:
            self.logger.error(f"SMS service connection test failed: {e}")
            return False
