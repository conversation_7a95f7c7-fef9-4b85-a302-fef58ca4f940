#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Admin Dashboard for Cyber Shield Pro
Real-time cybersecurity monitoring and control interface
"""

import os
import json
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import SocketIO, emit, join_room, leave_room
import secrets
from ..utils.logger import Logger
from ..database.log_manager import LogManager
from ..database.threat_manager import ThreatManager
from ..reports.report_generator import ReportGenerator, ReportConfig, ReportType, ReportFormat

@dataclass
class DashboardUser:
    """Dashboard user information"""
    username: str
    role: str  # admin, operator, viewer
    permissions: List[str]
    last_login: datetime
    session_id: str
    is_active: bool

@dataclass
class DashboardAlert:
    """Real-time dashboard alert"""
    id: str
    timestamp: datetime
    severity: str  # critical, warning, info
    category: str
    title: str
    message: str
    source: str
    acknowledged: bool = False
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None

class AdminDashboard:
    """Advanced Cybersecurity Admin Dashboard"""
    
    def __init__(self, log_manager: LogManager, threat_manager: ThreatManager, 
                 report_generator: ReportGenerator, logger: Logger):
        """Initialize admin dashboard"""
        self.log_manager = log_manager
        self.threat_manager = threat_manager
        self.report_generator = report_generator
        self.logger = logger
        
        # Flask app setup
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.secret_key = secrets.token_hex(32)
        
        # SocketIO for real-time updates
        self.socketio = SocketIO(self.app, cors_allowed_origins="*", 
                               async_mode='threading')
        
        # Dashboard state
        self.is_running = False
        self.active_users = {}
        self.dashboard_alerts = []
        self.system_components = {}
        
        # Real-time data
        self.real_time_data = {
            'threats': [],
            'system_stats': {},
            'network_activity': {},
            'performance_metrics': {},
            'alerts': []
        }
        
        # Dashboard configuration
        self.config = {
            'host': '127.0.0.1',
            'port': 8080,
            'debug': False,
            'auto_refresh_interval': 5,  # seconds
            'max_alerts': 100,
            'session_timeout': 3600  # 1 hour
        }
        
        # User roles and permissions
        self.user_roles = {
            'admin': [
                'view_dashboard', 'manage_threats', 'manage_firewall',
                'manage_processes', 'generate_reports', 'manage_users',
                'system_control', 'view_logs', 'acknowledge_alerts'
            ],
            'operator': [
                'view_dashboard', 'manage_threats', 'view_logs',
                'acknowledge_alerts', 'generate_reports'
            ],
            'viewer': [
                'view_dashboard', 'view_logs'
            ]
        }
        
        # Default admin user (should be changed in production)
        self.users = {
            'admin': {
                'password': 'CyberShield2025!',  # Should be hashed
                'role': 'admin',
                'email': '<EMAIL>'
            }
        }
        
        # Setup routes and handlers
        self._setup_routes()
        self._setup_socketio_handlers()
        
        # Background tasks
        self.background_tasks = []
        
    def start_dashboard(self, host: str = None, port: int = None, debug: bool = False):
        """Start the dashboard server"""
        try:
            if self.is_running:
                self.logger.warning("Dashboard is already running")
                return
            
            # Update configuration
            if host:
                self.config['host'] = host
            if port:
                self.config['port'] = port
            if debug is not None:
                self.config['debug'] = debug
            
            # Start background tasks
            self._start_background_tasks()
            
            self.is_running = True
            self.logger.info(f"Starting dashboard on {self.config['host']}:{self.config['port']}")
            
            # Run the dashboard
            self.socketio.run(
                self.app,
                host=self.config['host'],
                port=self.config['port'],
                debug=self.config['debug'],
                allow_unsafe_werkzeug=True
            )
            
        except Exception as e:
            self.logger.error(f"Error starting dashboard: {e}")
            self.is_running = False
            raise
    
    def stop_dashboard(self):
        """Stop the dashboard server"""
        try:
            self.is_running = False
            
            # Stop background tasks
            for task in self.background_tasks:
                if hasattr(task, 'cancel'):
                    task.cancel()
            
            self.logger.info("Dashboard stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping dashboard: {e}")
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            """Main dashboard page"""
            if 'user_id' not in session:
                return redirect(url_for('login'))
            
            user = self.active_users.get(session['user_id'])
            if not user or not user.is_active:
                return redirect(url_for('login'))
            
            return render_template('dashboard.html', 
                                 user=user,
                                 config=self.config)
        
        @self.app.route('/login', methods=['GET', 'POST'])
        def login():
            """User login"""
            if request.method == 'POST':
                username = request.form.get('username')
                password = request.form.get('password')
                
                if self._authenticate_user(username, password):
                    session_id = secrets.token_hex(16)
                    session['user_id'] = session_id
                    
                    user = DashboardUser(
                        username=username,
                        role=self.users[username]['role'],
                        permissions=self.user_roles[self.users[username]['role']],
                        last_login=datetime.now(),
                        session_id=session_id,
                        is_active=True
                    )
                    
                    self.active_users[session_id] = user
                    self.logger.info(f"User {username} logged in")
                    
                    return redirect(url_for('index'))
                else:
                    return render_template('login.html', error='Invalid credentials')
            
            return render_template('login.html')
        
        @self.app.route('/logout')
        def logout():
            """User logout"""
            if 'user_id' in session:
                user_id = session['user_id']
                if user_id in self.active_users:
                    username = self.active_users[user_id].username
                    del self.active_users[user_id]
                    self.logger.info(f"User {username} logged out")
                
                session.clear()
            
            return redirect(url_for('login'))
        
        @self.app.route('/api/dashboard/stats')
        def api_dashboard_stats():
            """Get dashboard statistics"""
            if not self._check_permission('view_dashboard'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                stats = self._get_dashboard_stats()
                return jsonify(stats)
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/threats')
        def api_threats():
            """Get threat data"""
            if not self._check_permission('view_dashboard'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                limit = request.args.get('limit', 50, type=int)
                threats = self.threat_manager.get_recent_threats(limit)
                return jsonify([asdict(threat) for threat in threats])
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/alerts')
        def api_alerts():
            """Get dashboard alerts"""
            if not self._check_permission('view_dashboard'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                return jsonify([asdict(alert) for alert in self.dashboard_alerts])
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/alerts/<alert_id>/acknowledge', methods=['POST'])
        def api_acknowledge_alert(alert_id):
            """Acknowledge an alert"""
            if not self._check_permission('acknowledge_alerts'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                user_id = session.get('user_id')
                user = self.active_users.get(user_id)
                
                for alert in self.dashboard_alerts:
                    if alert.id == alert_id:
                        alert.acknowledged = True
                        alert.acknowledged_by = user.username if user else 'Unknown'
                        alert.acknowledged_at = datetime.now()
                        
                        # Emit update to all clients
                        self.socketio.emit('alert_acknowledged', {
                            'alert_id': alert_id,
                            'acknowledged_by': alert.acknowledged_by
                        })
                        
                        return jsonify({'success': True})
                
                return jsonify({'error': 'Alert not found'}), 404
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/reports/generate', methods=['POST'])
        def api_generate_report():
            """Generate a security report"""
            if not self._check_permission('generate_reports'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                data = request.get_json()
                report_type = ReportType(data.get('type', 'security_summary'))
                report_format = ReportFormat(data.get('format', 'pdf'))
                
                # Date range
                end_date = datetime.now()
                start_date = end_date - timedelta(days=data.get('days', 7))
                
                config = ReportConfig(
                    report_type=report_type,
                    format=report_format,
                    date_range={'start': start_date, 'end': end_date}
                )
                
                # Generate report in background
                def generate_report_task():
                    try:
                        output_path = self.report_generator.generate_report(config)
                        self.socketio.emit('report_generated', {
                            'success': True,
                            'path': output_path,
                            'type': report_type.value
                        })
                    except Exception as e:
                        self.socketio.emit('report_generated', {
                            'success': False,
                            'error': str(e)
                        })
                
                threading.Thread(target=generate_report_task, daemon=True).start()
                
                return jsonify({'success': True, 'message': 'Report generation started'})
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/system/control', methods=['POST'])
        def api_system_control():
            """System control actions"""
            if not self._check_permission('system_control'):
                return jsonify({'error': 'Unauthorized'}), 401
            
            try:
                data = request.get_json()
                action = data.get('action')
                target = data.get('target')
                
                result = self._execute_system_control(action, target, data)
                return jsonify(result)
                
            except Exception as e:
                return jsonify({'error': str(e)}), 500
    
    def _setup_socketio_handlers(self):
        """Setup SocketIO event handlers"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            user_id = session.get('user_id')
            if user_id and user_id in self.active_users:
                join_room('dashboard')
                emit('connected', {'status': 'success'})
                self.logger.info(f"Client connected: {user_id}")
            else:
                emit('connected', {'status': 'unauthorized'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            user_id = session.get('user_id')
            if user_id:
                leave_room('dashboard')
                self.logger.info(f"Client disconnected: {user_id}")
        
        @self.socketio.on('request_update')
        def handle_request_update(data):
            """Handle client request for data update"""
            if not self._check_permission('view_dashboard'):
                emit('error', {'message': 'Unauthorized'})
                return
            
            update_type = data.get('type', 'all')
            
            if update_type in ['all', 'stats']:
                emit('dashboard_stats', self._get_dashboard_stats())
            
            if update_type in ['all', 'threats']:
                threats = self.threat_manager.get_recent_threats(20)
                emit('threats_update', [asdict(threat) for threat in threats])
            
            if update_type in ['all', 'alerts']:
                emit('alerts_update', [asdict(alert) for alert in self.dashboard_alerts])
    
    def _start_background_tasks(self):
        """Start background tasks for real-time updates"""
        
        def real_time_update_task():
            """Background task for real-time updates"""
            while self.is_running:
                try:
                    # Update dashboard data
                    self._update_real_time_data()
                    
                    # Emit updates to connected clients
                    self.socketio.emit('dashboard_stats', self._get_dashboard_stats(), room='dashboard')
                    
                    # Check for new alerts
                    self._check_for_new_alerts()
                    
                    # Sleep for configured interval
                    threading.Event().wait(self.config['auto_refresh_interval'])
                    
                except Exception as e:
                    self.logger.error(f"Error in real-time update task: {e}")
                    threading.Event().wait(10)
        
        # Start background task
        task = threading.Thread(target=real_time_update_task, daemon=True)
        task.start()
        self.background_tasks.append(task)
    
    def _authenticate_user(self, username: str, password: str) -> bool:
        """Authenticate user credentials"""
        try:
            if username in self.users:
                # In production, use proper password hashing
                return self.users[username]['password'] == password
            return False
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return False
    
    def _check_permission(self, permission: str) -> bool:
        """Check if current user has permission"""
        try:
            user_id = session.get('user_id')
            if not user_id or user_id not in self.active_users:
                return False
            
            user = self.active_users[user_id]
            return permission in user.permissions
            
        except Exception as e:
            self.logger.error(f"Permission check error: {e}")
            return False
    
    def _get_dashboard_stats(self) -> Dict[str, Any]:
        """Get comprehensive dashboard statistics"""
        try:
            # Get recent threats
            recent_threats = self.threat_manager.get_recent_threats(100)
            
            # Calculate threat statistics
            total_threats = len(recent_threats)
            critical_threats = len([t for t in recent_threats if t.severity == 'critical'])
            active_threats = len([t for t in recent_threats if t.status == 'active'])
            
            # System statistics (would be provided by system components)
            system_stats = self.system_components.get('system_monitor', {})
            
            # Network statistics
            network_stats = self.system_components.get('network_monitor', {})
            
            # Performance statistics
            performance_stats = self.system_components.get('performance_monitor', {})
            
            return {
                'timestamp': datetime.now().isoformat(),
                'threats': {
                    'total': total_threats,
                    'critical': critical_threats,
                    'active': active_threats,
                    'resolved': total_threats - active_threats
                },
                'system': {
                    'health': system_stats.get('health', 'unknown'),
                    'cpu_usage': system_stats.get('cpu_usage', 0),
                    'memory_usage': system_stats.get('memory_usage', 0),
                    'disk_usage': system_stats.get('disk_usage', 0)
                },
                'network': {
                    'active_connections': network_stats.get('active_connections', 0),
                    'blocked_ips': network_stats.get('blocked_ips', 0),
                    'bandwidth_usage': network_stats.get('bandwidth_usage', 0)
                },
                'alerts': {
                    'total': len(self.dashboard_alerts),
                    'unacknowledged': len([a for a in self.dashboard_alerts if not a.acknowledged]),
                    'critical': len([a for a in self.dashboard_alerts if a.severity == 'critical'])
                },
                'users': {
                    'active': len(self.active_users),
                    'total': len(self.users)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting dashboard stats: {e}")
            return {'error': str(e)}
    
    def _update_real_time_data(self):
        """Update real-time dashboard data"""
        try:
            # This would be called by system components to update their data
            pass
        except Exception as e:
            self.logger.error(f"Error updating real-time data: {e}")
    
    def _check_for_new_alerts(self):
        """Check for new alerts and emit to clients"""
        try:
            # This would check various sources for new alerts
            # For now, just clean up old alerts
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.dashboard_alerts = [
                alert for alert in self.dashboard_alerts
                if alert.timestamp > cutoff_time
            ]
            
            # Limit number of alerts
            if len(self.dashboard_alerts) > self.config['max_alerts']:
                self.dashboard_alerts = self.dashboard_alerts[-self.config['max_alerts']:]
                
        except Exception as e:
            self.logger.error(f"Error checking for new alerts: {e}")
    
    def _execute_system_control(self, action: str, target: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute system control action"""
        try:
            # This would interface with system components to execute actions
            # For security, only allow specific actions
            
            allowed_actions = {
                'restart_service': self._restart_service,
                'block_ip': self._block_ip,
                'terminate_process': self._terminate_process,
                'update_firewall_rule': self._update_firewall_rule
            }
            
            if action not in allowed_actions:
                return {'success': False, 'error': 'Action not allowed'}
            
            # Execute action
            result = allowed_actions[action](target, data)
            
            # Log action
            user_id = session.get('user_id')
            user = self.active_users.get(user_id)
            username = user.username if user else 'Unknown'
            
            self.log_manager.log_info(
                1, 'admin', f'System control action executed: {action} on {target}',
                {'action': action, 'target': target, 'user': username, 'result': result}
            )
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing system control: {e}")
            return {'success': False, 'error': str(e)}
    
    def add_alert(self, severity: str, category: str, title: str, message: str, source: str):
        """Add a new dashboard alert"""
        try:
            alert = DashboardAlert(
                id=secrets.token_hex(8),
                timestamp=datetime.now(),
                severity=severity,
                category=category,
                title=title,
                message=message,
                source=source
            )
            
            self.dashboard_alerts.append(alert)
            
            # Emit to connected clients
            self.socketio.emit('new_alert', asdict(alert), room='dashboard')
            
            self.logger.info(f"Dashboard alert added: {title}")
            
        except Exception as e:
            self.logger.error(f"Error adding dashboard alert: {e}")
    
    def register_system_component(self, name: str, component: Any):
        """Register a system component for dashboard integration"""
        self.system_components[name] = component
        self.logger.info(f"System component registered: {name}")
    
    def get_dashboard_url(self) -> str:
        """Get dashboard URL"""
        return f"http://{self.config['host']}:{self.config['port']}"
    
    # Placeholder methods for system control actions
    def _restart_service(self, service_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        return {'success': True, 'message': f'Service {service_name} restart requested'}
    
    def _block_ip(self, ip_address: str, data: Dict[str, Any]) -> Dict[str, Any]:
        return {'success': True, 'message': f'IP {ip_address} blocked'}
    
    def _terminate_process(self, process_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        return {'success': True, 'message': f'Process {process_id} termination requested'}
    
    def _update_firewall_rule(self, rule_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        return {'success': True, 'message': f'Firewall rule {rule_id} updated'}
