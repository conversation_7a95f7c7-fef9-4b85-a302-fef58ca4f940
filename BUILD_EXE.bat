@echo off
color 0B
title Cyber Shield Pro - EXE Builder

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                 CYBER SHIELD PRO EXE BUILDER                 ║
echo  ║              تحويل البرنامج إلى ملف تنفيذي                  ║
echo  ║                 Developed in Palestine 🇵🇸                   ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
echo [INFO] فحص تثبيت Python...

python --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] تم العثور على Python - استخدام أمر 'python'
    set PYTHON_CMD=python
    goto :check_pyinstaller
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] تم العثور على Python - استخدام أمر 'py'
    set PYTHON_CMD=py
    goto :check_pyinstaller
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] تم العثور على Python - استخدام أمر 'python3'
    set PYTHON_CMD=python3
    goto :check_pyinstaller
)

echo [ERROR] Python غير مثبت!
echo.
echo يرجى تثبيت Python 3.8 أو أحدث من:
echo https://www.python.org/downloads/
echo.
echo مهم: تأكد من تفعيل "Add Python to PATH" أثناء التثبيت!
echo.
echo أو شغل ملف setup_and_run.ps1 كمسؤول للتثبيت التلقائي.
echo.
pause
exit /b 1

:check_pyinstaller
echo [INFO] فحص PyInstaller...

%PYTHON_CMD% -c "import PyInstaller" >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] PyInstaller موجود
    goto :build_exe
)

echo [WARN] PyInstaller غير موجود. جاري التثبيت...
%PYTHON_CMD% -m pip install pyinstaller
if %errorlevel% neq 0 (
    echo [ERROR] فشل في تثبيت PyInstaller!
    echo جرب تشغيل الأمر التالي يدوياً:
    echo %PYTHON_CMD% -m pip install pyinstaller
    pause
    exit /b 1
)

echo [OK] تم تثبيت PyInstaller بنجاح

:build_exe
echo.
echo [INFO] بدء عملية بناء ملف EXE...
echo هذا قد يستغرق عدة دقائق...
echo.

REM Create build directory
if not exist "build_temp" mkdir build_temp

REM Create spec file
echo [INFO] إنشاء ملف التكوين...

(
echo # -*- mode: python ; coding: utf-8 -*-
echo.
echo block_cipher = None
echo.
echo a = Analysis^(
echo     ['main.py'],
echo     pathex=[],
echo     binaries=[],
echo     datas=[
echo         ^('config', 'config'^),
echo         ^('assets', 'assets'^),
echo         ^('src', 'src'^),
echo     ],
echo     hiddenimports=[
echo         'customtkinter',
echo         'PIL',
echo         'PIL._tkinter_finder',
echo         'requests',
echo         'psutil',
echo         'cryptography',
echo         'sqlite3',
echo         'hashlib',
echo         'secrets',
echo         'threading',
echo         'datetime',
echo         'json',
echo         'os',
echo         'sys',
echo         'pathlib',
echo         'typing',
echo         'tkinter',
echo         'tkinter.ttk',
echo         'tkinter.messagebox',
echo         'tkinter.filedialog',
echo     ],
echo     hookspath=[],
echo     hooksconfig={},
echo     runtime_hooks=[],
echo     excludes=[],
echo     win_no_prefer_redirects=False,
echo     win_private_assemblies=False,
echo     cipher=block_cipher,
echo     noarchive=False,
echo ^)
echo.
echo pyz = PYZ^(a.pure, a.zipped_data, cipher=block_cipher^)
echo.
echo exe = EXE^(
echo     pyz,
echo     a.scripts,
echo     a.binaries,
echo     a.zipfiles,
echo     a.datas,
echo     [],
echo     name='CyberShieldPro',
echo     debug=False,
echo     bootloader_ignore_signals=False,
echo     strip=False,
echo     upx=True,
echo     upx_exclude=[],
echo     runtime_tmpdir=None,
echo     console=False,
echo     disable_windowed_traceback=False,
echo     argv_emulation=False,
echo     target_arch=None,
echo     codesign_identity=None,
echo     entitlements_file=None,
echo ^)
) > build_temp\cyber_shield.spec

echo [INFO] بناء الملف التنفيذي...

%PYTHON_CMD% -m PyInstaller --clean build_temp\cyber_shield.spec

if %errorlevel% neq 0 (
    echo [ERROR] فشل في بناء ملف EXE!
    echo تحقق من الأخطاء أعلاه.
    pause
    exit /b 1
)

echo.
echo [SUCCESS] تم بناء ملف EXE بنجاح! 🎉
echo.

REM Check if EXE was created
if exist "dist\CyberShieldPro.exe" (
    echo [INFO] تم إنشاء الملف: dist\CyberShieldPro.exe
    
    REM Get file size
    for %%A in ("dist\CyberShieldPro.exe") do (
        set size=%%~zA
        set /a sizeMB=!size!/1048576
    )
    
    echo [INFO] حجم الملف: !sizeMB! MB تقريباً
    echo.
    
    REM Create distribution package
    echo [INFO] إنشاء حزمة التوزيع...
    
    if exist "CyberShieldPro_Distribution" rmdir /s /q "CyberShieldPro_Distribution"
    mkdir "CyberShieldPro_Distribution"
    
    REM Copy EXE
    copy "dist\CyberShieldPro.exe" "CyberShieldPro_Distribution\"
    
    REM Copy documentation
    if exist "README.md" copy "README.md" "CyberShieldPro_Distribution\"
    if exist "QUICK_START_GUIDE.md" copy "QUICK_START_GUIDE.md" "CyberShieldPro_Distribution\"
    if exist "INSTALLATION_GUIDE.md" copy "INSTALLATION_GUIDE.md" "CyberShieldPro_Distribution\"
    if exist "LICENSE" copy "LICENSE" "CyberShieldPro_Distribution\"
    
    REM Copy config
    if exist "config" xcopy "config" "CyberShieldPro_Distribution\config\" /E /I /Q
    
    REM Create empty directories
    mkdir "CyberShieldPro_Distribution\database" 2>nul
    mkdir "CyberShieldPro_Distribution\logs" 2>nul
    mkdir "CyberShieldPro_Distribution\temp" 2>nul
    mkdir "CyberShieldPro_Distribution\assets" 2>nul
    
    REM Create run batch file for distribution
    (
    echo @echo off
    echo title Cyber Shield Pro
    echo echo Starting Cyber Shield Pro...
    echo echo.
    echo echo Default Login:
    echo echo Username: admin
    echo echo Password: admin123
    echo echo.
    echo echo ⚠️  Change admin password after first login!
    echo echo.
    echo CyberShieldPro.exe
    echo if %%errorlevel%% neq 0 ^(
    echo     echo.
    echo     echo Error starting application!
    echo     pause
    echo ^)
    ) > "CyberShieldPro_Distribution\RUN_CYBER_SHIELD.bat"
    
    echo [SUCCESS] تم إنشاء حزمة التوزيع: CyberShieldPro_Distribution\
    echo.
    echo ┌─────────────────────────────────────────────────────────────┐
    echo │  ✅ تم إنجاز عملية التحويل بنجاح!                          │
    echo │                                                             │
    echo │  📁 الملفات الجاهزة:                                       │
    echo │  • CyberShieldPro.exe - الملف التنفيذي الرئيسي             │
    echo │  • CyberShieldPro_Distribution\ - حزمة التوزيع الكاملة      │
    echo │                                                             │
    echo │  🚀 للتشغيل:                                               │
    echo │  انقر نقراً مزدوجاً على CyberShieldPro.exe                │
    echo │                                                             │
    echo │  📋 معلومات الدخول الافتراضية:                            │
    echo │  اسم المستخدم: admin                                       │
    echo │  كلمة المرور: admin123                                     │
    echo └─────────────────────────────────────────────────────────────┘
    
) else (
    echo [ERROR] لم يتم العثور على ملف EXE في مجلد dist!
    echo تحقق من وجود أخطاء في عملية البناء.
)

echo.
echo [INFO] تنظيف الملفات المؤقتة...

REM Clean up
if exist "build" rmdir /s /q "build" 2>nul
if exist "build_temp" rmdir /s /q "build_temp" 2>nul
if exist "__pycache__" rmdir /s /q "__pycache__" 2>nul

echo [INFO] تم الانتهاء من عملية البناء.
echo.
echo هل تريد فتح مجلد التوزيع؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    if exist "CyberShieldPro_Distribution" (
        explorer "CyberShieldPro_Distribution"
    )
)

echo.
echo شكراً لاستخدام Cyber Shield Pro! 🛡️
pause
