@echo off
color 0A
title Cyber Shield Pro - Advanced Cybersecurity Protection

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    CYBER SHIELD PRO v1.0.0                  ║
echo  ║              Advanced Cybersecurity Protection               ║
echo  ║                 Developed in Palestine 🇵🇸                   ║
echo  ║                                                              ║
echo  ║  Copyright © 2025 - All rights reserved                     ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
echo [INFO] Checking Python installation...

python --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] Python found - using 'python' command
    set PYTHON_CMD=python
    goto :check_deps
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] Python found - using 'py' command
    set PYTHON_CMD=py
    goto :check_deps
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo [OK] Python found - using 'python3' command
    set PYTHON_CMD=python3
    goto :check_deps
)

echo [ERROR] Python is not installed or not in PATH!
echo.
echo Please install Python 3.8 or higher from:
echo https://www.python.org/downloads/
echo.
echo IMPORTANT: Make sure to check "Add Python to PATH" during installation!
echo.
echo Alternative: Run 'setup_and_run.ps1' as Administrator for automatic installation.
echo.
pause
exit /b 1

:check_deps
echo [INFO] Checking dependencies...

REM Check if CustomTkinter is installed
%PYTHON_CMD% -c "import customtkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo [WARN] CustomTkinter not found. Installing dependencies...
    goto :install_deps
)

echo [OK] Dependencies appear to be installed
goto :run_app

:install_deps
echo [INFO] Installing required dependencies...
echo This may take a few minutes...
echo.

%PYTHON_CMD% -m pip install --upgrade pip
%PYTHON_CMD% -m pip install customtkinter==5.2.0
%PYTHON_CMD% -m pip install Pillow==10.0.0
%PYTHON_CMD% -m pip install requests==2.31.0
%PYTHON_CMD% -m pip install psutil==5.9.5
%PYTHON_CMD% -m pip install cryptography==41.0.3
%PYTHON_CMD% -m pip install geocoder==1.38.1
%PYTHON_CMD% -m pip install folium==0.14.0
%PYTHON_CMD% -m pip install reportlab==4.0.4
%PYTHON_CMD% -m pip install pycryptodome==3.18.0
%PYTHON_CMD% -m pip install qrcode==7.4.2
%PYTHON_CMD% -m pip install pyotp==2.9.0
%PYTHON_CMD% -m pip install bcrypt==4.0.1

if %errorlevel% neq 0 (
    echo [ERROR] Failed to install some dependencies!
    echo Try running as Administrator or install manually:
    echo %PYTHON_CMD% -m pip install -r requirements.txt
    echo.
    pause
    exit /b 1
)

echo [OK] Dependencies installed successfully!
echo.

:run_app
echo [INFO] Starting Cyber Shield Pro...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │  Welcome to Cyber Shield Pro!                              │
echo │                                                             │
echo │  Default Admin Account:                                     │
echo │  Username: admin                                            │
echo │  Password: admin123                                         │
echo │                                                             │
echo │  ⚠️  IMPORTANT: Change the admin password after first login! │
echo └─────────────────────────────────────────────────────────────┘
echo.

REM Start the application
%PYTHON_CMD% main.py

if %errorlevel% neq 0 (
    echo.
    echo [ERROR] Application encountered an error!
    echo Check the logs folder for more details.
    echo.
    echo Common solutions:
    echo 1. Run as Administrator
    echo 2. Check antivirus software (may block the application)
    echo 3. Ensure all dependencies are installed
    echo 4. Check Python version (requires 3.8+)
    echo.
    pause
    exit /b 1
)

echo.
echo [INFO] Cyber Shield Pro has been closed.
echo Thank you for using our cybersecurity solution!
echo.
pause
