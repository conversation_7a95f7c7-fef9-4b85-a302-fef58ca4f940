#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process Monitor for Cyber Shield Pro
Monitors running processes for suspicious activities
"""

import psutil
import time
import threading
from typing import Dict, List, Any, Callable, Optional
from .threat_detector import ThreatDetector

class ProcessMonitor:
    """Process monitoring component"""
    
    def __init__(self, threat_detector: ThreatDetector):
        """Initialize process monitor"""
        self.threat_detector = threat_detector
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 5  # seconds
        
        # Process tracking
        self.known_processes = {}
        self.suspicious_processes = {}
        self.process_history = []
        
        # Callbacks
        self.threat_callback = None
        self.process_callback = None
        
        # Suspicious process indicators
        self.suspicious_names = [
            'virus', 'trojan', 'malware', 'hack', 'crack',
            'keygen', 'patch', 'loader', 'inject',
            'backdoor', 'rootkit', 'keylog', 'steal',
            'bitcoin', 'miner', 'ransom', 'encrypt',
            'bot', 'rat', 'spy', 'worm'
        ]
        
        # High CPU/Memory thresholds
        self.cpu_threshold = 80.0  # 80% CPU usage
        self.memory_threshold = 500 * 1024 * 1024  # 500MB memory usage
        
        # Network activity thresholds
        self.network_threshold = 1024 * 1024  # 1MB/s network activity
    
    def start_monitoring(self, threat_callback: Callable = None, 
                        process_callback: Callable = None) -> bool:
        """Start process monitoring"""
        if self.is_monitoring:
            return False
        
        try:
            self.threat_callback = threat_callback
            self.process_callback = process_callback
            self.is_monitoring = True
            
            # Start monitoring thread
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            
            return True
            
        except Exception as e:
            print(f"Error starting process monitoring: {e}")
            self.is_monitoring = False
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop process monitoring"""
        if not self.is_monitoring:
            return False
        
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        return True
    
    def _monitor_worker(self):
        """Worker thread for process monitoring"""
        while self.is_monitoring:
            try:
                # Get current processes
                current_processes = self._get_current_processes()
                
                # Check for new processes
                new_processes = self._detect_new_processes(current_processes)
                
                # Analyze new processes
                for proc_info in new_processes:
                    self._analyze_process(proc_info)
                
                # Monitor existing processes
                self._monitor_existing_processes(current_processes)
                
                # Update known processes
                self.known_processes = current_processes
                
                # Call process callback
                if self.process_callback:
                    self.process_callback(current_processes)
                
                # Sleep until next check
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                print(f"Error in process monitor worker: {e}")
                time.sleep(self.monitor_interval)
    
    def _get_current_processes(self) -> Dict[int, Dict[str, Any]]:
        """Get information about all current processes"""
        processes = {}
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 
                                           'cpu_percent', 'memory_info', 
                                           'create_time', 'status']):
                try:
                    proc_info = proc.info
                    if proc_info['pid'] is not None:
                        # Add additional information
                        proc_info['cpu_usage'] = proc.cpu_percent()
                        proc_info['memory_usage'] = proc_info['memory_info'].rss if proc_info['memory_info'] else 0
                        proc_info['num_threads'] = proc.num_threads()
                        
                        # Get network connections
                        try:
                            connections = proc.connections()
                            proc_info['network_connections'] = len(connections)
                            proc_info['connections'] = connections[:10]  # Limit to first 10
                        except (psutil.AccessDenied, psutil.NoSuchProcess):
                            proc_info['network_connections'] = 0
                            proc_info['connections'] = []
                        
                        processes[proc_info['pid']] = proc_info
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            print(f"Error getting current processes: {e}")
        
        return processes
    
    def _detect_new_processes(self, current_processes: Dict[int, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect newly started processes"""
        new_processes = []
        
        for pid, proc_info in current_processes.items():
            if pid not in self.known_processes:
                new_processes.append(proc_info)
        
        return new_processes
    
    def _analyze_process(self, proc_info: Dict[str, Any]):
        """Analyze a process for suspicious behavior"""
        try:
            pid = proc_info['pid']
            name = proc_info.get('name', '').lower()
            exe_path = proc_info.get('exe', '')
            cmdline = ' '.join(proc_info.get('cmdline', [])).lower()
            
            threat_indicators = []
            threat_score = 0
            
            # Check process name
            for suspicious_name in self.suspicious_names:
                if suspicious_name in name:
                    threat_indicators.append(f"Suspicious process name: {suspicious_name}")
                    threat_score += 3
            
            # Check executable path
            if exe_path:
                # Analyze executable file
                file_analysis = self.threat_detector.analyze_file(exe_path)
                if file_analysis['is_threat']:
                    threat_indicators.append(f"Malicious executable: {file_analysis['threat_name']}")
                    threat_score += 5
                
                # Check for suspicious locations
                suspicious_paths = ['temp', 'tmp', 'appdata\\local\\temp']
                if any(path in exe_path.lower() for path in suspicious_paths):
                    threat_indicators.append("Process running from temporary directory")
                    threat_score += 2
            
            # Check command line arguments
            suspicious_cmdline = [
                'powershell', 'cmd.exe /c', 'wscript', 'cscript',
                'regsvr32', 'rundll32', 'mshta', 'bitsadmin',
                'certutil', 'schtasks', 'at.exe', 'sc.exe'
            ]
            
            for sus_cmd in suspicious_cmdline:
                if sus_cmd in cmdline:
                    threat_indicators.append(f"Suspicious command line: {sus_cmd}")
                    threat_score += 2
            
            # Check for high resource usage
            cpu_usage = proc_info.get('cpu_usage', 0)
            memory_usage = proc_info.get('memory_usage', 0)
            
            if cpu_usage > self.cpu_threshold:
                threat_indicators.append(f"High CPU usage: {cpu_usage:.1f}%")
                threat_score += 1
            
            if memory_usage > self.memory_threshold:
                threat_indicators.append(f"High memory usage: {memory_usage / (1024*1024):.1f}MB")
                threat_score += 1
            
            # Check network activity
            network_connections = proc_info.get('network_connections', 0)
            if network_connections > 10:
                threat_indicators.append(f"Many network connections: {network_connections}")
                threat_score += 2
            
            # Check for suspicious network connections
            connections = proc_info.get('connections', [])
            for conn in connections:
                if hasattr(conn, 'raddr') and conn.raddr:
                    # Check for connections to suspicious ports
                    suspicious_ports = [4444, 5555, 6666, 7777, 8888, 9999]
                    if conn.raddr.port in suspicious_ports:
                        threat_indicators.append(f"Connection to suspicious port: {conn.raddr.port}")
                        threat_score += 3
            
            # Determine if process is suspicious
            if threat_score >= 5:
                self._report_suspicious_process(proc_info, threat_indicators, threat_score)
            
        except Exception as e:
            print(f"Error analyzing process {proc_info.get('pid', 'unknown')}: {e}")
    
    def _monitor_existing_processes(self, current_processes: Dict[int, Dict[str, Any]]):
        """Monitor existing processes for changes in behavior"""
        try:
            for pid, proc_info in current_processes.items():
                if pid in self.known_processes:
                    old_info = self.known_processes[pid]
                    
                    # Check for significant changes in resource usage
                    old_cpu = old_info.get('cpu_usage', 0)
                    new_cpu = proc_info.get('cpu_usage', 0)
                    
                    if new_cpu > self.cpu_threshold and new_cpu > old_cpu * 2:
                        self._report_behavior_change(proc_info, 'Sudden CPU spike')
                    
                    # Check for new network connections
                    old_connections = old_info.get('network_connections', 0)
                    new_connections = proc_info.get('network_connections', 0)
                    
                    if new_connections > old_connections + 5:
                        self._report_behavior_change(proc_info, 'Increased network activity')
                        
        except Exception as e:
            print(f"Error monitoring existing processes: {e}")
    
    def _report_suspicious_process(self, proc_info: Dict[str, Any], 
                                 indicators: List[str], threat_score: int):
        """Report a suspicious process"""
        try:
            pid = proc_info['pid']
            name = proc_info.get('name', 'Unknown')
            
            # Add to suspicious processes
            self.suspicious_processes[pid] = {
                'process_info': proc_info,
                'threat_indicators': indicators,
                'threat_score': threat_score,
                'detected_time': time.time()
            }
            
            # Determine severity
            if threat_score >= 10:
                severity = 'high'
            elif threat_score >= 7:
                severity = 'medium'
            else:
                severity = 'low'
            
            # Call threat callback
            if self.threat_callback:
                threat_data = {
                    'type': 'suspicious_process',
                    'process_name': name,
                    'process_id': pid,
                    'executable_path': proc_info.get('exe', ''),
                    'threat_indicators': indicators,
                    'threat_score': threat_score,
                    'severity': severity
                }
                self.threat_callback(threat_data)
            
            print(f"Suspicious process detected: {name} (PID: {pid}, Score: {threat_score})")
            
        except Exception as e:
            print(f"Error reporting suspicious process: {e}")
    
    def _report_behavior_change(self, proc_info: Dict[str, Any], change_type: str):
        """Report suspicious behavior change in existing process"""
        try:
            if self.threat_callback:
                threat_data = {
                    'type': 'behavior_change',
                    'process_name': proc_info.get('name', 'Unknown'),
                    'process_id': proc_info['pid'],
                    'change_type': change_type,
                    'severity': 'medium'
                }
                self.threat_callback(threat_data)
                
        except Exception as e:
            print(f"Error reporting behavior change: {e}")
    
    def kill_process(self, pid: int) -> bool:
        """Kill a suspicious process"""
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            
            # Wait for process to terminate
            proc.wait(timeout=10)
            
            # Remove from suspicious processes
            if pid in self.suspicious_processes:
                del self.suspicious_processes[pid]
            
            return True
            
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired) as e:
            print(f"Error killing process {pid}: {e}")
            return False
    
    def get_suspicious_processes(self) -> Dict[int, Dict[str, Any]]:
        """Get list of currently suspicious processes"""
        return self.suspicious_processes.copy()
    
    def get_process_statistics(self) -> Dict[str, Any]:
        """Get process monitoring statistics"""
        try:
            total_processes = len(self.known_processes)
            suspicious_count = len(self.suspicious_processes)
            
            # Calculate average resource usage
            total_cpu = sum(proc.get('cpu_usage', 0) for proc in self.known_processes.values())
            total_memory = sum(proc.get('memory_usage', 0) for proc in self.known_processes.values())
            
            avg_cpu = total_cpu / max(total_processes, 1)
            avg_memory = total_memory / max(total_processes, 1)
            
            return {
                'total_processes': total_processes,
                'suspicious_processes': suspicious_count,
                'average_cpu_usage': avg_cpu,
                'average_memory_usage': avg_memory,
                'is_monitoring': self.is_monitoring
            }
            
        except Exception as e:
            print(f"Error getting process statistics: {e}")
            return {}
    
    def clear_suspicious_processes(self):
        """Clear the list of suspicious processes"""
        self.suspicious_processes.clear()
    
    def set_monitoring_interval(self, interval: int):
        """Set the monitoring interval in seconds"""
        if interval > 0:
            self.monitor_interval = interval
