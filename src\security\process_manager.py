#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Process Manager for Cyber Shield Pro
Complete process management with admin privileges and security controls
"""

import os
import sys
import time
import threading
import ctypes
import subprocess
import psutil
import winreg
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
from .threat_detector import ThreatDetector
from ..database.log_manager import LogManager
from ..utils.logger import Logger

class ProcessAction(Enum):
    ALLOW = "allow"
    BLOCK = "block"
    QUARANTINE = "quarantine"
    TERMINATE = "terminate"
    MONITOR = "monitor"

class ProcessPriority(Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    REALTIME = "realtime"

@dataclass
class ProcessInfo:
    """Comprehensive process information"""
    pid: int
    name: str
    exe_path: Optional[str]
    cmdline: List[str]
    username: Optional[str]
    status: str
    create_time: datetime
    cpu_percent: float
    memory_percent: float
    memory_info: Dict[str, int]
    num_threads: int
    num_handles: int
    connections: List[Dict[str, Any]]
    open_files: List[str]
    parent_pid: Optional[int]
    children_pids: List[int]
    priority: str
    nice: int
    is_system: bool
    is_suspicious: bool
    threat_level: str
    digital_signature: Optional[Dict[str, Any]]
    file_version: Optional[str]
    company: Optional[str]
    description: Optional[str]

class ProcessManager:
    """Advanced Process Management System"""
    
    def __init__(self, threat_detector: ThreatDetector, log_manager: LogManager, logger: Logger):
        """Initialize process manager"""
        self.threat_detector = threat_detector
        self.log_manager = log_manager
        self.logger = logger
        
        # Process tracking
        self.tracked_processes = {}
        self.process_history = []
        self.blocked_processes = set()
        self.allowed_processes = set()
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()
        
        # Process rules
        self.process_rules = {}
        self.auto_actions = {
            'malicious': ProcessAction.TERMINATE,
            'suspicious': ProcessAction.MONITOR,
            'unknown': ProcessAction.MONITOR
        }
        
        # Callbacks
        self.callbacks = {}
        
        # System processes (should not be terminated)
        self.system_processes = {
            'system', 'csrss.exe', 'winlogon.exe', 'services.exe',
            'lsass.exe', 'svchost.exe', 'explorer.exe', 'dwm.exe',
            'wininit.exe', 'smss.exe', 'audiodg.exe'
        }
        
        # Performance monitoring
        self.performance_thresholds = {
            'cpu_high': 80.0,  # 80% CPU usage
            'memory_high': 80.0,  # 80% memory usage
            'handles_high': 10000,  # 10k handles
            'threads_high': 1000   # 1k threads
        }
        
        # Load process rules
        self._load_process_rules()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def start_monitoring(self, callbacks: Dict[str, Callable] = None) -> bool:
        """Start process monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.callbacks = callbacks or {}
            
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            
            self.is_monitoring = True
            self.logger.info("Process monitoring started")
            
            if self.callbacks.get('on_monitoring_started'):
                self.callbacks['on_monitoring_started']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start process monitoring: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop process monitoring"""
        try:
            self.is_monitoring = False
            
            if self.monitor_thread:
                self.monitor_thread.join(timeout=10)
            
            self.logger.info("Process monitoring stopped")
            
            if self.callbacks.get('on_monitoring_stopped'):
                self.callbacks['on_monitoring_stopped']()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop process monitoring: {e}")
            return False
    
    def _monitor_worker(self):
        """Main monitoring worker thread"""
        while self.is_monitoring:
            try:
                # Scan all processes
                current_processes = self._scan_processes()
                
                # Check for new processes
                new_processes = self._detect_new_processes(current_processes)
                
                # Analyze new processes
                for proc_info in new_processes:
                    self._analyze_process(proc_info)
                
                # Monitor existing processes
                self._monitor_existing_processes(current_processes)
                
                # Update tracked processes
                self.tracked_processes = {p.pid: p for p in current_processes}
                
                # Clean up old history
                self._cleanup_history()
                
                time.sleep(2)  # Check every 2 seconds
                
            except Exception as e:
                self.logger.error(f"Error in process monitoring: {e}")
                time.sleep(10)
    
    def _scan_processes(self) -> List[ProcessInfo]:
        """Scan all running processes"""
        processes = []
        
        try:
            for proc in psutil.process_iter():
                try:
                    proc_info = self._get_process_info(proc)
                    if proc_info:
                        processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
            
        except Exception as e:
            self.logger.error(f"Error scanning processes: {e}")
        
        return processes
    
    def _get_process_info(self, proc: psutil.Process) -> Optional[ProcessInfo]:
        """Get comprehensive process information"""
        try:
            # Basic process info
            proc_info = proc.as_dict([
                'pid', 'name', 'exe', 'cmdline', 'username', 'status',
                'create_time', 'cpu_percent', 'memory_percent', 'memory_info',
                'num_threads', 'num_handles', 'ppid', 'nice'
            ])
            
            # Get connections
            try:
                connections = [
                    {
                        'local_addr': conn.laddr._asdict() if conn.laddr else None,
                        'remote_addr': conn.raddr._asdict() if conn.raddr else None,
                        'status': conn.status,
                        'type': conn.type.name
                    }
                    for conn in proc.connections()
                ]
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                connections = []
            
            # Get open files
            try:
                open_files = [f.path for f in proc.open_files()]
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                open_files = []
            
            # Get children
            try:
                children_pids = [child.pid for child in proc.children()]
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                children_pids = []
            
            # Determine if system process
            is_system = (
                proc_info['name'].lower() in self.system_processes or
                proc_info['username'] in ['SYSTEM', 'LOCAL SERVICE', 'NETWORK SERVICE'] or
                proc_info['pid'] in [0, 4]  # System Idle Process and System
            )
            
            # Get file information
            digital_signature = None
            file_version = None
            company = None
            description = None
            
            if proc_info['exe']:
                file_info = self._get_file_info(proc_info['exe'])
                digital_signature = file_info.get('digital_signature')
                file_version = file_info.get('file_version')
                company = file_info.get('company')
                description = file_info.get('description')
            
            # Analyze for threats
            is_suspicious, threat_level = self._analyze_process_threat(proc_info, proc)
            
            # Get priority
            try:
                priority_map = {
                    psutil.IDLE_PRIORITY_CLASS: ProcessPriority.LOW.value,
                    psutil.NORMAL_PRIORITY_CLASS: ProcessPriority.NORMAL.value,
                    psutil.HIGH_PRIORITY_CLASS: ProcessPriority.HIGH.value,
                    psutil.REALTIME_PRIORITY_CLASS: ProcessPriority.REALTIME.value
                }
                priority = priority_map.get(proc.nice(), ProcessPriority.NORMAL.value)
            except:
                priority = ProcessPriority.NORMAL.value
            
            return ProcessInfo(
                pid=proc_info['pid'],
                name=proc_info['name'],
                exe_path=proc_info['exe'],
                cmdline=proc_info['cmdline'] or [],
                username=proc_info['username'],
                status=proc_info['status'],
                create_time=datetime.fromtimestamp(proc_info['create_time']),
                cpu_percent=proc_info['cpu_percent'] or 0.0,
                memory_percent=proc_info['memory_percent'] or 0.0,
                memory_info=proc_info['memory_info']._asdict() if proc_info['memory_info'] else {},
                num_threads=proc_info['num_threads'] or 0,
                num_handles=proc_info['num_handles'] or 0,
                connections=connections,
                open_files=open_files,
                parent_pid=proc_info['ppid'],
                children_pids=children_pids,
                priority=priority,
                nice=proc_info['nice'] or 0,
                is_system=is_system,
                is_suspicious=is_suspicious,
                threat_level=threat_level,
                digital_signature=digital_signature,
                file_version=file_version,
                company=company,
                description=description
            )
            
        except Exception as e:
            self.logger.error(f"Error getting process info for PID {proc.pid}: {e}")
            return None
    
    def _analyze_process_threat(self, proc_info: Dict[str, Any], proc: psutil.Process) -> tuple:
        """Analyze process for threats"""
        try:
            is_suspicious = False
            threat_level = "low"
            
            # Check executable file if available
            if proc_info['exe']:
                file_analysis = self.threat_detector.analyze_file(proc_info['exe'])
                if file_analysis['is_threat']:
                    is_suspicious = True
                    threat_level = file_analysis['severity']
            
            # Check process name for suspicious patterns
            name_lower = proc_info['name'].lower()
            suspicious_names = [
                'virus', 'trojan', 'malware', 'hack', 'crack',
                'keygen', 'patch', 'loader', 'inject', 'bot'
            ]
            
            if any(sus_name in name_lower for sus_name in suspicious_names):
                is_suspicious = True
                threat_level = "medium"
            
            # Check command line for suspicious patterns
            cmdline_str = ' '.join(proc_info['cmdline'] or []).lower()
            suspicious_cmdline = [
                'powershell -enc', 'cmd /c echo', 'wscript', 'cscript',
                'regsvr32', 'rundll32', 'mshta', 'bitsadmin'
            ]
            
            if any(sus_cmd in cmdline_str for sus_cmd in suspicious_cmdline):
                is_suspicious = True
                threat_level = max(threat_level, "medium")
            
            # Check for high resource usage
            if (proc_info['cpu_percent'] > self.performance_thresholds['cpu_high'] or
                proc_info['memory_percent'] > self.performance_thresholds['memory_high']):
                if not proc_info['name'].lower() in self.system_processes:
                    threat_level = max(threat_level, "medium")
            
            # Check for excessive handles/threads
            if (proc_info['num_handles'] > self.performance_thresholds['handles_high'] or
                proc_info['num_threads'] > self.performance_thresholds['threads_high']):
                threat_level = max(threat_level, "medium")
            
            return is_suspicious, threat_level
            
        except Exception as e:
            self.logger.error(f"Error analyzing process threat: {e}")
            return False, "low"
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Get file information including digital signature"""
        try:
            file_info = {}
            
            # This would use Windows API to get file version info
            # For now, return basic info
            if os.path.exists(file_path):
                file_info['exists'] = True
                file_info['size'] = os.path.getsize(file_path)
                file_info['modified'] = os.path.getmtime(file_path)
            
            return file_info
            
        except Exception as e:
            self.logger.error(f"Error getting file info for {file_path}: {e}")
            return {}
    
    def _detect_new_processes(self, current_processes: List[ProcessInfo]) -> List[ProcessInfo]:
        """Detect newly started processes"""
        current_pids = {p.pid for p in current_processes}
        tracked_pids = set(self.tracked_processes.keys())
        
        new_pids = current_pids - tracked_pids
        
        return [p for p in current_processes if p.pid in new_pids]
    
    def _analyze_process(self, proc_info: ProcessInfo):
        """Analyze new process and take action"""
        try:
            # Add to history
            self.process_history.append(proc_info)
            
            # Log new process
            self.log_manager.log_info(
                1, 'security', f'New process started: {proc_info.name} (PID: {proc_info.pid})',
                asdict(proc_info)
            )
            
            # Check process rules
            action = self._get_process_action(proc_info)
            
            # Execute action
            if action == ProcessAction.TERMINATE:
                self.terminate_process(proc_info.pid, "Malicious process detected")
            elif action == ProcessAction.BLOCK:
                self.block_process(proc_info.name)
            elif action == ProcessAction.QUARANTINE:
                self.quarantine_process(proc_info.pid)
            
            # Notify callbacks
            if self.callbacks.get('on_new_process'):
                self.callbacks['on_new_process'](proc_info)
            
            if proc_info.is_suspicious and self.callbacks.get('on_suspicious_process'):
                self.callbacks['on_suspicious_process'](proc_info)
            
        except Exception as e:
            self.logger.error(f"Error analyzing process {proc_info.pid}: {e}")
    
    def _get_process_action(self, proc_info: ProcessInfo) -> ProcessAction:
        """Determine action for process based on rules"""
        try:
            # Check specific process rules
            if proc_info.name in self.process_rules:
                return self.process_rules[proc_info.name]
            
            # Check blocked processes
            if proc_info.name in self.blocked_processes:
                return ProcessAction.TERMINATE
            
            # Check allowed processes
            if proc_info.name in self.allowed_processes:
                return ProcessAction.ALLOW
            
            # Auto actions based on threat level
            if proc_info.threat_level == "high":
                return self.auto_actions.get('malicious', ProcessAction.TERMINATE)
            elif proc_info.is_suspicious:
                return self.auto_actions.get('suspicious', ProcessAction.MONITOR)
            else:
                return self.auto_actions.get('unknown', ProcessAction.MONITOR)
            
        except Exception as e:
            self.logger.error(f"Error getting process action: {e}")
            return ProcessAction.MONITOR
    
    def _monitor_existing_processes(self, current_processes: List[ProcessInfo]):
        """Monitor existing processes for changes"""
        try:
            for proc_info in current_processes:
                if proc_info.pid in self.tracked_processes:
                    old_info = self.tracked_processes[proc_info.pid]
                    
                    # Check for significant changes
                    if self._has_significant_changes(old_info, proc_info):
                        self._handle_process_changes(old_info, proc_info)
            
        except Exception as e:
            self.logger.error(f"Error monitoring existing processes: {e}")
    
    def _has_significant_changes(self, old_info: ProcessInfo, new_info: ProcessInfo) -> bool:
        """Check if process has significant changes"""
        try:
            # Check CPU usage spike
            if new_info.cpu_percent > old_info.cpu_percent * 2 and new_info.cpu_percent > 50:
                return True
            
            # Check memory usage spike
            if new_info.memory_percent > old_info.memory_percent * 2 and new_info.memory_percent > 50:
                return True
            
            # Check new network connections
            if len(new_info.connections) > len(old_info.connections) + 5:
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking process changes: {e}")
            return False
    
    def _handle_process_changes(self, old_info: ProcessInfo, new_info: ProcessInfo):
        """Handle significant process changes"""
        try:
            changes = []
            
            if new_info.cpu_percent > old_info.cpu_percent * 2:
                changes.append(f"CPU usage spike: {old_info.cpu_percent:.1f}% -> {new_info.cpu_percent:.1f}%")
            
            if new_info.memory_percent > old_info.memory_percent * 2:
                changes.append(f"Memory usage spike: {old_info.memory_percent:.1f}% -> {new_info.memory_percent:.1f}%")
            
            if len(new_info.connections) > len(old_info.connections) + 5:
                changes.append(f"New connections: {len(old_info.connections)} -> {len(new_info.connections)}")
            
            if changes:
                self.logger.warning(f"Process changes detected for {new_info.name} (PID: {new_info.pid}): {', '.join(changes)}")
                
                if self.callbacks.get('on_process_changes'):
                    self.callbacks['on_process_changes'](old_info, new_info, changes)
            
        except Exception as e:
            self.logger.error(f"Error handling process changes: {e}")
    
    def terminate_process(self, pid: int, reason: str = "User request") -> bool:
        """Terminate a process"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to terminate processes")
                return False
            
            # Check if it's a system process
            if pid in self.tracked_processes:
                proc_info = self.tracked_processes[pid]
                if proc_info.is_system:
                    self.logger.warning(f"Attempted to terminate system process: {proc_info.name}")
                    return False
            
            # Terminate process
            process = psutil.Process(pid)
            process.terminate()
            
            # Wait for termination
            try:
                process.wait(timeout=10)
            except psutil.TimeoutExpired:
                # Force kill if terminate didn't work
                process.kill()
                process.wait(timeout=5)
            
            self.logger.info(f"Process {pid} terminated: {reason}")
            
            # Log action
            self.log_manager.log_warning(
                1, 'security', f'Process terminated: PID {pid}',
                {'pid': pid, 'reason': reason}
            )
            
            return True
            
        except psutil.NoSuchProcess:
            self.logger.info(f"Process {pid} no longer exists")
            return True
        except psutil.AccessDenied:
            self.logger.error(f"Access denied terminating process {pid}")
            return False
        except Exception as e:
            self.logger.error(f"Error terminating process {pid}: {e}")
            return False
    
    def suspend_process(self, pid: int) -> bool:
        """Suspend a process"""
        try:
            if not self.has_admin_rights:
                return False
            
            process = psutil.Process(pid)
            process.suspend()
            
            self.logger.info(f"Process {pid} suspended")
            return True
            
        except Exception as e:
            self.logger.error(f"Error suspending process {pid}: {e}")
            return False
    
    def resume_process(self, pid: int) -> bool:
        """Resume a suspended process"""
        try:
            if not self.has_admin_rights:
                return False
            
            process = psutil.Process(pid)
            process.resume()
            
            self.logger.info(f"Process {pid} resumed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error resuming process {pid}: {e}")
            return False
    
    def set_process_priority(self, pid: int, priority: ProcessPriority) -> bool:
        """Set process priority"""
        try:
            if not self.has_admin_rights:
                return False
            
            priority_map = {
                ProcessPriority.LOW: psutil.IDLE_PRIORITY_CLASS,
                ProcessPriority.NORMAL: psutil.NORMAL_PRIORITY_CLASS,
                ProcessPriority.HIGH: psutil.HIGH_PRIORITY_CLASS,
                ProcessPriority.REALTIME: psutil.REALTIME_PRIORITY_CLASS
            }
            
            process = psutil.Process(pid)
            process.nice(priority_map[priority])
            
            self.logger.info(f"Process {pid} priority set to {priority.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting process priority {pid}: {e}")
            return False
    
    def block_process(self, process_name: str) -> bool:
        """Block a process from running"""
        try:
            self.blocked_processes.add(process_name.lower())
            self.logger.info(f"Process blocked: {process_name}")
            
            # Terminate any running instances
            for proc_info in self.tracked_processes.values():
                if proc_info.name.lower() == process_name.lower():
                    self.terminate_process(proc_info.pid, f"Blocked process: {process_name}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error blocking process {process_name}: {e}")
            return False
    
    def unblock_process(self, process_name: str) -> bool:
        """Unblock a process"""
        try:
            self.blocked_processes.discard(process_name.lower())
            self.logger.info(f"Process unblocked: {process_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error unblocking process {process_name}: {e}")
            return False
    
    def quarantine_process(self, pid: int) -> bool:
        """Quarantine a process (suspend and isolate)"""
        try:
            # Suspend the process
            if not self.suspend_process(pid):
                return False
            
            # Additional quarantine measures could be implemented here
            # such as network isolation, file access restrictions, etc.
            
            self.logger.info(f"Process {pid} quarantined")
            return True
            
        except Exception as e:
            self.logger.error(f"Error quarantining process {pid}: {e}")
            return False
    
    def _load_process_rules(self):
        """Load process rules from configuration"""
        try:
            # This would load from a configuration file
            # For now, set some default rules
            self.process_rules = {
                'notepad.exe': ProcessAction.ALLOW,
                'calc.exe': ProcessAction.ALLOW,
                'malware.exe': ProcessAction.TERMINATE
            }
            
        except Exception as e:
            self.logger.error(f"Error loading process rules: {e}")
    
    def _cleanup_history(self):
        """Clean up old process history"""
        try:
            # Keep only last 1000 entries
            if len(self.process_history) > 1000:
                self.process_history = self.process_history[-1000:]
            
        except Exception as e:
            self.logger.error(f"Error cleaning up history: {e}")
    
    def get_process_list(self) -> List[Dict[str, Any]]:
        """Get list of all tracked processes"""
        return [asdict(proc_info) for proc_info in self.tracked_processes.values()]
    
    def get_process_info(self, pid: int) -> Optional[Dict[str, Any]]:
        """Get information about specific process"""
        proc_info = self.tracked_processes.get(pid)
        return asdict(proc_info) if proc_info else None
    
    def get_suspicious_processes(self) -> List[Dict[str, Any]]:
        """Get list of suspicious processes"""
        return [asdict(proc_info) for proc_info in self.tracked_processes.values() 
                if proc_info.is_suspicious]
    
    def get_high_resource_processes(self) -> List[Dict[str, Any]]:
        """Get processes using high resources"""
        high_resource = []
        for proc_info in self.tracked_processes.values():
            if (proc_info.cpu_percent > self.performance_thresholds['cpu_high'] or
                proc_info.memory_percent > self.performance_thresholds['memory_high']):
                high_resource.append(asdict(proc_info))
        
        return high_resource
    
    def get_process_statistics(self) -> Dict[str, Any]:
        """Get process monitoring statistics"""
        try:
            total_processes = len(self.tracked_processes)
            suspicious_count = sum(1 for p in self.tracked_processes.values() if p.is_suspicious)
            system_count = sum(1 for p in self.tracked_processes.values() if p.is_system)
            
            # Calculate resource usage
            total_cpu = sum(p.cpu_percent for p in self.tracked_processes.values())
            total_memory = sum(p.memory_percent for p in self.tracked_processes.values())
            
            return {
                'total_processes': total_processes,
                'suspicious_processes': suspicious_count,
                'system_processes': system_count,
                'user_processes': total_processes - system_count,
                'blocked_processes': len(self.blocked_processes),
                'total_cpu_usage': total_cpu,
                'total_memory_usage': total_memory,
                'is_monitoring': self.is_monitoring,
                'has_admin_rights': self.has_admin_rights
            }
            
        except Exception as e:
            self.logger.error(f"Error getting process statistics: {e}")
            return {}
