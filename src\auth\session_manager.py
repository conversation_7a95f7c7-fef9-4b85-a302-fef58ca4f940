#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Session Manager for Cyber Shield Pro
Handles user sessions and authentication tokens
"""

import secrets
import datetime
from typing import Optional, Dict, Any
from ..database.db_manager import DatabaseManager

class SessionManager:
    """Session management class"""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize session manager"""
        self.db = db_manager
        self.session_duration = 24 * 60 * 60  # 24 hours in seconds
    
    def create_session(self, user_id: int, ip_address: str = None, 
                      user_agent: str = None) -> Optional[str]:
        """Create a new user session"""
        try:
            # Generate secure session token
            session_token = secrets.token_urlsafe(32)
            
            # Calculate expiration time
            expires_at = datetime.datetime.now() + datetime.timedelta(seconds=self.session_duration)
            
            # Insert session into database
            query = '''
                INSERT INTO sessions (user_id, session_token, ip_address, user_agent, 
                                    created_at, expires_at, is_active)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?, TRUE)
            '''
            
            if self.db.execute_update(query, (user_id, session_token, ip_address, 
                                            user_agent, expires_at.isoformat())):
                return session_token
            
            return None
            
        except Exception as e:
            print(f"Error creating session: {e}")
            return None
    
    def get_session(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Get session by token"""
        try:
            query = "SELECT * FROM sessions WHERE session_token = ? AND is_active = TRUE"
            result = self.db.execute_query(query, (session_token,))
            
            if result:
                return dict(result[0])
            return None
            
        except Exception as e:
            print(f"Error getting session: {e}")
            return None
    
    def is_session_expired(self, session_token: str) -> bool:
        """Check if session is expired"""
        try:
            session = self.get_session(session_token)
            if not session:
                return True
            
            expires_at = datetime.datetime.fromisoformat(session['expires_at'])
            return datetime.datetime.now() > expires_at
            
        except Exception as e:
            print(f"Error checking session expiration: {e}")
            return True
    
    def update_session_activity(self, session_token: str) -> bool:
        """Update session last activity time"""
        try:
            # Extend session expiration
            new_expires_at = datetime.datetime.now() + datetime.timedelta(seconds=self.session_duration)
            
            query = '''
                UPDATE sessions 
                SET expires_at = ?
                WHERE session_token = ? AND is_active = TRUE
            '''
            
            return self.db.execute_update(query, (new_expires_at.isoformat(), session_token))
            
        except Exception as e:
            print(f"Error updating session activity: {e}")
            return False
    
    def invalidate_session(self, session_token: str) -> bool:
        """Invalidate a session"""
        try:
            query = '''
                UPDATE sessions 
                SET is_active = FALSE
                WHERE session_token = ?
            '''
            
            return self.db.execute_update(query, (session_token,))
            
        except Exception as e:
            print(f"Error invalidating session: {e}")
            return False
    
    def invalidate_all_user_sessions(self, user_id: int) -> bool:
        """Invalidate all sessions for a user"""
        try:
            query = '''
                UPDATE sessions 
                SET is_active = FALSE
                WHERE user_id = ?
            '''
            
            return self.db.execute_update(query, (user_id,))
            
        except Exception as e:
            print(f"Error invalidating user sessions: {e}")
            return False
    
    def get_user_sessions(self, user_id: int) -> list:
        """Get all active sessions for a user"""
        try:
            query = '''
                SELECT session_token, ip_address, user_agent, created_at, expires_at
                FROM sessions 
                WHERE user_id = ? AND is_active = TRUE
                ORDER BY created_at DESC
            '''
            
            result = self.db.execute_query(query, (user_id,))
            return [dict(row) for row in result] if result else []
            
        except Exception as e:
            print(f"Error getting user sessions: {e}")
            return []
    
    def cleanup_expired_sessions(self) -> bool:
        """Clean up expired sessions"""
        try:
            query = '''
                UPDATE sessions 
                SET is_active = FALSE
                WHERE expires_at < CURRENT_TIMESTAMP AND is_active = TRUE
            '''
            
            return self.db.execute_update(query)
            
        except Exception as e:
            print(f"Error cleaning up expired sessions: {e}")
            return False
    
    def get_session_statistics(self) -> Dict[str, int]:
        """Get session statistics"""
        try:
            stats = {
                'total_sessions': 0,
                'active_sessions': 0,
                'expired_sessions': 0,
                'unique_users': 0
            }
            
            # Total sessions
            query = "SELECT COUNT(*) as count FROM sessions"
            result = self.db.execute_query(query)
            if result:
                stats['total_sessions'] = result[0]['count']
            
            # Active sessions
            query = "SELECT COUNT(*) as count FROM sessions WHERE is_active = TRUE"
            result = self.db.execute_query(query)
            if result:
                stats['active_sessions'] = result[0]['count']
            
            # Expired sessions
            query = '''
                SELECT COUNT(*) as count FROM sessions 
                WHERE expires_at < CURRENT_TIMESTAMP
            '''
            result = self.db.execute_query(query)
            if result:
                stats['expired_sessions'] = result[0]['count']
            
            # Unique users with active sessions
            query = '''
                SELECT COUNT(DISTINCT user_id) as count FROM sessions 
                WHERE is_active = TRUE
            '''
            result = self.db.execute_query(query)
            if result:
                stats['unique_users'] = result[0]['count']
            
            return stats
            
        except Exception as e:
            print(f"Error getting session statistics: {e}")
            return {}
    
    def is_session_valid(self, session_token: str) -> bool:
        """Check if session is valid and not expired"""
        try:
            session = self.get_session(session_token)
            if not session or not session['is_active']:
                return False
            
            return not self.is_session_expired(session_token)
            
        except Exception as e:
            print(f"Error validating session: {e}")
            return False
