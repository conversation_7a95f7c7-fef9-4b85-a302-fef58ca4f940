#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Service Manager for Cyber Shield Pro
Windows services management with security monitoring
"""

import subprocess
import threading
import time
import ctypes
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from ..utils.logger import Logger

class ServiceState(Enum):
    STOPPED = "stopped"
    RUNNING = "running"
    PAUSED = "paused"
    PENDING = "pending"
    UNKNOWN = "unknown"

class ServiceStartType(Enum):
    AUTOMATIC = "automatic"
    MANUAL = "manual"
    DISABLED = "disabled"
    AUTOMATIC_DELAYED = "automatic_delayed"

@dataclass
class ServiceInfo:
    """Windows service information"""
    name: str
    display_name: str
    state: ServiceState
    start_type: ServiceStartType
    pid: Optional[int]
    description: str
    executable_path: str
    account: str
    dependencies: List[str]
    dependents: List[str]
    is_system_service: bool
    is_suspicious: bool
    threat_level: str
    last_checked: datetime

class ServiceManager:
    """Windows Services Management"""
    
    def __init__(self, logger: Logger):
        """Initialize service manager"""
        self.logger = logger
        
        # Service tracking
        self.services = {}
        self.service_history = []
        
        # Monitoring state
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Admin privileges
        self.has_admin_rights = self._check_admin_privileges()
        
        # Suspicious service patterns
        self.suspicious_patterns = [
            'virus', 'trojan', 'malware', 'hack', 'crack',
            'keygen', 'patch', 'loader', 'inject', 'bot',
            'miner', 'crypto', 'backdoor', 'rootkit'
        ]
        
        # Known system services
        self.system_services = {
            'wuauserv', 'bits', 'cryptsvc', 'msiserver', 'trustedinstaller',
            'spooler', 'themes', 'audiosrv', 'browser', 'dhcp',
            'dnscache', 'eventlog', 'lanmanserver', 'lanmanworkstation',
            'netlogon', 'netman', 'nla', 'policyagent', 'rpcss',
            'samss', 'schedule', 'seclogon', 'sens', 'sharedaccess',
            'shellhwdetection', 'srservice', 'ssdpsrv', 'stisvc',
            'tapisrv', 'termservice', 'upnphost', 'w32time',
            'winmgmt', 'wsearch', 'wuauserv', 'xmlprov'
        }
        
        # Service alerts
        self.service_alerts = []
        
        # Load initial service data
        self._scan_services()
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with administrator privileges"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
    
    def start_monitoring(self) -> bool:
        """Start service monitoring"""
        try:
            if self.is_monitoring:
                return True
            
            self.monitor_thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self.monitor_thread.start()
            
            self.is_monitoring = True
            self.logger.info("Service monitoring started")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start service monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop service monitoring"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("Service monitoring stopped")
    
    def _monitor_worker(self):
        """Service monitoring worker thread"""
        while self.is_monitoring:
            try:
                # Scan services for changes
                self._scan_services()
                
                # Check for suspicious services
                self._check_suspicious_services()
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in service monitoring: {e}")
                time.sleep(60)
    
    def _scan_services(self):
        """Scan all Windows services"""
        try:
            # Use sc query to get service list
            result = subprocess.run(['sc', 'query', 'state=', 'all'], 
                                  capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode != 0:
                self.logger.error(f"Failed to query services: {result.stderr}")
                return
            
            # Parse service list
            services = self._parse_service_list(result.stdout)
            
            # Get detailed info for each service
            for service_name in services:
                service_info = self._get_service_info(service_name)
                if service_info:
                    # Check if this is a new or changed service
                    if service_name not in self.services:
                        self._handle_new_service(service_info)
                    elif self._service_changed(self.services[service_name], service_info):
                        self._handle_service_change(self.services[service_name], service_info)
                    
                    self.services[service_name] = service_info
            
        except Exception as e:
            self.logger.error(f"Error scanning services: {e}")
    
    def _parse_service_list(self, output: str) -> List[str]:
        """Parse sc query output to get service names"""
        services = []
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('SERVICE_NAME:'):
                service_name = line.split(':', 1)[1].strip()
                services.append(service_name)
        
        return services
    
    def _get_service_info(self, service_name: str) -> Optional[ServiceInfo]:
        """Get detailed information about a service"""
        try:
            # Get service configuration
            config_result = subprocess.run(['sc', 'qc', service_name], 
                                         capture_output=True, text=True, encoding='utf-8')
            
            # Get service status
            status_result = subprocess.run(['sc', 'query', service_name], 
                                         capture_output=True, text=True, encoding='utf-8')
            
            if config_result.returncode != 0 or status_result.returncode != 0:
                return None
            
            # Parse configuration
            config_data = self._parse_service_config(config_result.stdout)
            status_data = self._parse_service_status(status_result.stdout)
            
            # Determine if service is suspicious
            is_suspicious, threat_level = self._analyze_service_threat(service_name, config_data)
            
            # Check if it's a system service
            is_system_service = (
                service_name.lower() in self.system_services or
                config_data.get('account', '').lower() in ['localsystem', 'localservice', 'networkservice']
            )
            
            return ServiceInfo(
                name=service_name,
                display_name=config_data.get('display_name', service_name),
                state=self._parse_service_state(status_data.get('state', 'UNKNOWN')),
                start_type=self._parse_start_type(config_data.get('start_type', 'UNKNOWN')),
                pid=status_data.get('pid'),
                description=config_data.get('description', ''),
                executable_path=config_data.get('binary_path', ''),
                account=config_data.get('account', ''),
                dependencies=config_data.get('dependencies', []),
                dependents=config_data.get('dependents', []),
                is_system_service=is_system_service,
                is_suspicious=is_suspicious,
                threat_level=threat_level,
                last_checked=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error getting service info for {service_name}: {e}")
            return None
    
    def _parse_service_config(self, output: str) -> Dict[str, Any]:
        """Parse sc qc output"""
        config = {}
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower().replace(' ', '_')
                value = value.strip()
                
                if key == 'dependencies':
                    config[key] = [dep.strip() for dep in value.split('/') if dep.strip()]
                else:
                    config[key] = value
        
        return config
    
    def _parse_service_status(self, output: str) -> Dict[str, Any]:
        """Parse sc query output"""
        status = {}
        lines = output.split('\n')
        
        for line in lines:
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower().replace(' ', '_')
                value = value.strip()
                
                if key == 'pid':
                    try:
                        status[key] = int(value) if value != '0' else None
                    except ValueError:
                        status[key] = None
                else:
                    status[key] = value
        
        return status
    
    def _parse_service_state(self, state_str: str) -> ServiceState:
        """Parse service state string"""
        state_map = {
            'STOPPED': ServiceState.STOPPED,
            'RUNNING': ServiceState.RUNNING,
            'PAUSED': ServiceState.PAUSED,
            'START_PENDING': ServiceState.PENDING,
            'STOP_PENDING': ServiceState.PENDING,
            'CONTINUE_PENDING': ServiceState.PENDING,
            'PAUSE_PENDING': ServiceState.PENDING
        }
        
        return state_map.get(state_str.upper(), ServiceState.UNKNOWN)
    
    def _parse_start_type(self, start_type_str: str) -> ServiceStartType:
        """Parse service start type string"""
        start_type_map = {
            'AUTO_START': ServiceStartType.AUTOMATIC,
            'DEMAND_START': ServiceStartType.MANUAL,
            'DISABLED': ServiceStartType.DISABLED,
            'DELAYED_AUTO_START': ServiceStartType.AUTOMATIC_DELAYED
        }
        
        return start_type_map.get(start_type_str.upper(), ServiceStartType.MANUAL)
    
    def _analyze_service_threat(self, service_name: str, config_data: Dict[str, Any]) -> tuple:
        """Analyze service for potential threats"""
        try:
            is_suspicious = False
            threat_level = "low"
            
            # Check service name for suspicious patterns
            name_lower = service_name.lower()
            if any(pattern in name_lower for pattern in self.suspicious_patterns):
                is_suspicious = True
                threat_level = "high"
            
            # Check executable path
            exe_path = config_data.get('binary_path', '').lower()
            if exe_path:
                # Check for suspicious locations
                suspicious_paths = [
                    'temp', 'tmp', 'appdata', 'programdata',
                    'users\\public', 'windows\\temp'
                ]
                
                if any(sus_path in exe_path for sus_path in suspicious_paths):
                    is_suspicious = True
                    threat_level = max(threat_level, "medium")
                
                # Check for suspicious executables
                if any(pattern in exe_path for pattern in self.suspicious_patterns):
                    is_suspicious = True
                    threat_level = "high"
            
            # Check service account
            account = config_data.get('account', '').lower()
            if account and account not in ['localsystem', 'localservice', 'networkservice']:
                # Non-standard service account might be suspicious
                threat_level = max(threat_level, "medium")
            
            return is_suspicious, threat_level
            
        except Exception as e:
            self.logger.error(f"Error analyzing service threat: {e}")
            return False, "low"
    
    def _handle_new_service(self, service_info: ServiceInfo):
        """Handle newly discovered service"""
        try:
            self.logger.info(f"New service detected: {service_info.name}")
            
            # Add to history
            self.service_history.append({
                'timestamp': datetime.now(),
                'action': 'service_created',
                'service': asdict(service_info)
            })
            
            # Check if suspicious
            if service_info.is_suspicious:
                self._create_service_alert(
                    'suspicious_service',
                    f'Suspicious service detected: {service_info.name}',
                    service_info
                )
            
        except Exception as e:
            self.logger.error(f"Error handling new service: {e}")
    
    def _handle_service_change(self, old_info: ServiceInfo, new_info: ServiceInfo):
        """Handle service state changes"""
        try:
            changes = []
            
            if old_info.state != new_info.state:
                changes.append(f"State: {old_info.state.value} -> {new_info.state.value}")
            
            if old_info.start_type != new_info.start_type:
                changes.append(f"Start type: {old_info.start_type.value} -> {new_info.start_type.value}")
            
            if changes:
                self.logger.info(f"Service {new_info.name} changed: {', '.join(changes)}")
                
                # Add to history
                self.service_history.append({
                    'timestamp': datetime.now(),
                    'action': 'service_changed',
                    'service': new_info.name,
                    'changes': changes
                })
            
        except Exception as e:
            self.logger.error(f"Error handling service change: {e}")
    
    def _service_changed(self, old_info: ServiceInfo, new_info: ServiceInfo) -> bool:
        """Check if service has significant changes"""
        return (old_info.state != new_info.state or 
                old_info.start_type != new_info.start_type or
                old_info.pid != new_info.pid)
    
    def _check_suspicious_services(self):
        """Check for suspicious service activities"""
        try:
            suspicious_services = [
                service for service in self.services.values()
                if service.is_suspicious and service.state == ServiceState.RUNNING
            ]
            
            for service in suspicious_services:
                self._create_service_alert(
                    'suspicious_running',
                    f'Suspicious service running: {service.name}',
                    service
                )
            
        except Exception as e:
            self.logger.error(f"Error checking suspicious services: {e}")
    
    def _create_service_alert(self, alert_type: str, message: str, service_info: ServiceInfo):
        """Create service alert"""
        try:
            alert = {
                'timestamp': datetime.now(),
                'type': alert_type,
                'message': message,
                'service': asdict(service_info)
            }
            
            self.service_alerts.append(alert)
            
            # Keep only last 50 alerts
            if len(self.service_alerts) > 50:
                self.service_alerts = self.service_alerts[-50:]
            
            self.logger.warning(f"Service alert: {message}")
            
        except Exception as e:
            self.logger.error(f"Error creating service alert: {e}")
    
    def start_service(self, service_name: str) -> bool:
        """Start a Windows service"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to start services")
                return False
            
            result = subprocess.run(['sc', 'start', service_name], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Service started: {service_name}")
                return True
            else:
                self.logger.error(f"Failed to start service {service_name}: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error starting service {service_name}: {e}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """Stop a Windows service"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to stop services")
                return False
            
            result = subprocess.run(['sc', 'stop', service_name], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Service stopped: {service_name}")
                return True
            else:
                self.logger.error(f"Failed to stop service {service_name}: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error stopping service {service_name}: {e}")
            return False
    
    def disable_service(self, service_name: str) -> bool:
        """Disable a Windows service"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to disable services")
                return False
            
            result = subprocess.run(['sc', 'config', service_name, 'start=', 'disabled'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Service disabled: {service_name}")
                return True
            else:
                self.logger.error(f"Failed to disable service {service_name}: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error disabling service {service_name}: {e}")
            return False
    
    def enable_service(self, service_name: str, start_type: ServiceStartType = ServiceStartType.AUTOMATIC) -> bool:
        """Enable a Windows service"""
        try:
            if not self.has_admin_rights:
                self.logger.error("Admin privileges required to enable services")
                return False
            
            start_type_map = {
                ServiceStartType.AUTOMATIC: 'auto',
                ServiceStartType.MANUAL: 'demand',
                ServiceStartType.DISABLED: 'disabled',
                ServiceStartType.AUTOMATIC_DELAYED: 'delayed-auto'
            }
            
            start_param = start_type_map.get(start_type, 'auto')
            
            result = subprocess.run(['sc', 'config', service_name, 'start=', start_param], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"Service enabled: {service_name} ({start_type.value})")
                return True
            else:
                self.logger.error(f"Failed to enable service {service_name}: {result.stderr}")
                return False
            
        except Exception as e:
            self.logger.error(f"Error enabling service {service_name}: {e}")
            return False
    
    def get_service_list(self) -> List[Dict[str, Any]]:
        """Get list of all services"""
        return [asdict(service) for service in self.services.values()]
    
    def get_service_info(self, service_name: str) -> Optional[Dict[str, Any]]:
        """Get information about specific service"""
        service = self.services.get(service_name)
        return asdict(service) if service else None
    
    def get_suspicious_services(self) -> List[Dict[str, Any]]:
        """Get list of suspicious services"""
        return [asdict(service) for service in self.services.values() if service.is_suspicious]
    
    def get_running_services(self) -> List[Dict[str, Any]]:
        """Get list of running services"""
        return [asdict(service) for service in self.services.values() 
                if service.state == ServiceState.RUNNING]
    
    def get_service_alerts(self) -> List[Dict[str, Any]]:
        """Get service alerts"""
        return self.service_alerts
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service management status"""
        try:
            total_services = len(self.services)
            running_services = len([s for s in self.services.values() if s.state == ServiceState.RUNNING])
            suspicious_services = len([s for s in self.services.values() if s.is_suspicious])
            system_services = len([s for s in self.services.values() if s.is_system_service])
            
            return {
                'total_services': total_services,
                'running_services': running_services,
                'stopped_services': total_services - running_services,
                'suspicious_services': suspicious_services,
                'system_services': system_services,
                'user_services': total_services - system_services,
                'recent_alerts': len(self.service_alerts),
                'is_monitoring': self.is_monitoring,
                'has_admin_rights': self.has_admin_rights
            }
            
        except Exception as e:
            self.logger.error(f"Error getting service status: {e}")
            return {}
