#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Password Manager for Cyber Shield Pro
Handles password validation, strength checking, and generation
"""

import re
import secrets
import string
from typing import Dict, List, Tuple

class PasswordManager:
    """Password management and validation"""
    
    def __init__(self):
        """Initialize password manager"""
        self.min_length = 8
        self.max_length = 128
        
        # Common weak passwords
        self.weak_passwords = {
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            '1234567890', 'password1', '123123', 'admin123', 'root',
            'user', 'test', 'guest', 'login', 'pass', '12345678'
        }
        
        # Common patterns to avoid
        self.weak_patterns = [
            r'^(.)\1+$',  # All same character
            r'^\d+$',     # All digits
            r'^[a-zA-Z]+$',  # All letters
            r'(012|123|234|345|456|567|678|789|890)',  # Sequential digits
            r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',  # Sequential letters
        ]
    
    def is_password_strong(self, password: str) -> bool:
        """Check if password meets strength requirements"""
        if not password:
            return False
        
        # Length check
        if len(password) < self.min_length or len(password) > self.max_length:
            return False
        
        # Check against common weak passwords
        if password.lower() in self.weak_passwords:
            return False
        
        # Check against weak patterns
        for pattern in self.weak_patterns:
            if re.search(pattern, password.lower()):
                return False
        
        # Character type requirements
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_special = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password))
        
        # Require at least 3 of the 4 character types
        char_types = sum([has_lower, has_upper, has_digit, has_special])
        
        return char_types >= 3
    
    def get_password_strength(self, password: str) -> Dict[str, any]:
        """Get detailed password strength analysis"""
        if not password:
            return {
                'score': 0,
                'strength': 'Very Weak',
                'feedback': ['Password is empty'],
                'requirements': self._get_requirements_status(password)
            }
        
        score = 0
        feedback = []
        
        # Length scoring
        if len(password) >= self.min_length:
            score += 20
        else:
            feedback.append(f'Password should be at least {self.min_length} characters long')
        
        if len(password) >= 12:
            score += 10
        
        # Character type scoring
        has_lower = bool(re.search(r'[a-z]', password))
        has_upper = bool(re.search(r'[A-Z]', password))
        has_digit = bool(re.search(r'\d', password))
        has_special = bool(re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password))
        
        if has_lower:
            score += 15
        else:
            feedback.append('Add lowercase letters')
        
        if has_upper:
            score += 15
        else:
            feedback.append('Add uppercase letters')
        
        if has_digit:
            score += 15
        else:
            feedback.append('Add numbers')
        
        if has_special:
            score += 15
        else:
            feedback.append('Add special characters (!@#$%^&*)')
        
        # Complexity bonus
        unique_chars = len(set(password))
        if unique_chars >= len(password) * 0.7:
            score += 10
        
        # Penalty for common passwords
        if password.lower() in self.weak_passwords:
            score -= 30
            feedback.append('Avoid common passwords')
        
        # Penalty for patterns
        for pattern in self.weak_patterns:
            if re.search(pattern, password.lower()):
                score -= 20
                feedback.append('Avoid predictable patterns')
                break
        
        # Determine strength level
        if score >= 80:
            strength = 'Very Strong'
        elif score >= 60:
            strength = 'Strong'
        elif score >= 40:
            strength = 'Medium'
        elif score >= 20:
            strength = 'Weak'
        else:
            strength = 'Very Weak'
        
        return {
            'score': max(0, min(100, score)),
            'strength': strength,
            'feedback': feedback,
            'requirements': self._get_requirements_status(password)
        }
    
    def generate_password(self, length: int = 12, include_symbols: bool = True) -> str:
        """Generate a strong random password"""
        if length < self.min_length:
            length = self.min_length
        if length > self.max_length:
            length = self.max_length
        
        # Character sets
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?'
        
        # Ensure at least one character from each required set
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits)
        ]
        
        if include_symbols:
            password.append(secrets.choice(symbols))
            all_chars = lowercase + uppercase + digits + symbols
        else:
            all_chars = lowercase + uppercase + digits
        
        # Fill remaining length
        for _ in range(length - len(password)):
            password.append(secrets.choice(all_chars))
        
        # Shuffle the password
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    def generate_passphrase(self, word_count: int = 4) -> str:
        """Generate a passphrase using random words"""
        # Simple word list for demonstration
        words = [
            'apple', 'banana', 'cherry', 'dragon', 'elephant', 'forest',
            'guitar', 'house', 'island', 'jungle', 'kitchen', 'laptop',
            'mountain', 'notebook', 'ocean', 'piano', 'queen', 'river',
            'sunset', 'tiger', 'umbrella', 'village', 'window', 'yellow',
            'zebra', 'bridge', 'castle', 'diamond', 'engine', 'flower'
        ]
        
        selected_words = []
        for _ in range(word_count):
            word = secrets.choice(words)
            # Capitalize first letter
            word = word.capitalize()
            selected_words.append(word)
        
        # Add numbers and symbols
        passphrase = '-'.join(selected_words)
        passphrase += str(secrets.randbelow(100))
        passphrase += secrets.choice('!@#$%^&*')
        
        return passphrase
    
    def _get_requirements_status(self, password: str) -> Dict[str, bool]:
        """Get status of password requirements"""
        return {
            'min_length': len(password) >= self.min_length,
            'has_lowercase': bool(re.search(r'[a-z]', password)),
            'has_uppercase': bool(re.search(r'[A-Z]', password)),
            'has_digit': bool(re.search(r'\d', password)),
            'has_special': bool(re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]', password)),
            'not_common': password.lower() not in self.weak_passwords,
            'no_patterns': not any(re.search(pattern, password.lower()) for pattern in self.weak_patterns)
        }
    
    def suggest_improvements(self, password: str) -> List[str]:
        """Suggest improvements for password strength"""
        suggestions = []
        requirements = self._get_requirements_status(password)
        
        if not requirements['min_length']:
            suggestions.append(f'Make it at least {self.min_length} characters long')
        
        if not requirements['has_lowercase']:
            suggestions.append('Add lowercase letters (a-z)')
        
        if not requirements['has_uppercase']:
            suggestions.append('Add uppercase letters (A-Z)')
        
        if not requirements['has_digit']:
            suggestions.append('Add numbers (0-9)')
        
        if not requirements['has_special']:
            suggestions.append('Add special characters (!@#$%^&*)')
        
        if not requirements['not_common']:
            suggestions.append('Avoid common passwords')
        
        if not requirements['no_patterns']:
            suggestions.append('Avoid predictable patterns like 123 or abc')
        
        if len(password) >= self.min_length and len(set(password)) < len(password) * 0.7:
            suggestions.append('Use more unique characters')
        
        return suggestions
