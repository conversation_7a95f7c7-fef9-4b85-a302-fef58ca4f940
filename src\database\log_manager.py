#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Log Management for Cyber Shield Pro
Handles application logging and audit trails
"""

import datetime
import json
from typing import Optional, List, Dict, Any
from .db_manager import DatabaseManager

class LogManager:
    """Log management class"""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize log manager"""
        self.db = db_manager
    
    def add_log(self, user_id: int, log_level: str, category: str, 
                message: str, details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Add a new log entry"""
        try:
            details_json = json.dumps(details) if details else None
            
            query = '''
                INSERT INTO logs (user_id, log_level, category, message, details, ip_address, created_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            '''
            
            if self.db.execute_update(query, (user_id, log_level, category, 
                                            message, details_json, ip_address)):
                return self.db.get_last_insert_id()
            
            return None
            
        except Exception as e:
            print(f"Error adding log: {e}")
            return None
    
    def get_logs_by_user(self, user_id: int, limit: int = 100, 
                        log_level: str = None, category: str = None) -> List[Dict[str, Any]]:
        """Get logs for a specific user"""
        try:
            query = "SELECT * FROM logs WHERE user_id = ?"
            params = [user_id]
            
            if log_level:
                query += " AND log_level = ?"
                params.append(log_level)
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)
            
            result = self.db.execute_query(query, tuple(params))
            
            logs = []
            if result:
                for row in result:
                    log_dict = dict(row)
                    # Parse details JSON
                    if log_dict['details']:
                        try:
                            log_dict['details'] = json.loads(log_dict['details'])
                        except:
                            pass
                    logs.append(log_dict)
            
            return logs
            
        except Exception as e:
            print(f"Error getting logs by user: {e}")
            return []
    
    def get_all_logs(self, limit: int = 1000, log_level: str = None, 
                    category: str = None) -> List[Dict[str, Any]]:
        """Get all logs (admin only)"""
        try:
            query = '''
                SELECT l.*, u.username 
                FROM logs l
                LEFT JOIN users u ON l.user_id = u.id
                WHERE 1=1
            '''
            params = []
            
            if log_level:
                query += " AND l.log_level = ?"
                params.append(log_level)
            
            if category:
                query += " AND l.category = ?"
                params.append(category)
            
            query += " ORDER BY l.created_at DESC LIMIT ?"
            params.append(limit)
            
            result = self.db.execute_query(query, tuple(params))
            
            logs = []
            if result:
                for row in result:
                    log_dict = dict(row)
                    # Parse details JSON
                    if log_dict['details']:
                        try:
                            log_dict['details'] = json.loads(log_dict['details'])
                        except:
                            pass
                    logs.append(log_dict)
            
            return logs
            
        except Exception as e:
            print(f"Error getting all logs: {e}")
            return []
    
    def get_log_statistics(self, user_id: int = None) -> Dict[str, int]:
        """Get log statistics"""
        try:
            stats = {
                'total_logs': 0,
                'error_logs': 0,
                'warning_logs': 0,
                'info_logs': 0,
                'debug_logs': 0,
                'security_logs': 0,
                'system_logs': 0,
                'user_logs': 0
            }
            
            # Base query condition
            where_clause = "WHERE user_id = ?" if user_id else ""
            params = (user_id,) if user_id else ()
            
            # Total logs
            query = f"SELECT COUNT(*) as count FROM logs {where_clause}"
            result = self.db.execute_query(query, params)
            if result:
                stats['total_logs'] = result[0]['count']
            
            # Logs by level
            levels = ['ERROR', 'WARNING', 'INFO', 'DEBUG']
            for level in levels:
                query = f"SELECT COUNT(*) as count FROM logs {where_clause}"
                if where_clause:
                    query += " AND log_level = ?"
                    query_params = params + (level,)
                else:
                    query += "WHERE log_level = ?"
                    query_params = (level,)
                
                result = self.db.execute_query(query, query_params)
                if result:
                    stats[f'{level.lower()}_logs'] = result[0]['count']
            
            # Logs by category
            categories = ['security', 'system', 'user']
            for category in categories:
                query = f"SELECT COUNT(*) as count FROM logs {where_clause}"
                if where_clause:
                    query += " AND category = ?"
                    query_params = params + (category,)
                else:
                    query += "WHERE category = ?"
                    query_params = (category,)
                
                result = self.db.execute_query(query, query_params)
                if result:
                    stats[f'{category}_logs'] = result[0]['count']
            
            return stats
            
        except Exception as e:
            print(f"Error getting log statistics: {e}")
            return {}
    
    def cleanup_old_logs(self, days: int = 30) -> bool:
        """Clean up old log entries"""
        try:
            query = '''
                DELETE FROM logs 
                WHERE created_at < datetime('now', '-{} days')
            '''.format(days)
            
            return self.db.execute_update(query)
            
        except Exception as e:
            print(f"Error cleaning up old logs: {e}")
            return False
    
    def export_logs(self, user_id: int = None, start_date: str = None, 
                   end_date: str = None) -> List[Dict[str, Any]]:
        """Export logs for reporting"""
        try:
            query = '''
                SELECT l.*, u.username 
                FROM logs l
                LEFT JOIN users u ON l.user_id = u.id
                WHERE 1=1
            '''
            params = []
            
            if user_id:
                query += " AND l.user_id = ?"
                params.append(user_id)
            
            if start_date:
                query += " AND l.created_at >= ?"
                params.append(start_date)
            
            if end_date:
                query += " AND l.created_at <= ?"
                params.append(end_date)
            
            query += " ORDER BY l.created_at DESC"
            
            result = self.db.execute_query(query, tuple(params))
            
            logs = []
            if result:
                for row in result:
                    log_dict = dict(row)
                    # Parse details JSON
                    if log_dict['details']:
                        try:
                            log_dict['details'] = json.loads(log_dict['details'])
                        except:
                            pass
                    logs.append(log_dict)
            
            return logs
            
        except Exception as e:
            print(f"Error exporting logs: {e}")
            return []
    
    # Convenience methods for different log levels
    def log_info(self, user_id: int, category: str, message: str, 
                details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Log info message"""
        return self.add_log(user_id, 'INFO', category, message, details, ip_address)
    
    def log_warning(self, user_id: int, category: str, message: str, 
                   details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Log warning message"""
        return self.add_log(user_id, 'WARNING', category, message, details, ip_address)
    
    def log_error(self, user_id: int, category: str, message: str, 
                 details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Log error message"""
        return self.add_log(user_id, 'ERROR', category, message, details, ip_address)
    
    def log_debug(self, user_id: int, category: str, message: str, 
                 details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Log debug message"""
        return self.add_log(user_id, 'DEBUG', category, message, details, ip_address)
    
    def log_security_event(self, user_id: int, message: str, 
                          details: Dict[str, Any] = None, ip_address: str = None) -> Optional[int]:
        """Log security event"""
        return self.add_log(user_id, 'WARNING', 'security', message, details, ip_address)
