# Cyber Shield Pro - EXE Builder PowerShell Script
# تحويل البرنامج إلى ملف تنفيذي

param(
    [switch]$Clean = $false,
    [switch]$Verbose = $false
)

# Set console colors
$Host.UI.RawUI.BackgroundColor = "Black"
$Host.UI.RawUI.ForegroundColor = "Green"
Clear-Host

Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
Write-Host "║                 CYBER SHIELD PRO EXE BUILDER                 ║" -ForegroundColor Cyan
Write-Host "║              تحويل البرنامج إلى ملف تنفيذي                  ║" -ForegroundColor Cyan
Write-Host "║                 Developed in Palestine 🇵🇸                   ║" -ForegroundColor Cyan
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
Write-Host ""

# Function to check Python installation
function Test-PythonInstalled {
    $pythonCommands = @("python", "py", "python3")
    
    foreach ($cmd in $pythonCommands) {
        try {
            $version = & $cmd --version 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✓ تم العثور على Python: $version" -ForegroundColor Green
                return $cmd
            }
        } catch {
            continue
        }
    }
    
    return $null
}

# Function to install PyInstaller
function Install-PyInstaller {
    param($pythonCmd)
    
    Write-Host "📦 فحص PyInstaller..." -ForegroundColor Yellow
    
    try {
        & $pythonCmd -c "import PyInstaller" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ PyInstaller موجود" -ForegroundColor Green
            return $true
        }
    } catch {}
    
    Write-Host "⚠️  PyInstaller غير موجود. جاري التثبيت..." -ForegroundColor Yellow
    
    try {
        & $pythonCmd -m pip install pyinstaller
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم تثبيت PyInstaller بنجاح" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ فشل في تثبيت PyInstaller" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ خطأ في تثبيت PyInstaller: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create spec file
function New-SpecFile {
    $specContent = @"
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('assets', 'assets'),
        ('src', 'src'),
    ],
    hiddenimports=[
        'customtkinter',
        'PIL',
        'PIL._tkinter_finder',
        'requests',
        'psutil',
        'cryptography',
        'sqlite3',
        'hashlib',
        'secrets',
        'threading',
        'datetime',
        'json',
        'os',
        'sys',
        'pathlib',
        'typing',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CyberShieldPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
"@

    $specContent | Out-File -FilePath "cyber_shield.spec" -Encoding UTF8
    Write-Host "✓ تم إنشاء ملف التكوين" -ForegroundColor Green
}

# Function to build EXE
function Build-Executable {
    param($pythonCmd)
    
    Write-Host "🔨 بدء عملية بناء ملف EXE..." -ForegroundColor Yellow
    Write-Host "⏳ هذا قد يستغرق عدة دقائق..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        if ($Verbose) {
            & $pythonCmd -m PyInstaller --clean cyber_shield.spec
        } else {
            & $pythonCmd -m PyInstaller --clean cyber_shield.spec 2>$null
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ تم بناء ملف EXE بنجاح!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ فشل في بناء ملف EXE" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ خطأ في بناء ملف EXE: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create distribution package
function New-DistributionPackage {
    Write-Host "📦 إنشاء حزمة التوزيع..." -ForegroundColor Yellow
    
    $distDir = "CyberShieldPro_Distribution"
    
    # Remove existing distribution
    if (Test-Path $distDir) {
        Remove-Item $distDir -Recurse -Force
    }
    
    # Create distribution directory
    New-Item -ItemType Directory -Path $distDir | Out-Null
    
    # Copy EXE
    if (Test-Path "dist\CyberShieldPro.exe") {
        Copy-Item "dist\CyberShieldPro.exe" $distDir
        
        # Get file size
        $fileSize = (Get-Item "dist\CyberShieldPro.exe").Length
        $fileSizeMB = [math]::Round($fileSize / 1MB, 1)
        Write-Host "✓ حجم الملف: $fileSizeMB MB" -ForegroundColor Green
    } else {
        Write-Host "✗ لم يتم العثور على ملف EXE" -ForegroundColor Red
        return $false
    }
    
    # Copy documentation
    $docFiles = @("README.md", "QUICK_START_GUIDE.md", "INSTALLATION_GUIDE.md", "LICENSE", "CHANGELOG.md")
    foreach ($file in $docFiles) {
        if (Test-Path $file) {
            Copy-Item $file $distDir
        }
    }
    
    # Copy config directory
    if (Test-Path "config") {
        Copy-Item "config" "$distDir\config" -Recurse
    }
    
    # Create empty directories
    @("database", "logs", "temp", "assets") | ForEach-Object {
        New-Item -ItemType Directory -Path "$distDir\$_" -Force | Out-Null
    }
    
    # Create run batch file
    $runBatchContent = @"
@echo off
title Cyber Shield Pro
echo Starting Cyber Shield Pro...
echo.
echo Default Login:
echo Username: admin
echo Password: admin123
echo.
echo ⚠️  Change admin password after first login!
echo.
CyberShieldPro.exe
if %errorlevel% neq 0 (
    echo.
    echo Error starting application!
    pause
)
"@
    
    $runBatchContent | Out-File -FilePath "$distDir\RUN_CYBER_SHIELD.bat" -Encoding ASCII
    
    Write-Host "✓ تم إنشاء حزمة التوزيع: $distDir\" -ForegroundColor Green
    return $true
}

# Function to cleanup build files
function Remove-BuildFiles {
    Write-Host "🧹 تنظيف الملفات المؤقتة..." -ForegroundColor Yellow
    
    $itemsToRemove = @("build", "__pycache__", "cyber_shield.spec")
    
    foreach ($item in $itemsToRemove) {
        if (Test-Path $item) {
            Remove-Item $item -Recurse -Force
        }
    }
    
    Write-Host "✓ تم تنظيف الملفات المؤقتة" -ForegroundColor Green
}

# Main execution
try {
    # Check Python installation
    $pythonCmd = Test-PythonInstalled
    if (-not $pythonCmd) {
        Write-Host "✗ Python غير مثبت!" -ForegroundColor Red
        Write-Host ""
        Write-Host "يرجى تثبيت Python 3.8 أو أحدث من:" -ForegroundColor Yellow
        Write-Host "https://www.python.org/downloads/" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "مهم: تأكد من تفعيل 'Add Python to PATH' أثناء التثبيت!" -ForegroundColor Yellow
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # Install PyInstaller
    if (-not (Install-PyInstaller $pythonCmd)) {
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # Clean previous builds if requested
    if ($Clean) {
        Write-Host "🧹 تنظيف البناءات السابقة..." -ForegroundColor Yellow
        if (Test-Path "dist") { Remove-Item "dist" -Recurse -Force }
        if (Test-Path "build") { Remove-Item "build" -Recurse -Force }
    }
    
    # Create spec file
    New-SpecFile
    
    # Build executable
    if (-not (Build-Executable $pythonCmd)) {
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
    
    # Create distribution package
    if (New-DistributionPackage) {
        Write-Host ""
        Write-Host "┌─────────────────────────────────────────────────────────────┐" -ForegroundColor Green
        Write-Host "│  ✅ تم إنجاز عملية التحويل بنجاح!                          │" -ForegroundColor Green
        Write-Host "│                                                             │" -ForegroundColor Green
        Write-Host "│  📁 الملفات الجاهزة:                                       │" -ForegroundColor Green
        Write-Host "│  • CyberShieldPro.exe - الملف التنفيذي الرئيسي             │" -ForegroundColor Green
        Write-Host "│  • CyberShieldPro_Distribution\ - حزمة التوزيع الكاملة      │" -ForegroundColor Green
        Write-Host "│                                                             │" -ForegroundColor Green
        Write-Host "│  🚀 للتشغيل:                                               │" -ForegroundColor Green
        Write-Host "│  انقر نقراً مزدوجاً على CyberShieldPro.exe                │" -ForegroundColor Green
        Write-Host "│                                                             │" -ForegroundColor Green
        Write-Host "│  📋 معلومات الدخول الافتراضية:                            │" -ForegroundColor Green
        Write-Host "│  اسم المستخدم: admin                                       │" -ForegroundColor Green
        Write-Host "│  كلمة المرور: admin123                                     │" -ForegroundColor Green
        Write-Host "└─────────────────────────────────────────────────────────────┘" -ForegroundColor Green
    }
    
    # Cleanup
    Remove-BuildFiles
    
    # Ask to open distribution folder
    Write-Host ""
    $openFolder = Read-Host "هل تريد فتح مجلد التوزيع؟ (y/n)"
    if ($openFolder -eq "y" -or $openFolder -eq "Y") {
        if (Test-Path "CyberShieldPro_Distribution") {
            Invoke-Item "CyberShieldPro_Distribution"
        }
    }
    
} catch {
    Write-Host "✗ خطأ غير متوقع: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host ""
Write-Host "شكراً لاستخدام Cyber Shield Pro! 🛡️" -ForegroundColor Cyan
Read-Host "اضغط Enter للخروج"
