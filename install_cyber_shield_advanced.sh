#!/bin/bash

# Cyber Shield Pro Advanced - Installation Script
# This script sets up Cyber Shield Pro Advanced on Linux/macOS

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️${NC} $1"
}

print_error() {
    echo -e "${RED}❌${NC} $1"
}

print_info() {
    echo -e "${BLUE}ℹ️${NC} $1"
}

print_header() {
    echo
    echo "================================================================"
    echo -e "                ${PURPLE}🛡️  Cyber Shield Pro Advanced 🛡️${NC}"
    echo -e "                     ${CYAN}Installation Script${NC}"
    echo "================================================================"
    echo
}

# Detect OS
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_info "Detected OS: Linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_info "Detected OS: macOS"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Check if running as root (for Linux)
check_privileges() {
    if [[ "$OS" == "linux" ]]; then
        if [ "$EUID" -ne 0 ]; then
            print_warning "Not running as root"
            print_info "Some installation steps may require sudo privileges"
            echo
            read -p "Continue with installation? (y/n): " choice
            if [[ ! "$choice" =~ ^[Yy]$ ]]; then
                echo "Installation cancelled"
                exit 1
            fi
        else
            print_status "Running with root privileges"
        fi
    fi
}

# Check if Python is installed
check_python() {
    print_info "Checking Python installation..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
        print_status "Python 3 is installed"
        $PYTHON_CMD --version
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PIP_CMD="pip"
        python_version=$(python --version 2>&1)
        if [[ $python_version == *"Python 3"* ]]; then
            print_status "Python 3 is installed"
            echo $python_version
        else
            print_error "Python 3 is required but Python 2 is installed"
            install_python
            return
        fi
    else
        print_error "Python is not installed"
        install_python
        return
    fi
    
    # Check if pip is available
    if ! command -v $PIP_CMD &> /dev/null; then
        print_error "pip is not installed"
        install_pip
    else
        print_status "pip is available"
    fi
}

# Install Python
install_python() {
    print_info "Installing Python 3..."
    
    if [[ "$OS" == "linux" ]]; then
        # Detect Linux distribution
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y python3 python3-pip python3-venv python3-dev
        elif command -v yum &> /dev/null; then
            sudo yum install -y python3 python3-pip python3-devel
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y python3 python3-pip python3-devel
        elif command -v pacman &> /dev/null; then
            sudo pacman -S python python-pip
        else
            print_error "Could not detect package manager"
            print_info "Please install Python 3.8+ manually"
            exit 1
        fi
        
        PYTHON_CMD="python3"
        PIP_CMD="pip3"
        
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install python3
            PYTHON_CMD="python3"
            PIP_CMD="pip3"
        else
            print_error "Homebrew not found"
            print_info "Please install Python 3.8+ manually from https://python.org"
            print_info "Or install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
    fi
    
    if command -v $PYTHON_CMD &> /dev/null; then
        print_status "Python 3 installed successfully"
        $PYTHON_CMD --version
    else
        print_error "Failed to install Python 3"
        exit 1
    fi
}

# Install pip
install_pip() {
    print_info "Installing pip..."
    
    if [[ "$OS" == "linux" ]]; then
        if command -v apt-get &> /dev/null; then
            sudo apt-get install -y python3-pip
        elif command -v yum &> /dev/null; then
            sudo yum install -y python3-pip
        elif command -v dnf &> /dev/null; then
            sudo dnf install -y python3-pip
        fi
    elif [[ "$OS" == "macos" ]]; then
        $PYTHON_CMD -m ensurepip --upgrade
    fi
}

# Install system dependencies
install_system_dependencies() {
    print_info "Installing system dependencies..."
    
    if [[ "$OS" == "linux" ]]; then
        if command -v apt-get &> /dev/null; then
            sudo apt-get update
            sudo apt-get install -y \
                libpcap-dev \
                python3-dev \
                build-essential \
                libffi-dev \
                libssl-dev \
                libnotify-bin \
                curl \
                wget
        elif command -v yum &> /dev/null; then
            sudo yum groupinstall -y "Development Tools"
            sudo yum install -y \
                libpcap-devel \
                python3-devel \
                libffi-devel \
                openssl-devel \
                libnotify \
                curl \
                wget
        elif command -v dnf &> /dev/null; then
            sudo dnf groupinstall -y "Development Tools"
            sudo dnf install -y \
                libpcap-devel \
                python3-devel \
                libffi-devel \
                openssl-devel \
                libnotify \
                curl \
                wget
        elif command -v pacman &> /dev/null; then
            sudo pacman -S \
                libpcap \
                python \
                base-devel \
                libffi \
                openssl \
                libnotify \
                curl \
                wget
        fi
        
        print_status "System dependencies installed"
        
    elif [[ "$OS" == "macos" ]]; then
        if command -v brew &> /dev/null; then
            brew install libpcap
            print_status "System dependencies installed"
        else
            print_warning "Homebrew not found, some features may not work"
        fi
    fi
}

# Create virtual environment
create_virtual_environment() {
    print_info "Creating virtual environment..."
    
    if [ ! -d "venv" ]; then
        $PYTHON_CMD -m venv venv
        print_status "Virtual environment created"
    else
        print_status "Virtual environment already exists"
    fi
    
    # Activate virtual environment
    source venv/bin/activate
    print_status "Virtual environment activated"
    
    # Upgrade pip in virtual environment
    pip install --upgrade pip
}

# Install Python dependencies
install_python_dependencies() {
    print_info "Installing Python dependencies..."
    print_warning "This may take several minutes..."
    
    # Install essential dependencies first
    pip install wheel setuptools
    
    # Install main dependencies
    if pip install -r requirements_advanced.txt; then
        print_status "Dependencies installed successfully"
    else
        print_warning "Some dependencies failed to install"
        print_info "Trying to install essential dependencies only..."
        
        # Install essential dependencies
        pip install aiohttp aiosqlite psutil flask fastapi uvicorn
        
        if [ $? -eq 0 ]; then
            print_status "Essential dependencies installed"
            print_warning "Some optional features may not be available"
        else
            print_error "Failed to install essential dependencies"
            exit 1
        fi
    fi
}

# Create necessary directories
create_directories() {
    print_info "Creating application directories..."
    
    directories=("logs" "data" "temp" "reports" "quarantine" "config" "certs" "assets" "assets/icons" "assets/images")
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
        fi
    done
    
    print_status "Directories created"
}

# Set up firewall (Linux only)
setup_firewall() {
    if [[ "$OS" == "linux" ]]; then
        print_info "Setting up firewall rules..."
        
        # Check if ufw is available
        if command -v ufw &> /dev/null; then
            sudo ufw allow 8080/tcp comment "Cyber Shield Pro Advanced Dashboard"
            print_status "UFW firewall rule added for port 8080"
        elif command -v firewall-cmd &> /dev/null; then
            sudo firewall-cmd --permanent --add-port=8080/tcp
            sudo firewall-cmd --reload
            print_status "Firewalld rule added for port 8080"
        else
            print_warning "No supported firewall found"
            print_info "You may need to manually open port 8080"
        fi
    fi
}

# Create desktop shortcut (Linux only)
create_desktop_shortcut() {
    if [[ "$OS" == "linux" ]]; then
        print_info "Creating desktop shortcut..."
        
        desktop_dir="$HOME/Desktop"
        if [ ! -d "$desktop_dir" ]; then
            desktop_dir="$HOME/Рабочий стол"  # Russian
            if [ ! -d "$desktop_dir" ]; then
                desktop_dir="$HOME/Bureau"  # French
                if [ ! -d "$desktop_dir" ]; then
                    desktop_dir="$HOME/Escritorio"  # Spanish
                fi
            fi
        fi
        
        if [ -d "$desktop_dir" ]; then
            cat > "$desktop_dir/Cyber Shield Pro Advanced.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Cyber Shield Pro Advanced
Comment=Advanced Cybersecurity Protection Suite
Exec=$PWD/run_cyber_shield_advanced.sh
Icon=$PWD/assets/icons/cybershield.png
Terminal=true
StartupNotify=true
Categories=Security;System;
EOF
            
            chmod +x "$desktop_dir/Cyber Shield Pro Advanced.desktop"
            print_status "Desktop shortcut created"
        else
            print_warning "Desktop directory not found"
        fi
    fi
}

# Create application menu entry (Linux only)
create_menu_entry() {
    if [[ "$OS" == "linux" ]]; then
        print_info "Creating application menu entry..."
        
        applications_dir="$HOME/.local/share/applications"
        mkdir -p "$applications_dir"
        
        cat > "$applications_dir/cyber-shield-pro-advanced.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=Cyber Shield Pro Advanced
Comment=Advanced Cybersecurity Protection Suite
Exec=$PWD/run_cyber_shield_advanced.sh
Icon=$PWD/assets/icons/cybershield.png
Terminal=true
StartupNotify=true
Categories=Security;System;
EOF
        
        print_status "Application menu entry created"
    fi
}

# Test installation
test_installation() {
    print_info "Testing installation..."
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Test core modules
    if python -c "import aiohttp, aiosqlite, psutil; print('✅ Core modules imported successfully')" 2>/dev/null; then
        print_status "Installation test passed"
    else
        print_warning "Some modules may not be properly installed"
        print_info "The application might still work with limited functionality"
    fi
}

# Create uninstaller
create_uninstaller() {
    print_info "Creating uninstaller..."
    
    cat > uninstall_cyber_shield_advanced.sh << 'EOF'
#!/bin/bash

# Cyber Shield Pro Advanced - Uninstaller

echo "Uninstalling Cyber Shield Pro Advanced..."

# Remove virtual environment
if [ -d "venv" ]; then
    rm -rf venv
    echo "✅ Virtual environment removed"
fi

# Remove shortcuts (Linux)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    rm -f "$HOME/Desktop/Cyber Shield Pro Advanced.desktop" 2>/dev/null
    rm -f "$HOME/.local/share/applications/cyber-shield-pro-advanced.desktop" 2>/dev/null
    echo "✅ Shortcuts removed"
fi

# Remove firewall rules (Linux)
if command -v ufw &> /dev/null; then
    sudo ufw delete allow 8080/tcp 2>/dev/null
elif command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --remove-port=8080/tcp 2>/dev/null
    sudo firewall-cmd --reload 2>/dev/null
fi

echo "✅ Cyber Shield Pro Advanced uninstalled successfully"
echo "Note: Application files and data remain in the current directory"
EOF
    
    chmod +x uninstall_cyber_shield_advanced.sh
    print_status "Uninstaller created"
}

# Main installation function
main() {
    print_header
    
    detect_os
    check_privileges
    check_python
    install_system_dependencies
    create_virtual_environment
    install_python_dependencies
    create_directories
    setup_firewall
    create_desktop_shortcut
    create_menu_entry
    test_installation
    create_uninstaller
    
    echo
    echo "================================================================"
    echo -e "                    ${GREEN}🎉 INSTALLATION COMPLETE! 🎉${NC}"
    echo "================================================================"
    echo
    echo -e "${GREEN}✅${NC} Cyber Shield Pro Advanced has been installed successfully!"
    echo
    echo -e "${BLUE}🚀${NC} To start the application:"
    echo "    • Run: ./run_cyber_shield_advanced.sh"
    if [[ "$OS" == "linux" ]]; then
        echo "    • Use the desktop shortcut, or"
        echo "    • Find it in your application menu"
    fi
    echo
    echo -e "${BLUE}📊${NC} Dashboard will be available at: http://localhost:8080"
    echo -e "${BLUE}👤${NC} Default login credentials:"
    echo "    Username: admin"
    echo "    Password: CyberShield2025!"
    echo
    echo -e "${BLUE}📚${NC} Documentation:"
    echo "    • README_ADVANCED.md - Complete user guide"
    echo "    • config/cybershield_advanced.yaml - Configuration file"
    echo
    echo -e "${BLUE}🔧${NC} To uninstall: Run ./uninstall_cyber_shield_advanced.sh"
    echo
    echo -e "${YELLOW}⚠️  IMPORTANT SECURITY NOTES:${NC}"
    echo "    • Change default password after first login"
    echo "    • Review configuration settings"
    echo "    • Run with appropriate privileges for full functionality"
    echo
    echo "================================================================"
    echo
    
    # Ask if user wants to start the application now
    read -p "Would you like to start Cyber Shield Pro Advanced now? (y/n): " start_now
    if [[ "$start_now" =~ ^[Yy]$ ]]; then
        echo
        echo -e "${BLUE}🚀${NC} Starting Cyber Shield Pro Advanced..."
        ./run_cyber_shield_advanced.sh
    else
        echo
        echo -e "${GREEN}👋${NC} Installation complete. You can start the application anytime!"
    fi
}

# Make sure we're in the right directory
cd "$(dirname "$0")"

# Run main function
main "$@"
