#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
User Management for Cyber Shield Pro
Handles user authentication, registration, and profile management
"""

import hashlib
import os
import secrets
import datetime
from typing import Optional, Dict, Any
from .db_manager import DatabaseManager

class UserManager:
    """User management class"""
    
    def __init__(self, db_manager: DatabaseManager):
        """Initialize user manager"""
        self.db = db_manager
    
    def create_user(self, username: str, email: str, password: str, is_admin: bool = False) -> Optional[int]:
        """Create a new user account"""
        try:
            # Check if username or email already exists
            if self.get_user_by_username(username) or self.get_user_by_email(email):
                return None
            
            # Generate salt and hash password
            salt = secrets.token_hex(32)
            password_hash = self._hash_password(password, salt)
            
            # Insert user into database
            query = '''
                INSERT INTO users (username, email, password_hash, salt, is_admin, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            '''
            
            if self.db.execute_update(query, (username, email, password_hash, salt, is_admin, True)):
                return self.db.get_last_insert_id()
            
            return None
            
        except Exception as e:
            print(f"Error creating user: {e}")
            return None
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict[str, Any]]:
        """Authenticate user login"""
        try:
            user = self.get_user_by_username(username)
            if not user:
                return None
            
            # Check if user is active
            if not user['is_active']:
                return None
            
            # Verify password
            if self._verify_password(password, user['password_hash'], user['salt']):
                # Update last login
                self._update_last_login(user['id'])
                
                # Return user data (without sensitive info)
                return {
                    'id': user['id'],
                    'username': user['username'],
                    'email': user['email'],
                    'is_admin': user['is_admin'],
                    'two_factor_enabled': user['two_factor_enabled'],
                    'last_login': user['last_login']
                }
            
            return None
            
        except Exception as e:
            print(f"Authentication error: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by ID"""
        try:
            query = "SELECT * FROM users WHERE id = ?"
            result = self.db.execute_query(query, (user_id,))
            
            if result:
                return dict(result[0])
            return None
            
        except Exception as e:
            print(f"Error getting user by ID: {e}")
            return None
    
    def get_user_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user by username"""
        try:
            query = "SELECT * FROM users WHERE username = ?"
            result = self.db.execute_query(query, (username,))
            
            if result:
                return dict(result[0])
            return None
            
        except Exception as e:
            print(f"Error getting user by username: {e}")
            return None
    
    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email"""
        try:
            query = "SELECT * FROM users WHERE email = ?"
            result = self.db.execute_query(query, (email,))
            
            if result:
                return dict(result[0])
            return None
            
        except Exception as e:
            print(f"Error getting user by email: {e}")
            return None
    
    def update_password(self, user_id: int, new_password: str) -> bool:
        """Update user password"""
        try:
            # Generate new salt and hash
            salt = secrets.token_hex(32)
            password_hash = self._hash_password(new_password, salt)
            
            query = '''
                UPDATE users 
                SET password_hash = ?, salt = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (password_hash, salt, user_id))
            
        except Exception as e:
            print(f"Error updating password: {e}")
            return False
    
    def enable_two_factor(self, user_id: int, secret: str) -> bool:
        """Enable two-factor authentication"""
        try:
            query = '''
                UPDATE users 
                SET two_factor_enabled = TRUE, two_factor_secret = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (secret, user_id))
            
        except Exception as e:
            print(f"Error enabling 2FA: {e}")
            return False
    
    def disable_two_factor(self, user_id: int) -> bool:
        """Disable two-factor authentication"""
        try:
            query = '''
                UPDATE users 
                SET two_factor_enabled = FALSE, two_factor_secret = NULL, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (user_id,))
            
        except Exception as e:
            print(f"Error disabling 2FA: {e}")
            return False
    
    def deactivate_user(self, user_id: int) -> bool:
        """Deactivate user account"""
        try:
            query = '''
                UPDATE users 
                SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (user_id,))
            
        except Exception as e:
            print(f"Error deactivating user: {e}")
            return False
    
    def activate_user(self, user_id: int) -> bool:
        """Activate user account"""
        try:
            query = '''
                UPDATE users 
                SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            '''
            
            return self.db.execute_update(query, (user_id,))
            
        except Exception as e:
            print(f"Error activating user: {e}")
            return False
    
    def get_all_users(self) -> list:
        """Get all users (admin only)"""
        try:
            query = '''
                SELECT id, username, email, is_admin, is_active, 
                       two_factor_enabled, last_login, created_at
                FROM users
                ORDER BY created_at DESC
            '''
            
            result = self.db.execute_query(query)
            return [dict(row) for row in result] if result else []
            
        except Exception as e:
            print(f"Error getting all users: {e}")
            return []
    
    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt"""
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000).hex()
    
    def _verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """Verify password against stored hash"""
        return self._hash_password(password, salt) == stored_hash
    
    def _update_last_login(self, user_id: int):
        """Update user's last login timestamp"""
        try:
            query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
            self.db.execute_update(query, (user_id,))
        except Exception as e:
            print(f"Error updating last login: {e}")
