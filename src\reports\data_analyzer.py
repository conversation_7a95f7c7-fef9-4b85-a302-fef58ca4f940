#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Data Analyzer for Cyber Shield Pro
Advanced data analysis for security reports and insights
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict, Counter
import statistics
from dataclasses import dataclass

@dataclass
class ThreatAnalysis:
    """Threat analysis results"""
    total_threats: int
    critical_threats: int
    high_threats: int
    medium_threats: int
    low_threats: int
    resolved_threats: int
    active_threats: int
    threat_types: Dict[str, int]
    severity_breakdown: Dict[str, int]
    daily_trend: List[Dict[str, Any]]
    top_sources: List[Dict[str, Any]]
    attack_patterns: List[Dict[str, Any]]

@dataclass
class PerformanceAnalysis:
    """Performance analysis results"""
    avg_cpu_usage: float
    max_cpu_usage: float
    avg_memory_usage: float
    max_memory_usage: float
    avg_disk_usage: float
    max_disk_usage: float
    performance_score: float
    bottlenecks: List[str]
    trends: Dict[str, List[float]]
    recommendations: List[str]

class DataAnalyzer:
    """Advanced Data Analysis Engine"""
    
    def __init__(self):
        """Initialize data analyzer"""
        self.analysis_cache = {}
        
        # Analysis thresholds
        self.thresholds = {
            'cpu_high': 80.0,
            'memory_high': 80.0,
            'disk_high': 85.0,
            'threat_spike': 5.0,  # 5x normal rate
            'performance_good': 80.0
        }
    
    def analyze_threats(self, threats: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Comprehensive threat analysis"""
        try:
            if not threats:
                return self._empty_threat_analysis()
            
            # Basic counts
            total_threats = len(threats)
            
            # Severity analysis
            severity_counts = Counter(threat.get('severity', 'unknown') for threat in threats)
            critical_threats = severity_counts.get('critical', 0)
            high_threats = severity_counts.get('high', 0)
            medium_threats = severity_counts.get('medium', 0)
            low_threats = severity_counts.get('low', 0)
            
            # Status analysis
            status_counts = Counter(threat.get('status', 'unknown') for threat in threats)
            resolved_threats = status_counts.get('resolved', 0)
            active_threats = total_threats - resolved_threats
            
            # Threat type analysis
            threat_types = Counter(threat.get('type', 'unknown') for threat in threats)
            
            # Daily trend analysis
            daily_trend = self._analyze_daily_threat_trend(threats)
            
            # Top threat sources
            top_sources = self._analyze_threat_sources(threats)
            
            # Attack pattern analysis
            attack_patterns = self._analyze_attack_patterns(threats)
            
            return {
                'total_threats': total_threats,
                'critical_threats': critical_threats,
                'high_threats': high_threats,
                'medium_threats': medium_threats,
                'low_threats': low_threats,
                'resolved_threats': resolved_threats,
                'active_threats': active_threats,
                'threat_types': dict(threat_types),
                'severity_breakdown': dict(severity_counts),
                'daily_trend': daily_trend,
                'top_sources': top_sources,
                'attack_patterns': attack_patterns,
                'threat_resolution_rate': (resolved_threats / total_threats * 100) if total_threats > 0 else 0,
                'critical_threat_percentage': (critical_threats / total_threats * 100) if total_threats > 0 else 0
            }
            
        except Exception as e:
            return {'error': f'Error analyzing threats: {e}'}
    
    def analyze_threat_trends(self, threats: List[Dict[str, Any]], 
                            days: int = 30) -> Dict[str, Any]:
        """Analyze threat trends over time"""
        try:
            if not threats:
                return {'trends': [], 'analysis': 'No threat data available'}
            
            # Group threats by date
            threat_by_date = defaultdict(list)
            for threat in threats:
                threat_date = threat.get('timestamp', datetime.now())
                if isinstance(threat_date, str):
                    threat_date = datetime.fromisoformat(threat_date.replace('Z', '+00:00'))
                date_key = threat_date.date()
                threat_by_date[date_key].append(threat)
            
            # Calculate daily statistics
            daily_stats = []
            for date, day_threats in threat_by_date.items():
                severity_counts = Counter(t.get('severity', 'unknown') for t in day_threats)
                daily_stats.append({
                    'date': date,
                    'total': len(day_threats),
                    'critical': severity_counts.get('critical', 0),
                    'high': severity_counts.get('high', 0),
                    'medium': severity_counts.get('medium', 0),
                    'low': severity_counts.get('low', 0)
                })
            
            # Sort by date
            daily_stats.sort(key=lambda x: x['date'])
            
            # Trend analysis
            if len(daily_stats) >= 7:
                recent_avg = statistics.mean([day['total'] for day in daily_stats[-7:]])
                previous_avg = statistics.mean([day['total'] for day in daily_stats[-14:-7]]) if len(daily_stats) >= 14 else recent_avg
                
                trend_direction = 'increasing' if recent_avg > previous_avg * 1.1 else 'decreasing' if recent_avg < previous_avg * 0.9 else 'stable'
                trend_percentage = ((recent_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0
            else:
                trend_direction = 'insufficient_data'
                trend_percentage = 0
            
            return {
                'daily_stats': daily_stats,
                'trend_direction': trend_direction,
                'trend_percentage': trend_percentage,
                'peak_day': max(daily_stats, key=lambda x: x['total']) if daily_stats else None,
                'average_daily_threats': statistics.mean([day['total'] for day in daily_stats]) if daily_stats else 0
            }
            
        except Exception as e:
            return {'error': f'Error analyzing threat trends: {e}'}
    
    def analyze_threat_patterns(self, threats: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze attack patterns and behaviors"""
        try:
            if not threats:
                return {'patterns': [], 'analysis': 'No threat data available'}
            
            patterns = []
            
            # Time-based patterns
            hour_distribution = defaultdict(int)
            day_distribution = defaultdict(int)
            
            for threat in threats:
                timestamp = threat.get('timestamp', datetime.now())
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                
                hour_distribution[timestamp.hour] += 1
                day_distribution[timestamp.strftime('%A')] += 1
            
            # Find peak hours and days
            peak_hour = max(hour_distribution.items(), key=lambda x: x[1]) if hour_distribution else (0, 0)
            peak_day = max(day_distribution.items(), key=lambda x: x[1]) if day_distribution else ('Unknown', 0)
            
            patterns.append({
                'type': 'temporal',
                'description': f'Peak attack time: {peak_hour[0]:02d}:00 ({peak_hour[1]} attacks)',
                'data': dict(hour_distribution)
            })
            
            patterns.append({
                'type': 'weekly',
                'description': f'Peak attack day: {peak_day[0]} ({peak_day[1]} attacks)',
                'data': dict(day_distribution)
            })
            
            # Source-based patterns
            source_ips = Counter(threat.get('source_ip', 'unknown') for threat in threats)
            if source_ips:
                top_source = source_ips.most_common(1)[0]
                patterns.append({
                    'type': 'source',
                    'description': f'Top attacking IP: {top_source[0]} ({top_source[1]} attacks)',
                    'data': dict(source_ips.most_common(10))
                })
            
            # Attack type patterns
            attack_types = Counter(threat.get('type', 'unknown') for threat in threats)
            if attack_types:
                top_attack = attack_types.most_common(1)[0]
                patterns.append({
                    'type': 'attack_type',
                    'description': f'Most common attack: {top_attack[0]} ({top_attack[1]} occurrences)',
                    'data': dict(attack_types)
                })
            
            return {
                'patterns': patterns,
                'total_patterns': len(patterns),
                'analysis': f'Identified {len(patterns)} distinct attack patterns'
            }
            
        except Exception as e:
            return {'error': f'Error analyzing threat patterns: {e}'}
    
    def analyze_system_performance(self, performance_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze system performance data"""
        try:
            if not performance_data:
                return {'error': 'No performance data available'}
            
            # Extract metrics
            cpu_values = [d.get('cpu_percent', 0) for d in performance_data if 'cpu_percent' in d]
            memory_values = [d.get('memory_percent', 0) for d in performance_data if 'memory_percent' in d]
            disk_values = [d.get('disk_percent', 0) for d in performance_data if 'disk_percent' in d]
            
            # Calculate statistics
            analysis = {
                'cpu_analysis': self._analyze_metric(cpu_values, 'CPU'),
                'memory_analysis': self._analyze_metric(memory_values, 'Memory'),
                'disk_analysis': self._analyze_metric(disk_values, 'Disk'),
                'overall_score': 0,
                'bottlenecks': [],
                'recommendations': []
            }
            
            # Calculate overall performance score
            scores = []
            if cpu_values:
                cpu_score = max(0, 100 - statistics.mean(cpu_values))
                scores.append(cpu_score)
                if statistics.mean(cpu_values) > self.thresholds['cpu_high']:
                    analysis['bottlenecks'].append('High CPU usage detected')
                    analysis['recommendations'].append('Consider optimizing CPU-intensive processes')
            
            if memory_values:
                memory_score = max(0, 100 - statistics.mean(memory_values))
                scores.append(memory_score)
                if statistics.mean(memory_values) > self.thresholds['memory_high']:
                    analysis['bottlenecks'].append('High memory usage detected')
                    analysis['recommendations'].append('Consider adding more RAM or optimizing memory usage')
            
            if disk_values:
                disk_score = max(0, 100 - statistics.mean(disk_values))
                scores.append(disk_score)
                if statistics.mean(disk_values) > self.thresholds['disk_high']:
                    analysis['bottlenecks'].append('High disk usage detected')
                    analysis['recommendations'].append('Consider cleaning up disk space or adding storage')
            
            analysis['overall_score'] = statistics.mean(scores) if scores else 0
            
            # Performance trends
            analysis['trends'] = self._analyze_performance_trends(performance_data)
            
            return analysis
            
        except Exception as e:
            return {'error': f'Error analyzing system performance: {e}'}
    
    def analyze_network_activity(self, network_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze network activity patterns"""
        try:
            if not network_data:
                return {'error': 'No network data available'}
            
            # Connection analysis
            total_connections = len(network_data)
            
            # Protocol distribution
            protocols = Counter(conn.get('protocol', 'unknown') for conn in network_data)
            
            # Port analysis
            local_ports = Counter(conn.get('local_port', 0) for conn in network_data)
            remote_ports = Counter(conn.get('remote_port', 0) for conn in network_data)
            
            # Geographic analysis
            countries = Counter(conn.get('country', 'unknown') for conn in network_data)
            
            # Suspicious activity detection
            suspicious_connections = []
            for conn in network_data:
                if self._is_suspicious_connection(conn):
                    suspicious_connections.append(conn)
            
            return {
                'total_connections': total_connections,
                'protocol_distribution': dict(protocols),
                'top_local_ports': dict(local_ports.most_common(10)),
                'top_remote_ports': dict(remote_ports.most_common(10)),
                'country_distribution': dict(countries.most_common(10)),
                'suspicious_connections': len(suspicious_connections),
                'suspicious_percentage': (len(suspicious_connections) / total_connections * 100) if total_connections > 0 else 0,
                'analysis_summary': f'Analyzed {total_connections} connections, found {len(suspicious_connections)} suspicious'
            }
            
        except Exception as e:
            return {'error': f'Error analyzing network activity: {e}'}
    
    def _analyze_daily_threat_trend(self, threats: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze daily threat trends"""
        try:
            daily_counts = defaultdict(int)
            
            for threat in threats:
                timestamp = threat.get('timestamp', datetime.now())
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                date_key = timestamp.date()
                daily_counts[date_key] += 1
            
            # Convert to list and sort
            trend_data = []
            for date, count in sorted(daily_counts.items()):
                trend_data.append({
                    'date': date.isoformat(),
                    'count': count
                })
            
            return trend_data
            
        except Exception as e:
            return []
    
    def _analyze_threat_sources(self, threats: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze top threat sources"""
        try:
            source_counts = Counter()
            
            for threat in threats:
                source = threat.get('source_ip', 'unknown')
                if source != 'unknown':
                    source_counts[source] += 1
            
            top_sources = []
            for source, count in source_counts.most_common(10):
                top_sources.append({
                    'source': source,
                    'count': count,
                    'percentage': (count / len(threats) * 100) if threats else 0
                })
            
            return top_sources
            
        except Exception as e:
            return []
    
    def _analyze_attack_patterns(self, threats: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze attack patterns"""
        try:
            patterns = []
            
            # Group by attack type and analyze
            attack_types = defaultdict(list)
            for threat in threats:
                attack_type = threat.get('type', 'unknown')
                attack_types[attack_type].append(threat)
            
            for attack_type, type_threats in attack_types.items():
                if len(type_threats) >= 3:  # Only analyze if we have enough data
                    pattern = {
                        'attack_type': attack_type,
                        'count': len(type_threats),
                        'severity_distribution': dict(Counter(t.get('severity', 'unknown') for t in type_threats)),
                        'avg_per_day': len(type_threats) / 30,  # Assuming 30-day period
                        'description': f'{attack_type} attacks: {len(type_threats)} occurrences'
                    }
                    patterns.append(pattern)
            
            return patterns
            
        except Exception as e:
            return []
    
    def _analyze_metric(self, values: List[float], metric_name: str) -> Dict[str, Any]:
        """Analyze a performance metric"""
        try:
            if not values:
                return {'error': f'No {metric_name} data available'}
            
            return {
                'average': statistics.mean(values),
                'maximum': max(values),
                'minimum': min(values),
                'median': statistics.median(values),
                'std_deviation': statistics.stdev(values) if len(values) > 1 else 0,
                'samples': len(values),
                'trend': 'stable'  # Would implement trend analysis
            }
            
        except Exception as e:
            return {'error': f'Error analyzing {metric_name}: {e}'}
    
    def _analyze_performance_trends(self, performance_data: List[Dict[str, Any]]) -> Dict[str, List[float]]:
        """Analyze performance trends"""
        try:
            trends = {
                'cpu': [d.get('cpu_percent', 0) for d in performance_data[-24:] if 'cpu_percent' in d],
                'memory': [d.get('memory_percent', 0) for d in performance_data[-24:] if 'memory_percent' in d],
                'disk': [d.get('disk_percent', 0) for d in performance_data[-24:] if 'disk_percent' in d]
            }
            
            return trends
            
        except Exception as e:
            return {'cpu': [], 'memory': [], 'disk': []}
    
    def _is_suspicious_connection(self, connection: Dict[str, Any]) -> bool:
        """Check if a network connection is suspicious"""
        try:
            # Check for suspicious ports
            suspicious_ports = {4444, 5555, 6666, 7777, 8888, 9999, 12345, 54321, 31337}
            
            remote_port = connection.get('remote_port', 0)
            if remote_port in suspicious_ports:
                return True
            
            # Check for suspicious countries (this would be configurable)
            suspicious_countries = {'Unknown', 'Anonymous'}
            country = connection.get('country', '')
            if country in suspicious_countries:
                return True
            
            # Check for high frequency from same IP
            # This would require additional context about connection frequency
            
            return False
            
        except Exception as e:
            return False
    
    def _empty_threat_analysis(self) -> Dict[str, Any]:
        """Return empty threat analysis structure"""
        return {
            'total_threats': 0,
            'critical_threats': 0,
            'high_threats': 0,
            'medium_threats': 0,
            'low_threats': 0,
            'resolved_threats': 0,
            'active_threats': 0,
            'threat_types': {},
            'severity_breakdown': {},
            'daily_trend': [],
            'top_sources': [],
            'attack_patterns': [],
            'threat_resolution_rate': 0,
            'critical_threat_percentage': 0
        }
    
    def generate_insights(self, analysis_results: Dict[str, Any]) -> List[str]:
        """Generate actionable insights from analysis results"""
        insights = []
        
        try:
            # Threat insights
            if 'total_threats' in analysis_results:
                total = analysis_results['total_threats']
                critical = analysis_results.get('critical_threats', 0)
                
                if total == 0:
                    insights.append("✅ No threats detected during this period - excellent security posture")
                elif critical > 0:
                    insights.append(f"🚨 {critical} critical threats require immediate attention")
                elif total > 50:
                    insights.append(f"⚠️ High threat volume detected ({total} threats) - review security measures")
                else:
                    insights.append(f"ℹ️ {total} threats detected - normal security activity")
            
            # Performance insights
            if 'overall_score' in analysis_results:
                score = analysis_results['overall_score']
                if score > 90:
                    insights.append("🚀 Excellent system performance")
                elif score > 70:
                    insights.append("👍 Good system performance")
                elif score > 50:
                    insights.append("⚠️ System performance needs attention")
                else:
                    insights.append("🚨 Poor system performance - immediate action required")
            
            # Network insights
            if 'suspicious_percentage' in analysis_results:
                sus_pct = analysis_results['suspicious_percentage']
                if sus_pct > 10:
                    insights.append(f"🔍 High percentage of suspicious network activity ({sus_pct:.1f}%)")
                elif sus_pct > 5:
                    insights.append(f"⚠️ Moderate suspicious network activity detected ({sus_pct:.1f}%)")
            
            return insights
            
        except Exception as e:
            return [f"Error generating insights: {e}"]
    
    def calculate_security_score(self, threat_data: Dict[str, Any], 
                                performance_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Calculate overall security score"""
        try:
            score = 100
            factors = []
            
            # Threat factor
            total_threats = threat_data.get('total_threats', 0)
            critical_threats = threat_data.get('critical_threats', 0)
            
            if critical_threats > 0:
                threat_penalty = min(50, critical_threats * 10)
                score -= threat_penalty
                factors.append(f"Critical threats: -{threat_penalty}")
            
            if total_threats > 20:
                volume_penalty = min(20, (total_threats - 20) * 2)
                score -= volume_penalty
                factors.append(f"High threat volume: -{volume_penalty}")
            
            # Performance factor
            if performance_data and 'overall_score' in performance_data:
                perf_score = performance_data['overall_score']
                if perf_score < 70:
                    perf_penalty = (70 - perf_score) / 2
                    score -= perf_penalty
                    factors.append(f"Performance issues: -{perf_penalty:.1f}")
            
            # Ensure score doesn't go below 0
            score = max(0, score)
            
            # Determine grade
            if score >= 90:
                grade = 'A'
                status = 'Excellent'
            elif score >= 80:
                grade = 'B'
                status = 'Good'
            elif score >= 70:
                grade = 'C'
                status = 'Fair'
            elif score >= 60:
                grade = 'D'
                status = 'Poor'
            else:
                grade = 'F'
                status = 'Critical'
            
            return {
                'score': round(score, 1),
                'grade': grade,
                'status': status,
                'factors': factors,
                'recommendations': self._get_score_recommendations(score, factors)
            }
            
        except Exception as e:
            return {'error': f'Error calculating security score: {e}'}
    
    def _get_score_recommendations(self, score: float, factors: List[str]) -> List[str]:
        """Get recommendations based on security score"""
        recommendations = []
        
        if score < 60:
            recommendations.append("Immediate security review required")
            recommendations.append("Implement emergency response procedures")
        elif score < 80:
            recommendations.append("Review and strengthen security policies")
            recommendations.append("Increase monitoring frequency")
        
        if any('Critical threats' in factor for factor in factors):
            recommendations.append("Address all critical threats immediately")
        
        if any('Performance' in factor for factor in factors):
            recommendations.append("Optimize system performance to improve security")
        
        if any('threat volume' in factor for factor in factors):
            recommendations.append("Investigate cause of high threat volume")
        
        return recommendations
