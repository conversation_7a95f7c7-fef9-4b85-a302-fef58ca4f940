#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
File Scanner for Cyber Shield Pro
Scans individual files for threats and malware
"""

import os
import time
from typing import Dict, Any, List
from pathlib import Path
from .threat_detector import ThreatDetector

class FileScanner:
    """File scanning component"""
    
    def __init__(self, threat_detector: ThreatDetector):
        """Initialize file scanner"""
        self.threat_detector = threat_detector
        
        # Scan statistics
        self.total_files_scanned = 0
        self.total_threats_found = 0
        self.scan_start_time = None
        
        # File type priorities (higher number = higher priority)
        self.file_priorities = {
            '.exe': 10,
            '.dll': 9,
            '.scr': 9,
            '.bat': 8,
            '.cmd': 8,
            '.com': 8,
            '.pif': 8,
            '.vbs': 7,
            '.js': 7,
            '.jar': 7,
            '.zip': 6,
            '.rar': 6,
            '.7z': 6,
            '.doc': 5,
            '.docx': 5,
            '.xls': 5,
            '.xlsx': 5,
            '.ppt': 5,
            '.pptx': 5,
            '.pdf': 5,
            '.jpg': 3,
            '.jpeg': 3,
            '.png': 3,
            '.gif': 3,
            '.bmp': 3,
            '.mp3': 2,
            '.mp4': 2,
            '.avi': 2,
            '.mkv': 2,
            '.mov': 2
        }
        
        # Maximum file size to scan (in bytes)
        self.max_scan_size = 100 * 1024 * 1024  # 100MB
        
        # Scan timeout per file (in seconds)
        self.scan_timeout = 30
    
    def scan_file(self, file_path: str) -> Dict[str, Any]:
        """Scan a single file for threats"""
        result = {
            'file_path': file_path,
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'low',
            'confidence': 0,
            'description': '',
            'scan_time': 0,
            'file_size': 0,
            'file_type': '',
            'details': {}
        }
        
        scan_start = time.time()
        
        try:
            # Check if file exists and is accessible
            if not os.path.exists(file_path):
                result['details']['error'] = 'File not found'
                return result
            
            if not os.path.isfile(file_path):
                result['details']['error'] = 'Not a file'
                return result
            
            if not os.access(file_path, os.R_OK):
                result['details']['error'] = 'File not readable'
                return result
            
            # Get file information
            file_stat = os.stat(file_path)
            result['file_size'] = file_stat.st_size
            result['file_type'] = Path(file_path).suffix.lower()
            
            # Check file size limit
            if result['file_size'] > self.max_scan_size:
                result['details']['warning'] = f'File too large to scan ({result["file_size"]} bytes)'
                return result
            
            # Skip empty files
            if result['file_size'] == 0:
                result['details']['warning'] = 'Empty file'
                return result
            
            # Perform threat analysis
            threat_analysis = self.threat_detector.analyze_file(file_path)
            
            # Update result with threat analysis
            if threat_analysis['is_threat']:
                result.update(threat_analysis)
                self.total_threats_found += 1
            
            # Add scan metadata
            result['details'].update(threat_analysis.get('details', {}))
            
        except PermissionError:
            result['details']['error'] = 'Permission denied'
        except FileNotFoundError:
            result['details']['error'] = 'File not found during scan'
        except OSError as e:
            result['details']['error'] = f'OS error: {str(e)}'
        except Exception as e:
            result['details']['error'] = f'Scan error: {str(e)}'
        
        finally:
            result['scan_time'] = time.time() - scan_start
            self.total_files_scanned += 1
        
        return result
    
    def scan_multiple_files(self, file_paths: List[str], 
                           progress_callback=None) -> List[Dict[str, Any]]:
        """Scan multiple files"""
        results = []
        total_files = len(file_paths)
        
        # Sort files by priority (high-risk files first)
        sorted_files = self._sort_files_by_priority(file_paths)
        
        for i, file_path in enumerate(sorted_files):
            # Call progress callback if provided
            if progress_callback:
                progress = int((i / total_files) * 100) if total_files > 0 else 100
                progress_callback(progress, i + 1, total_files, file_path)
            
            # Scan file
            result = self.scan_file(file_path)
            results.append(result)
        
        return results
    
    def _sort_files_by_priority(self, file_paths: List[str]) -> List[str]:
        """Sort files by scanning priority"""
        def get_priority(file_path):
            ext = Path(file_path).suffix.lower()
            return self.file_priorities.get(ext, 1)
        
        return sorted(file_paths, key=get_priority, reverse=True)
    
    def quick_scan_file(self, file_path: str) -> Dict[str, Any]:
        """Perform a quick scan of a file (basic checks only)"""
        result = {
            'file_path': file_path,
            'is_threat': False,
            'threat_type': None,
            'threat_name': None,
            'severity': 'low',
            'confidence': 0,
            'description': '',
            'scan_time': 0,
            'scan_type': 'quick'
        }
        
        scan_start = time.time()
        
        try:
            # Basic file checks
            if not os.path.exists(file_path) or not os.path.isfile(file_path):
                return result
            
            file_size = os.path.getsize(file_path)
            if file_size == 0 or file_size > self.max_scan_size:
                return result
            
            # Quick threat detection (filename and basic patterns only)
            filename_result = self.threat_detector._check_filename_patterns(file_path)
            if filename_result['is_threat']:
                result.update(filename_result)
                return result
            
            # Quick hash check for known malware
            hash_result = self.threat_detector._check_hash_signature(file_path)
            if hash_result['is_threat']:
                result.update(hash_result)
                return result
            
        except Exception as e:
            result['details'] = {'error': str(e)}
        
        finally:
            result['scan_time'] = time.time() - scan_start
        
        return result
    
    def deep_scan_file(self, file_path: str) -> Dict[str, Any]:
        """Perform a deep scan of a file (comprehensive analysis)"""
        result = self.scan_file(file_path)
        result['scan_type'] = 'deep'
        
        try:
            # Additional deep scan checks
            if os.path.exists(file_path) and os.path.isfile(file_path):
                # Check for packed/compressed executables
                if Path(file_path).suffix.lower() in ['.exe', '.dll']:
                    packer_result = self._check_for_packers(file_path)
                    if packer_result['is_packed']:
                        result['details']['packer_info'] = packer_result
                        if not result['is_threat']:
                            result['is_threat'] = True
                            result['threat_type'] = 'suspicious'
                            result['threat_name'] = 'Packed Executable'
                            result['severity'] = 'medium'
                            result['description'] = 'Executable appears to be packed/compressed'
                
                # Check for suspicious imports (PE files)
                if Path(file_path).suffix.lower() in ['.exe', '.dll']:
                    imports_result = self._check_suspicious_imports(file_path)
                    if imports_result['is_suspicious']:
                        result['details']['imports_info'] = imports_result
                        if not result['is_threat']:
                            result['is_threat'] = True
                            result['threat_type'] = 'suspicious'
                            result['threat_name'] = 'Suspicious Imports'
                            result['severity'] = 'medium'
                            result['description'] = 'File imports suspicious functions'
        
        except Exception as e:
            if 'details' not in result:
                result['details'] = {}
            result['details']['deep_scan_error'] = str(e)
        
        return result
    
    def _check_for_packers(self, file_path: str) -> Dict[str, Any]:
        """Check if executable is packed/compressed"""
        result = {
            'is_packed': False,
            'packer_type': None,
            'entropy': 0
        }
        
        try:
            with open(file_path, 'rb') as f:
                # Read first 1KB for analysis
                data = f.read(1024)
            
            # Calculate entropy (packed files usually have high entropy)
            entropy = self._calculate_entropy(data)
            result['entropy'] = entropy
            
            # High entropy suggests packing/compression
            if entropy > 7.5:  # Threshold for packed files
                result['is_packed'] = True
                result['packer_type'] = 'Unknown Packer'
            
            # Check for common packer signatures
            packer_signatures = {
                b'UPX!': 'UPX',
                b'PECompact': 'PECompact',
                b'ASPack': 'ASPack',
                b'FSG': 'FSG'
            }
            
            for signature, packer_name in packer_signatures.items():
                if signature in data:
                    result['is_packed'] = True
                    result['packer_type'] = packer_name
                    break
        
        except Exception:
            pass
        
        return result
    
    def _check_suspicious_imports(self, file_path: str) -> Dict[str, Any]:
        """Check for suspicious API imports in PE files"""
        result = {
            'is_suspicious': False,
            'suspicious_imports': [],
            'import_count': 0
        }
        
        try:
            # Suspicious API functions
            suspicious_apis = [
                'CreateRemoteThread', 'WriteProcessMemory', 'VirtualAllocEx',
                'SetWindowsHookEx', 'GetAsyncKeyState', 'FindWindow',
                'RegCreateKeyEx', 'RegSetValueEx', 'CryptAcquireContext',
                'InternetOpen', 'InternetConnect', 'HttpSendRequest',
                'CreateFile', 'WriteFile', 'DeleteFile',
                'CreateProcess', 'TerminateProcess', 'OpenProcess'
            ]
            
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # Simple string search for API names
            found_apis = []
            for api in suspicious_apis:
                if api.encode() in content:
                    found_apis.append(api)
            
            result['suspicious_imports'] = found_apis
            result['import_count'] = len(found_apis)
            
            # Consider suspicious if many dangerous APIs are imported
            if len(found_apis) >= 5:
                result['is_suspicious'] = True
        
        except Exception:
            pass
        
        return result
    
    def _calculate_entropy(self, data: bytes) -> float:
        """Calculate Shannon entropy of data"""
        if not data:
            return 0
        
        # Count byte frequencies
        frequencies = [0] * 256
        for byte in data:
            frequencies[byte] += 1
        
        # Calculate entropy
        entropy = 0
        data_len = len(data)
        for freq in frequencies:
            if freq > 0:
                probability = freq / data_len
                entropy -= probability * (probability.bit_length() - 1)
        
        return entropy
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """Get scanning statistics"""
        return {
            'total_files_scanned': self.total_files_scanned,
            'total_threats_found': self.total_threats_found,
            'threat_detection_rate': (self.total_threats_found / max(self.total_files_scanned, 1)) * 100
        }
    
    def reset_statistics(self):
        """Reset scanning statistics"""
        self.total_files_scanned = 0
        self.total_threats_found = 0
        self.scan_start_time = None
